2025-06-11 11:18:39.973 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-11 11:18:39.990 [main] INFO  cn.pf.orch.Application - Starting Application using Java 1.8.0_401 on guoxiaoleideMacBook-Pro.local with PID 10945 (/Users/<USER>/IdeaProjects/workspace/pf-orch-meiye/pf-orch-meiye-service/target/classes started by gxl in /Users/<USER>/IdeaProjects/workspace/pf-orch-meiye)
2025-06-11 11:18:39.991 [main] INFO  cn.pf.orch.Application - The following 1 profile is active: "local"
2025-06-11 11:18:41.080 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 11:18:41.082 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 11:18:41.151 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 53 ms. Found 0 Redis repository interfaces.
2025-06-11 11:18:41.438 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=7d08a36b-0151-3f01-a4d2-1e2dacf5f21b
2025-06-11 11:18:42.148 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8102 (http)
2025-06-11 11:18:42.162 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8102"]
2025-06-11 11:18:42.163 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 11:18:42.163 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.75]
2025-06-11 11:18:42.267 [main] INFO  o.a.c.c.C.[.[localhost].[/api/meiye] - Initializing Spring embedded WebApplicationContext
2025-06-11 11:18:42.267 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2194 ms
2025-06-11 11:18:43.332 [main] INFO  org.redisson.Version - Redisson 3.17.6
2025-06-11 11:18:43.346 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-06-11 11:18:53.461 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'FSAppController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'feishuAppClient' defined in class path resource [cn/pf/orch/feishu/config/FeishuAutoConfig.class]: Unsatisfied dependency expressed through method 'feishuAppClient' parameter 2; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'stringRedisTemplate' defined in class path resource [cn/genn/spring/boot/starter/cache/RedisAutoConfiguration.class]: Unsatisfied dependency expressed through method 'stringRedisTemplate' parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redissonConnectionFactory' defined in class path resource [cn/genn/spring/boot/starter/cache/RedisAutoConfiguration.class]: Unsatisfied dependency expressed through method 'redissonConnectionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [cn/genn/spring/boot/starter/cache/RedisAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 200.20.5.6/200.20.5.6:6379
