2025-05-23 10:23:40.007 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-05-23 10:23:40.023 [main] INFO  cn.pf.orch.Application - Starting Application using Java 1.8.0_401 on guoxiaoleideMacBook-Pro.local with PID 77795 (/Users/<USER>/IdeaProjects/workspace/pf-orch-meiye/pf-orch-meiye-service/target/classes started by gxl in /Users/<USER>/IdeaProjects/workspace/pf-orch-meiye)
2025-05-23 10:23:40.023 [main] INFO  cn.pf.orch.Application - The following 1 profile is active: "local"
2025-05-23 10:23:40.870 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-23 10:23:40.871 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-23 10:23:40.896 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-05-23 10:23:41.117 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=f6c70ece-5277-35ed-9eb6-b79ca9147cb1
2025-05-23 10:23:41.523 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8102 (http)
2025-05-23 10:23:41.527 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8102"]
2025-05-23 10:23:41.528 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-23 10:23:41.528 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.75]
2025-05-23 10:23:41.633 [main] INFO  o.a.c.c.C.[.[localhost].[/api/meiye] - Initializing Spring embedded WebApplicationContext
2025-05-23 10:23:41.634 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1542 ms
2025-05-23 10:23:42.265 [main] INFO  org.redisson.Version - Redisson 3.17.6
2025-05-23 10:23:42.279 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-05-23 10:23:42.529 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 10 connections initialized for **********/**********:6379
2025-05-23 10:23:42.601 [redisson-netty-2-8] INFO  o.r.c.p.MasterPubSubConnectionPool - 10 connections initialized for **********/**********:6379
2025-05-23 10:23:42.879 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-23 10:23:43.083 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-23 10:23:43.850 [main] INFO  o.d.x.f.s.c.FileStorageServiceBuilder - 加载 MinIO 存储平台：minio-1
2025-05-23 10:23:44.771 [main] INFO  c.g.c.c.c.CaffeineMultiCacheManager - Create CaffeineCacheManager with names=[genn:cache:caffeine_manager], cacheSpec='maximumSize=10000,expireAfterWrite=300s', ttl=300
2025-05-23 10:23:44.866 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-05-23 10:23:45.389 [main] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-05-23 10:23:46.762 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-05-23 10:23:47.032 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8102"]
2025-05-23 10:23:47.039 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8102 (http) with context path '/api/meiye'
2025-05-23 10:23:48.046 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-05-23 10:23:48.048 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-05-23 10:23:48.060 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-05-23 10:23:48.092 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-05-23 10:23:48.207 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getFileUrlUsingPOST_1
2025-05-23 10:23:48.231 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_1
2025-05-23 10:23:48.242 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-05-23 10:23:48.251 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_2
2025-05-23 10:23:48.269 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_2
2025-05-23 10:23:48.270 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_3
2025-05-23 10:23:48.270 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: batchRemoveUsingPOST_1
2025-05-23 10:23:48.272 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: changeUsingPOST_1
2025-05-23 10:23:48.273 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2025-05-23 10:23:48.307 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_1
2025-05-23 10:23:48.311 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-05-23 10:23:48.317 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2025-05-23 10:23:48.320 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryUsingPOST_1
2025-05-23 10:23:48.325 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_2
2025-05-23 10:23:48.326 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingGET_1
2025-05-23 10:23:48.329 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2025-05-23 10:23:48.337 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_3
2025-05-23 10:23:48.338 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingGET_2
2025-05-23 10:23:48.340 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_3
2025-05-23 10:23:48.344 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryTreeRangeUsingPOST_1
2025-05-23 10:23:48.351 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_3
2025-05-23 10:23:48.356 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_4
2025-05-23 10:23:48.357 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: checkUsingPOST_1
2025-05-23 10:23:48.364 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_4
2025-05-23 10:23:48.370 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_5
2025-05-23 10:23:48.370 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: batchRemoveUsingPOST_2
2025-05-23 10:23:48.371 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPOST_1
2025-05-23 10:23:48.372 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sendApprovalUsingPOST_1
2025-05-23 10:23:48.373 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sendNotifyUsingPOST_1
2025-05-23 10:23:48.375 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: changeUsingPOST_2
2025-05-23 10:23:48.377 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2025-05-23 10:23:48.378 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: countUsingPOST_1
2025-05-23 10:23:48.383 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_5
2025-05-23 10:23:48.384 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_6
2025-05-23 10:23:48.384 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: batchRemoveUsingPOST_3
2025-05-23 10:23:48.386 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: changeUsingPOST_3
2025-05-23 10:23:48.387 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_3
2025-05-23 10:23:48.388 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingPOST_1
2025-05-23 10:23:48.392 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_6
2025-05-23 10:23:48.393 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_7
2025-05-23 10:23:48.393 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: batchRemoveUsingPOST_4
2025-05-23 10:23:48.394 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: changeUsingPOST_4
2025-05-23 10:23:48.396 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_4
2025-05-23 10:23:48.401 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_2
2025-05-23 10:23:48.403 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_7
2025-05-23 10:23:48.428 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_8
2025-05-23 10:23:48.428 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: batchRemoveUsingPOST_5
2025-05-23 10:23:48.429 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: changeUsingPOST_5
2025-05-23 10:23:48.431 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_5
2025-05-23 10:23:48.450 [main] INFO  cn.pf.orch.Application - Started Application in 9.706 seconds (JVM running for 10.411)
2025-05-23 10:23:48.725 [RMI TCP Connection(2)-127.0.0.1] INFO  o.a.c.c.C.[.[localhost].[/api/meiye] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-23 10:23:48.725 [RMI TCP Connection(2)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-23 10:23:48.726 [RMI TCP Connection(2)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-05-23 10:23:50.873 [SpringApplicationShutdownHook] INFO  o.d.x.f.s.core.FileStorageService - 销毁存储平台 minio-1 成功
2025-05-23 10:24:02.012 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-05-23 10:24:02.025 [main] INFO  cn.pf.orch.Application - Starting Application using Java 1.8.0_401 on guoxiaoleideMacBook-Pro.local with PID 77812 (/Users/<USER>/IdeaProjects/workspace/pf-orch-meiye/pf-orch-meiye-service/target/classes started by gxl in /Users/<USER>/IdeaProjects/workspace/pf-orch-meiye)
2025-05-23 10:24:02.025 [main] INFO  cn.pf.orch.Application - The following 1 profile is active: "dev"
2025-05-23 10:24:02.856 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-23 10:24:02.858 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-23 10:24:02.882 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-05-23 10:24:03.094 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=f6c70ece-5277-35ed-9eb6-b79ca9147cb1
2025-05-23 10:24:03.480 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8102 (http)
2025-05-23 10:24:03.484 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8102"]
2025-05-23 10:24:03.485 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-23 10:24:03.485 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.75]
2025-05-23 10:24:03.599 [main] INFO  o.a.c.c.C.[.[localhost].[/api/meiye] - Initializing Spring embedded WebApplicationContext
2025-05-23 10:24:03.599 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1518 ms
2025-05-23 10:24:04.237 [main] INFO  org.redisson.Version - Redisson 3.17.6
2025-05-23 10:24:04.252 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-05-23 10:24:04.512 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 10 connections initialized for **********/**********:6379
2025-05-23 10:24:04.578 [redisson-netty-2-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 10 connections initialized for **********/**********:6379
2025-05-23 10:24:04.866 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-05-23 10:24:05.047 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-05-23 10:24:05.834 [main] INFO  o.d.x.f.s.c.FileStorageServiceBuilder - 加载 MinIO 存储平台：minio-1
2025-05-23 10:24:06.759 [main] INFO  c.g.c.c.c.CaffeineMultiCacheManager - Create CaffeineCacheManager with names=[genn:cache:caffeine_manager], cacheSpec='maximumSize=10000,expireAfterWrite=300s', ttl=300
2025-05-23 10:24:06.859 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-05-23 10:24:07.383 [main] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-05-23 10:24:07.970 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8102"]
2025-05-23 10:24:07.977 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8102 (http) with context path '/api/meiye'
2025-05-23 10:24:07.979 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-05-23 10:24:07.983 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-05-23 10:24:08.003 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-05-23 10:24:08.143 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getFileUrlUsingPOST_1
2025-05-23 10:24:08.165 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_1
2025-05-23 10:24:08.174 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-05-23 10:24:08.185 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_2
2025-05-23 10:24:08.202 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_2
2025-05-23 10:24:08.203 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_3
2025-05-23 10:24:08.204 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: batchRemoveUsingPOST_1
2025-05-23 10:24:08.206 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: changeUsingPOST_1
2025-05-23 10:24:08.207 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2025-05-23 10:24:08.240 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_1
2025-05-23 10:24:08.243 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-05-23 10:24:08.249 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2025-05-23 10:24:08.252 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryUsingPOST_1
2025-05-23 10:24:08.257 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_2
2025-05-23 10:24:08.257 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingGET_1
2025-05-23 10:24:08.261 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2025-05-23 10:24:08.269 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_3
2025-05-23 10:24:08.270 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingGET_2
2025-05-23 10:24:08.271 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_3
2025-05-23 10:24:08.275 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryTreeRangeUsingPOST_1
2025-05-23 10:24:08.283 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_3
2025-05-23 10:24:08.288 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_4
2025-05-23 10:24:08.289 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: checkUsingPOST_1
2025-05-23 10:24:08.296 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_4
2025-05-23 10:24:08.302 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_5
2025-05-23 10:24:08.303 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: batchRemoveUsingPOST_2
2025-05-23 10:24:08.303 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPOST_1
2025-05-23 10:24:08.305 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sendApprovalUsingPOST_1
2025-05-23 10:24:08.305 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sendNotifyUsingPOST_1
2025-05-23 10:24:08.307 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: changeUsingPOST_2
2025-05-23 10:24:08.309 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2025-05-23 10:24:08.311 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: countUsingPOST_1
2025-05-23 10:24:08.316 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_5
2025-05-23 10:24:08.317 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_6
2025-05-23 10:24:08.317 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: batchRemoveUsingPOST_3
2025-05-23 10:24:08.318 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: changeUsingPOST_3
2025-05-23 10:24:08.320 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_3
2025-05-23 10:24:08.320 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingPOST_1
2025-05-23 10:24:08.325 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_6
2025-05-23 10:24:08.325 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_7
2025-05-23 10:24:08.326 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: batchRemoveUsingPOST_4
2025-05-23 10:24:08.327 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: changeUsingPOST_4
2025-05-23 10:24:08.329 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_4
2025-05-23 10:24:08.333 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_2
2025-05-23 10:24:08.336 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_7
2025-05-23 10:24:08.360 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_8
2025-05-23 10:24:08.361 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: batchRemoveUsingPOST_5
2025-05-23 10:24:08.362 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: changeUsingPOST_5
2025-05-23 10:24:08.364 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_5
2025-05-23 10:24:08.382 [main] INFO  cn.pf.orch.Application - Started Application in 7.674 seconds (JVM running for 8.197)
2025-05-23 10:24:08.514 [RMI TCP Connection(3)-127.0.0.1] INFO  o.a.c.c.C.[.[localhost].[/api/meiye] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-23 10:24:08.514 [RMI TCP Connection(3)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-23 10:24:08.516 [RMI TCP Connection(3)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-05-23 10:30:00.014 [scheduling-1] INFO  c.p.o.meiye.job.RefreshUserTokenTask - 定时任务:刷新user_token开始执行
2025-05-23 10:30:04.742 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzAwMWQzNGJjNzllNDc2Yjk1NjVhZjE2MDQyOWVjMmY无须刷新
2025-05-23 10:30:04.766 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzRkNjk1Y2NhZGU4NGM2OWI4MWVjNzdhZTQ5YWE0ZTA无须刷新
2025-05-23 10:30:04.797 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDI0NjZmZDk3MWYzNGNmOGIwM2E5NDQ0NDc1YThiNjk无须刷新
2025-05-23 10:30:04.825 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:N2JlZjdhNWYxYzg1NDQxY2JmYmY4OTQzYmQ1MzRkZTU无须刷新
2025-05-23 10:30:04.853 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MTZiZWJlZTRlNTFiNGM0MzgzMThlYzY5NGJlN2UxZmE无须刷新
2025-05-23 10:30:04.878 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OTc3NjI0OWQwYzlmNDUzNjk3OTRkY2EwZTI4MmQxNzc无须刷新
2025-05-23 10:30:04.913 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZjliNzFiYWMzMTQ2NDllMGFiNjczNzU0NmNmZTNkYTI无须刷新
2025-05-23 10:30:04.934 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:M2ZlNGNhZGU2ZTRiNGI1ZjgxNGNhMGRlNjM5M2ZlMmM无须刷新
2025-05-23 10:30:04.957 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzhkN2JkMmQ4YmIxNDhiMGEwMDJlN2IxOGExMmQ5YTQ无须刷新
2025-05-23 10:30:04.988 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Y2JjMzRjMTkxY2E4NGExN2FkYTYxMzEyNWE5YmFlZjE无须刷新
2025-05-23 10:30:05.016 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NGNjN2M3Yjc3MWExNDkyYjkyMGU1YjIwYmNjZTg5ODQ无须刷新
2025-05-23 10:30:05.042 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NmNmOWJjZjM0NTgzNDRmZTk1YWFkYzZhN2Q4NjAzNjk无须刷新
2025-05-23 10:30:05.070 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YjliMDk0ZTI2MzY2NGRjZmIyMjlmYWEwZTcxNjMxY2I无须刷新
2025-05-23 10:30:05.095 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzAyZmVlMWFmYTM2NDZkOGEyMTQ3Y2MyMzA1Y2EyYWE无须刷新
2025-05-23 10:30:05.127 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Mjg0MDhlYjVlZTkzNDg3M2E1N2ZjZGJjMWVlMTA2NTg无须刷新
2025-05-23 10:30:05.168 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDUzYTRkYzFhOGRlNDhhM2EyMDE3YWMzYjFiY2RlZWY无须刷新
2025-05-23 10:30:05.198 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NDQ4ZmQ4MzI0MTFlNDcxYzlkMWQ0MmUxM2ExNDUyZDY无须刷新
2025-05-23 10:30:05.236 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MjVkNDM2OGZiMmNlNDViZGJjN2MxODhjMzFiYTU0Mzg无须刷新
2025-05-23 10:30:05.266 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NmEzNWZhNzMwYjA1NGU5MGFkZjc0NzZkODZlNjAxMDc无须刷新
2025-05-23 10:30:05.293 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzNmNGRlNjhkZmU4NGNlMmFjZTc1NGE2ODBkY2M4N2I无须刷新
2025-05-23 10:30:05.335 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MzIyYTllYWNkOGZmNDg0NjkwZDA0NzU0N2JmNmI2MGU无须刷新
2025-05-23 10:30:05.360 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YTJkM2ZhYzQ5MDhmNDRhMDljNjI3MjRiNjY0Yzc3MWU无须刷新
2025-05-23 10:30:05.388 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YWE5YTBjMzA2YzFlNGEzMTgxZGZhZjBmN2QzMGFjYTQ无须刷新
2025-05-23 10:30:05.421 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YmFiMTc0NjcxMjY4NDBmNDkzNzc0OTQ3NmRjYzY3NjE无须刷新
2025-05-23 10:30:05.490 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Y2I0NmE0YmYyZjBiNDIzODg2NjU5NGEyMWJmZGYyOTY无须刷新
2025-05-23 10:30:05.521 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OTQ3M2E4ZWNkMDRkNDhkMjgyOTBiMTJmYTQ5YTg1MTQ无须刷新
2025-05-23 10:30:05.545 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MjdmOWQxNjk1ZGY1NDQzYmJjZThkYzY1N2I1OTNiNzI无须刷新
2025-05-23 10:30:05.569 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YmI5NzZlNWIyMTRhNDg4MGExZDA4MTdhZDIzMTc3NDk无须刷新
2025-05-23 10:30:05.599 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZmU2ZDU5NmIxNmE5NDBkNTlkMzI5OGI1YTM1Y2I0MzA无须刷新
2025-05-23 10:30:05.638 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzI4MGJkOWZjMGY2NDU0NjhmMDM3YzMyMjQzNTUyNDU无须刷新
2025-05-23 10:30:05.667 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MTc0ZWZhYWMzMWYwNGQxNTg0M2IyNzM2ZDMxNTM5ZjQ无须刷新
2025-05-23 10:30:05.701 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZjUzYzVmMTdmOGQyNGE4ZTgyZWFmMjliYmUwNmIzYTI无须刷新
2025-05-23 10:30:05.738 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDZhMmZkODg3N2NhNGU2Mzg0NjkyMzk1OWM4YTk0MDY无须刷新
2025-05-23 10:30:05.769 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MmY1NmRhMjAxZDU1NDlmOThkMGY5ZDM5OTE3ZDMzN2U无须刷新
2025-05-23 10:30:05.796 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NTQzODM1NjBiY2Y5NDMzMDhjMTVhMDRkNzg4YmUyNTY无须刷新
2025-05-23 10:30:05.823 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OWFhMjhmNjU2OGU0NGM1Nzk3NmE0ZmUxMDY0MGQyMWY无须刷新
2025-05-23 10:30:05.846 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzIyMjdjNDJlODIxNDFiM2IwMDI5MzU4ZDZlNTA2NzM无须刷新
2025-05-23 10:30:05.872 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ODQ4MDRkYWM4MDVhNGRhNGIyOGIxOGIyNjEzNzQ2ZWE无须刷新
2025-05-23 10:30:05.917 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NDU2OTViZTk3YWE4NDI1ZmFlNGIwOTAxZTYxODQxMmY无须刷新
2025-05-23 10:30:06.040 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Yzg0NmY4OTc1NGRlNDliM2FmZDg3ODg1NmNjZDg5ZmI无须刷新
2025-05-23 10:30:06.066 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Zjg5YTlhZDZlMzUyNDgzMWJjYzU1ZGRiOWFhMDUzNzg无须刷新
2025-05-23 10:30:06.090 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NjM5MWRmMGViYWE2NGVmYmIzYzMyY2VlMGI3Y2EwZmI无须刷新
2025-05-23 10:30:06.091 [scheduling-1] INFO  c.p.o.meiye.job.RefreshUserTokenTask - 定时任务:执行耗时:6s
2025-05-23 10:36:31.890 [http-nio-8102-exec-1] INFO  c.p.o.meiye.interfaces.SsoController - 授权码:cKykD52dc2BdAxHzGwF82fdf6KcAGaBB
2025-05-23 10:36:31.953 [http-nio-8102-exec-1] INFO  c.p.o.meiye.interfaces.SsoController - [logCost][getCode][47][127.0.0.1][/api/meiye/sso/web/getCode], args={"code":"cKykD52dc2BdAxHzGwF82fdf6KcAGaBB","state":""}, result={"success":true,"traceid":"","code":"*********","msg":"success","err":null,"data":"cKykD52dc2BdAxHzGwF82fdf6KcAGaBB","timestamp":1747967791900}
2025-05-23 10:40:00.022 [scheduling-1] INFO  c.p.o.meiye.job.RefreshUserTokenTask - 定时任务:刷新user_token开始执行
2025-05-23 10:40:04.880 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzAwMWQzNGJjNzllNDc2Yjk1NjVhZjE2MDQyOWVjMmY无须刷新
2025-05-23 10:40:04.907 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzRkNjk1Y2NhZGU4NGM2OWI4MWVjNzdhZTQ5YWE0ZTA无须刷新
2025-05-23 10:40:04.932 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDI0NjZmZDk3MWYzNGNmOGIwM2E5NDQ0NDc1YThiNjk无须刷新
2025-05-23 10:40:04.960 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:N2JlZjdhNWYxYzg1NDQxY2JmYmY4OTQzYmQ1MzRkZTU无须刷新
2025-05-23 10:40:04.986 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MTZiZWJlZTRlNTFiNGM0MzgzMThlYzY5NGJlN2UxZmE无须刷新
2025-05-23 10:40:05.021 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OTc3NjI0OWQwYzlmNDUzNjk3OTRkY2EwZTI4MmQxNzc无须刷新
2025-05-23 10:40:05.048 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZjliNzFiYWMzMTQ2NDllMGFiNjczNzU0NmNmZTNkYTI无须刷新
2025-05-23 10:40:05.086 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:M2ZlNGNhZGU2ZTRiNGI1ZjgxNGNhMGRlNjM5M2ZlMmM无须刷新
2025-05-23 10:40:05.115 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzhkN2JkMmQ4YmIxNDhiMGEwMDJlN2IxOGExMmQ5YTQ无须刷新
2025-05-23 10:40:05.145 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Y2JjMzRjMTkxY2E4NGExN2FkYTYxMzEyNWE5YmFlZjE无须刷新
2025-05-23 10:40:05.172 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NGNjN2M3Yjc3MWExNDkyYjkyMGU1YjIwYmNjZTg5ODQ无须刷新
2025-05-23 10:40:05.200 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NmNmOWJjZjM0NTgzNDRmZTk1YWFkYzZhN2Q4NjAzNjk无须刷新
2025-05-23 10:40:05.224 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YjliMDk0ZTI2MzY2NGRjZmIyMjlmYWEwZTcxNjMxY2I无须刷新
2025-05-23 10:40:05.256 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzAyZmVlMWFmYTM2NDZkOGEyMTQ3Y2MyMzA1Y2EyYWE无须刷新
2025-05-23 10:40:05.294 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Mjg0MDhlYjVlZTkzNDg3M2E1N2ZjZGJjMWVlMTA2NTg无须刷新
2025-05-23 10:40:05.322 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDUzYTRkYzFhOGRlNDhhM2EyMDE3YWMzYjFiY2RlZWY无须刷新
2025-05-23 10:40:05.345 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NDQ4ZmQ4MzI0MTFlNDcxYzlkMWQ0MmUxM2ExNDUyZDY无须刷新
2025-05-23 10:40:05.378 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MjVkNDM2OGZiMmNlNDViZGJjN2MxODhjMzFiYTU0Mzg无须刷新
2025-05-23 10:40:05.408 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NmEzNWZhNzMwYjA1NGU5MGFkZjc0NzZkODZlNjAxMDc无须刷新
2025-05-23 10:40:05.433 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzNmNGRlNjhkZmU4NGNlMmFjZTc1NGE2ODBkY2M4N2I无须刷新
2025-05-23 10:40:05.459 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MzIyYTllYWNkOGZmNDg0NjkwZDA0NzU0N2JmNmI2MGU无须刷新
2025-05-23 10:40:05.482 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YTJkM2ZhYzQ5MDhmNDRhMDljNjI3MjRiNjY0Yzc3MWU无须刷新
2025-05-23 10:40:05.508 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YWE5YTBjMzA2YzFlNGEzMTgxZGZhZjBmN2QzMGFjYTQ无须刷新
2025-05-23 10:40:05.534 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YmFiMTc0NjcxMjY4NDBmNDkzNzc0OTQ3NmRjYzY3NjE无须刷新
2025-05-23 10:40:05.560 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Y2I0NmE0YmYyZjBiNDIzODg2NjU5NGEyMWJmZGYyOTY无须刷新
2025-05-23 10:40:05.586 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OTQ3M2E4ZWNkMDRkNDhkMjgyOTBiMTJmYTQ5YTg1MTQ无须刷新
2025-05-23 10:40:05.614 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MjdmOWQxNjk1ZGY1NDQzYmJjZThkYzY1N2I1OTNiNzI无须刷新
2025-05-23 10:40:05.638 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YmI5NzZlNWIyMTRhNDg4MGExZDA4MTdhZDIzMTc3NDk无须刷新
2025-05-23 10:40:05.663 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZmU2ZDU5NmIxNmE5NDBkNTlkMzI5OGI1YTM1Y2I0MzA无须刷新
2025-05-23 10:40:05.692 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzI4MGJkOWZjMGY2NDU0NjhmMDM3YzMyMjQzNTUyNDU无须刷新
2025-05-23 10:40:05.717 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MTc0ZWZhYWMzMWYwNGQxNTg0M2IyNzM2ZDMxNTM5ZjQ无须刷新
2025-05-23 10:40:05.741 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZjUzYzVmMTdmOGQyNGE4ZTgyZWFmMjliYmUwNmIzYTI无须刷新
2025-05-23 10:40:05.767 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDZhMmZkODg3N2NhNGU2Mzg0NjkyMzk1OWM4YTk0MDY无须刷新
2025-05-23 10:40:05.798 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MmY1NmRhMjAxZDU1NDlmOThkMGY5ZDM5OTE3ZDMzN2U无须刷新
2025-05-23 10:40:05.824 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NTQzODM1NjBiY2Y5NDMzMDhjMTVhMDRkNzg4YmUyNTY无须刷新
2025-05-23 10:40:05.849 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OWFhMjhmNjU2OGU0NGM1Nzk3NmE0ZmUxMDY0MGQyMWY无须刷新
2025-05-23 10:40:05.876 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzIyMjdjNDJlODIxNDFiM2IwMDI5MzU4ZDZlNTA2NzM无须刷新
2025-05-23 10:40:05.908 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ODQ4MDRkYWM4MDVhNGRhNGIyOGIxOGIyNjEzNzQ2ZWE无须刷新
2025-05-23 10:40:05.933 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NDU2OTViZTk3YWE4NDI1ZmFlNGIwOTAxZTYxODQxMmY无须刷新
2025-05-23 10:40:05.956 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Yzg0NmY4OTc1NGRlNDliM2FmZDg3ODg1NmNjZDg5ZmI无须刷新
2025-05-23 10:40:05.983 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Zjg5YTlhZDZlMzUyNDgzMWJjYzU1ZGRiOWFhMDUzNzg无须刷新
2025-05-23 10:40:06.010 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NjM5MWRmMGViYWE2NGVmYmIzYzMyY2VlMGI3Y2EwZmI无须刷新
2025-05-23 10:40:06.010 [scheduling-1] INFO  c.p.o.meiye.job.RefreshUserTokenTask - 定时任务:执行耗时:5s
2025-05-23 10:50:00.024 [scheduling-1] INFO  c.p.o.meiye.job.RefreshUserTokenTask - 定时任务:刷新user_token开始执行
2025-05-23 10:50:05.253 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzAwMWQzNGJjNzllNDc2Yjk1NjVhZjE2MDQyOWVjMmY无须刷新
2025-05-23 10:50:05.276 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzRkNjk1Y2NhZGU4NGM2OWI4MWVjNzdhZTQ5YWE0ZTA无须刷新
2025-05-23 10:50:05.303 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDI0NjZmZDk3MWYzNGNmOGIwM2E5NDQ0NDc1YThiNjk无须刷新
2025-05-23 10:50:05.332 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:N2JlZjdhNWYxYzg1NDQxY2JmYmY4OTQzYmQ1MzRkZTU无须刷新
2025-05-23 10:50:05.361 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MTZiZWJlZTRlNTFiNGM0MzgzMThlYzY5NGJlN2UxZmE无须刷新
2025-05-23 10:50:05.394 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OTc3NjI0OWQwYzlmNDUzNjk3OTRkY2EwZTI4MmQxNzc无须刷新
2025-05-23 10:50:05.419 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZjliNzFiYWMzMTQ2NDllMGFiNjczNzU0NmNmZTNkYTI无须刷新
2025-05-23 10:50:05.444 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:M2ZlNGNhZGU2ZTRiNGI1ZjgxNGNhMGRlNjM5M2ZlMmM无须刷新
2025-05-23 10:50:05.471 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzhkN2JkMmQ4YmIxNDhiMGEwMDJlN2IxOGExMmQ5YTQ无须刷新
2025-05-23 10:50:05.494 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Y2JjMzRjMTkxY2E4NGExN2FkYTYxMzEyNWE5YmFlZjE无须刷新
2025-05-23 10:50:05.518 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NGNjN2M3Yjc3MWExNDkyYjkyMGU1YjIwYmNjZTg5ODQ无须刷新
2025-05-23 10:50:05.544 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NmNmOWJjZjM0NTgzNDRmZTk1YWFkYzZhN2Q4NjAzNjk无须刷新
2025-05-23 10:50:05.568 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YjliMDk0ZTI2MzY2NGRjZmIyMjlmYWEwZTcxNjMxY2I无须刷新
2025-05-23 10:50:05.593 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzAyZmVlMWFmYTM2NDZkOGEyMTQ3Y2MyMzA1Y2EyYWE无须刷新
2025-05-23 10:50:05.617 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Mjg0MDhlYjVlZTkzNDg3M2E1N2ZjZGJjMWVlMTA2NTg无须刷新
2025-05-23 10:50:05.639 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDUzYTRkYzFhOGRlNDhhM2EyMDE3YWMzYjFiY2RlZWY无须刷新
2025-05-23 10:50:05.666 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NDQ4ZmQ4MzI0MTFlNDcxYzlkMWQ0MmUxM2ExNDUyZDY无须刷新
2025-05-23 10:50:05.691 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MjVkNDM2OGZiMmNlNDViZGJjN2MxODhjMzFiYTU0Mzg无须刷新
2025-05-23 10:50:05.713 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NmEzNWZhNzMwYjA1NGU5MGFkZjc0NzZkODZlNjAxMDc无须刷新
2025-05-23 10:50:05.768 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzNmNGRlNjhkZmU4NGNlMmFjZTc1NGE2ODBkY2M4N2I无须刷新
2025-05-23 10:50:05.798 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MzIyYTllYWNkOGZmNDg0NjkwZDA0NzU0N2JmNmI2MGU无须刷新
2025-05-23 10:50:05.826 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YTJkM2ZhYzQ5MDhmNDRhMDljNjI3MjRiNjY0Yzc3MWU无须刷新
2025-05-23 10:50:05.854 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YWE5YTBjMzA2YzFlNGEzMTgxZGZhZjBmN2QzMGFjYTQ无须刷新
2025-05-23 10:50:05.885 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YmFiMTc0NjcxMjY4NDBmNDkzNzc0OTQ3NmRjYzY3NjE无须刷新
2025-05-23 10:50:05.919 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Y2I0NmE0YmYyZjBiNDIzODg2NjU5NGEyMWJmZGYyOTY无须刷新
2025-05-23 10:50:05.948 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OTQ3M2E4ZWNkMDRkNDhkMjgyOTBiMTJmYTQ5YTg1MTQ无须刷新
2025-05-23 10:50:05.975 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MjdmOWQxNjk1ZGY1NDQzYmJjZThkYzY1N2I1OTNiNzI无须刷新
2025-05-23 10:50:06.000 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YmI5NzZlNWIyMTRhNDg4MGExZDA4MTdhZDIzMTc3NDk无须刷新
2025-05-23 10:50:06.033 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZmU2ZDU5NmIxNmE5NDBkNTlkMzI5OGI1YTM1Y2I0MzA无须刷新
2025-05-23 10:50:06.062 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzI4MGJkOWZjMGY2NDU0NjhmMDM3YzMyMjQzNTUyNDU无须刷新
2025-05-23 10:50:06.098 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MTc0ZWZhYWMzMWYwNGQxNTg0M2IyNzM2ZDMxNTM5ZjQ无须刷新
2025-05-23 10:50:06.125 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZjUzYzVmMTdmOGQyNGE4ZTgyZWFmMjliYmUwNmIzYTI无须刷新
2025-05-23 10:50:06.151 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDZhMmZkODg3N2NhNGU2Mzg0NjkyMzk1OWM4YTk0MDY无须刷新
2025-05-23 10:50:06.174 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MmY1NmRhMjAxZDU1NDlmOThkMGY5ZDM5OTE3ZDMzN2U无须刷新
2025-05-23 10:50:06.199 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NTQzODM1NjBiY2Y5NDMzMDhjMTVhMDRkNzg4YmUyNTY无须刷新
2025-05-23 10:50:06.223 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OWFhMjhmNjU2OGU0NGM1Nzk3NmE0ZmUxMDY0MGQyMWY无须刷新
2025-05-23 10:50:06.251 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzIyMjdjNDJlODIxNDFiM2IwMDI5MzU4ZDZlNTA2NzM无须刷新
2025-05-23 10:50:06.278 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ODQ4MDRkYWM4MDVhNGRhNGIyOGIxOGIyNjEzNzQ2ZWE无须刷新
2025-05-23 10:50:06.304 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NDU2OTViZTk3YWE4NDI1ZmFlNGIwOTAxZTYxODQxMmY无须刷新
2025-05-23 10:50:06.331 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Yzg0NmY4OTc1NGRlNDliM2FmZDg3ODg1NmNjZDg5ZmI无须刷新
2025-05-23 10:50:06.362 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Zjg5YTlhZDZlMzUyNDgzMWJjYzU1ZGRiOWFhMDUzNzg无须刷新
2025-05-23 10:50:06.389 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NjM5MWRmMGViYWE2NGVmYmIzYzMyY2VlMGI3Y2EwZmI无须刷新
2025-05-23 10:50:06.389 [scheduling-1] INFO  c.p.o.meiye.job.RefreshUserTokenTask - 定时任务:执行耗时:6s
2025-05-23 11:00:00.014 [scheduling-1] INFO  c.p.o.meiye.job.RefreshUserTokenTask - 定时任务:刷新user_token开始执行
2025-05-23 11:00:04.710 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzAwMWQzNGJjNzllNDc2Yjk1NjVhZjE2MDQyOWVjMmY无须刷新
2025-05-23 11:00:04.740 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzRkNjk1Y2NhZGU4NGM2OWI4MWVjNzdhZTQ5YWE0ZTA无须刷新
2025-05-23 11:00:04.768 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDI0NjZmZDk3MWYzNGNmOGIwM2E5NDQ0NDc1YThiNjk无须刷新
2025-05-23 11:00:04.813 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:N2JlZjdhNWYxYzg1NDQxY2JmYmY4OTQzYmQ1MzRkZTU无须刷新
2025-05-23 11:00:04.841 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MTZiZWJlZTRlNTFiNGM0MzgzMThlYzY5NGJlN2UxZmE无须刷新
2025-05-23 11:00:04.872 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OTc3NjI0OWQwYzlmNDUzNjk3OTRkY2EwZTI4MmQxNzc无须刷新
2025-05-23 11:00:04.904 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZjliNzFiYWMzMTQ2NDllMGFiNjczNzU0NmNmZTNkYTI无须刷新
2025-05-23 11:00:04.938 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:M2ZlNGNhZGU2ZTRiNGI1ZjgxNGNhMGRlNjM5M2ZlMmM无须刷新
2025-05-23 11:00:04.967 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzhkN2JkMmQ4YmIxNDhiMGEwMDJlN2IxOGExMmQ5YTQ无须刷新
2025-05-23 11:00:04.995 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Y2JjMzRjMTkxY2E4NGExN2FkYTYxMzEyNWE5YmFlZjE无须刷新
2025-05-23 11:00:05.020 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NGNjN2M3Yjc3MWExNDkyYjkyMGU1YjIwYmNjZTg5ODQ无须刷新
2025-05-23 11:00:05.044 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NmNmOWJjZjM0NTgzNDRmZTk1YWFkYzZhN2Q4NjAzNjk无须刷新
2025-05-23 11:00:05.073 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YjliMDk0ZTI2MzY2NGRjZmIyMjlmYWEwZTcxNjMxY2I无须刷新
2025-05-23 11:00:05.097 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzAyZmVlMWFmYTM2NDZkOGEyMTQ3Y2MyMzA1Y2EyYWE无须刷新
2025-05-23 11:00:05.121 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Mjg0MDhlYjVlZTkzNDg3M2E1N2ZjZGJjMWVlMTA2NTg无须刷新
2025-05-23 11:00:05.163 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDUzYTRkYzFhOGRlNDhhM2EyMDE3YWMzYjFiY2RlZWY无须刷新
2025-05-23 11:00:05.190 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NDQ4ZmQ4MzI0MTFlNDcxYzlkMWQ0MmUxM2ExNDUyZDY无须刷新
2025-05-23 11:00:05.211 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MjVkNDM2OGZiMmNlNDViZGJjN2MxODhjMzFiYTU0Mzg无须刷新
2025-05-23 11:00:05.234 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NmEzNWZhNzMwYjA1NGU5MGFkZjc0NzZkODZlNjAxMDc无须刷新
2025-05-23 11:00:05.257 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzNmNGRlNjhkZmU4NGNlMmFjZTc1NGE2ODBkY2M4N2I无须刷新
2025-05-23 11:00:05.302 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MzIyYTllYWNkOGZmNDg0NjkwZDA0NzU0N2JmNmI2MGU无须刷新
2025-05-23 11:00:05.331 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YTJkM2ZhYzQ5MDhmNDRhMDljNjI3MjRiNjY0Yzc3MWU无须刷新
2025-05-23 11:00:05.363 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YWE5YTBjMzA2YzFlNGEzMTgxZGZhZjBmN2QzMGFjYTQ无须刷新
2025-05-23 11:00:05.392 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YmFiMTc0NjcxMjY4NDBmNDkzNzc0OTQ3NmRjYzY3NjE无须刷新
2025-05-23 11:00:05.415 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Y2I0NmE0YmYyZjBiNDIzODg2NjU5NGEyMWJmZGYyOTY无须刷新
2025-05-23 11:00:05.439 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OTQ3M2E4ZWNkMDRkNDhkMjgyOTBiMTJmYTQ5YTg1MTQ无须刷新
2025-05-23 11:00:05.464 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MjdmOWQxNjk1ZGY1NDQzYmJjZThkYzY1N2I1OTNiNzI无须刷新
2025-05-23 11:00:05.494 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YmI5NzZlNWIyMTRhNDg4MGExZDA4MTdhZDIzMTc3NDk无须刷新
2025-05-23 11:00:05.518 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZmU2ZDU5NmIxNmE5NDBkNTlkMzI5OGI1YTM1Y2I0MzA无须刷新
2025-05-23 11:00:05.544 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzI4MGJkOWZjMGY2NDU0NjhmMDM3YzMyMjQzNTUyNDU无须刷新
2025-05-23 11:00:05.572 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MTc0ZWZhYWMzMWYwNGQxNTg0M2IyNzM2ZDMxNTM5ZjQ无须刷新
2025-05-23 11:00:05.596 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZjUzYzVmMTdmOGQyNGE4ZTgyZWFmMjliYmUwNmIzYTI无须刷新
2025-05-23 11:00:05.628 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDZhMmZkODg3N2NhNGU2Mzg0NjkyMzk1OWM4YTk0MDY无须刷新
2025-05-23 11:00:05.656 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MmY1NmRhMjAxZDU1NDlmOThkMGY5ZDM5OTE3ZDMzN2U无须刷新
2025-05-23 11:00:05.686 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NTQzODM1NjBiY2Y5NDMzMDhjMTVhMDRkNzg4YmUyNTY无须刷新
2025-05-23 11:00:05.715 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OWFhMjhmNjU2OGU0NGM1Nzk3NmE0ZmUxMDY0MGQyMWY无须刷新
2025-05-23 11:00:05.743 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzIyMjdjNDJlODIxNDFiM2IwMDI5MzU4ZDZlNTA2NzM无须刷新
2025-05-23 11:00:05.775 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ODQ4MDRkYWM4MDVhNGRhNGIyOGIxOGIyNjEzNzQ2ZWE无须刷新
2025-05-23 11:00:05.801 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NDU2OTViZTk3YWE4NDI1ZmFlNGIwOTAxZTYxODQxMmY无须刷新
2025-05-23 11:00:05.825 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Yzg0NmY4OTc1NGRlNDliM2FmZDg3ODg1NmNjZDg5ZmI无须刷新
2025-05-23 11:00:05.856 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Zjg5YTlhZDZlMzUyNDgzMWJjYzU1ZGRiOWFhMDUzNzg无须刷新
2025-05-23 11:00:05.879 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NjM5MWRmMGViYWE2NGVmYmIzYzMyY2VlMGI3Y2EwZmI无须刷新
2025-05-23 11:00:05.879 [scheduling-1] INFO  c.p.o.meiye.job.RefreshUserTokenTask - 定时任务:执行耗时:5s
2025-05-23 11:01:55.605 [http-nio-8102-exec-5] INFO  c.p.o.meiye.interfaces.SsoController - 授权码:6ywlKb52feK1AFHIHEG0eHA40IaezGzB
2025-05-23 11:01:55.611 [http-nio-8102-exec-5] INFO  c.p.o.meiye.interfaces.SsoController - [logCost][getCode][7][127.0.0.1][/api/meiye/sso/web/getCode], args={"code":"6ywlKb52feK1AFHIHEG0eHA40IaezGzB","state":""}, result={"success":true,"traceid":"","code":"*********","msg":"success","err":null,"data":"6ywlKb52feK1AFHIHEG0eHA40IaezGzB","timestamp":1747969315608}
2025-05-23 11:10:00.004 [scheduling-1] INFO  c.p.o.meiye.job.RefreshUserTokenTask - 定时任务:刷新user_token开始执行
2025-05-23 11:10:05.095 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzAwMWQzNGJjNzllNDc2Yjk1NjVhZjE2MDQyOWVjMmY无须刷新
2025-05-23 11:10:05.131 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzRkNjk1Y2NhZGU4NGM2OWI4MWVjNzdhZTQ5YWE0ZTA无须刷新
2025-05-23 11:10:05.162 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDI0NjZmZDk3MWYzNGNmOGIwM2E5NDQ0NDc1YThiNjk无须刷新
2025-05-23 11:10:05.197 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:N2JlZjdhNWYxYzg1NDQxY2JmYmY4OTQzYmQ1MzRkZTU无须刷新
2025-05-23 11:10:05.229 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MTZiZWJlZTRlNTFiNGM0MzgzMThlYzY5NGJlN2UxZmE无须刷新
2025-05-23 11:10:05.276 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OTc3NjI0OWQwYzlmNDUzNjk3OTRkY2EwZTI4MmQxNzc无须刷新
2025-05-23 11:10:05.305 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZjliNzFiYWMzMTQ2NDllMGFiNjczNzU0NmNmZTNkYTI无须刷新
2025-05-23 11:10:05.333 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:M2ZlNGNhZGU2ZTRiNGI1ZjgxNGNhMGRlNjM5M2ZlMmM无须刷新
2025-05-23 11:10:05.375 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzhkN2JkMmQ4YmIxNDhiMGEwMDJlN2IxOGExMmQ5YTQ无须刷新
2025-05-23 11:10:05.403 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Y2JjMzRjMTkxY2E4NGExN2FkYTYxMzEyNWE5YmFlZjE无须刷新
2025-05-23 11:10:05.438 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NGNjN2M3Yjc3MWExNDkyYjkyMGU1YjIwYmNjZTg5ODQ无须刷新
2025-05-23 11:10:05.469 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NmNmOWJjZjM0NTgzNDRmZTk1YWFkYzZhN2Q4NjAzNjk无须刷新
2025-05-23 11:10:05.498 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YjliMDk0ZTI2MzY2NGRjZmIyMjlmYWEwZTcxNjMxY2I无须刷新
2025-05-23 11:10:05.522 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzAyZmVlMWFmYTM2NDZkOGEyMTQ3Y2MyMzA1Y2EyYWE无须刷新
2025-05-23 11:10:05.556 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Mjg0MDhlYjVlZTkzNDg3M2E1N2ZjZGJjMWVlMTA2NTg无须刷新
2025-05-23 11:10:05.587 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDUzYTRkYzFhOGRlNDhhM2EyMDE3YWMzYjFiY2RlZWY无须刷新
2025-05-23 11:10:05.615 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NDQ4ZmQ4MzI0MTFlNDcxYzlkMWQ0MmUxM2ExNDUyZDY无须刷新
2025-05-23 11:10:05.641 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MjVkNDM2OGZiMmNlNDViZGJjN2MxODhjMzFiYTU0Mzg无须刷新
2025-05-23 11:10:05.665 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NmEzNWZhNzMwYjA1NGU5MGFkZjc0NzZkODZlNjAxMDc无须刷新
2025-05-23 11:10:05.694 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzNmNGRlNjhkZmU4NGNlMmFjZTc1NGE2ODBkY2M4N2I无须刷新
2025-05-23 11:10:05.720 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MzIyYTllYWNkOGZmNDg0NjkwZDA0NzU0N2JmNmI2MGU无须刷新
2025-05-23 11:10:05.745 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YTJkM2ZhYzQ5MDhmNDRhMDljNjI3MjRiNjY0Yzc3MWU无须刷新
2025-05-23 11:10:05.775 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YWE5YTBjMzA2YzFlNGEzMTgxZGZhZjBmN2QzMGFjYTQ无须刷新
2025-05-23 11:10:05.799 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YmFiMTc0NjcxMjY4NDBmNDkzNzc0OTQ3NmRjYzY3NjE无须刷新
2025-05-23 11:10:05.823 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Y2I0NmE0YmYyZjBiNDIzODg2NjU5NGEyMWJmZGYyOTY无须刷新
2025-05-23 11:10:05.854 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OTQ3M2E4ZWNkMDRkNDhkMjgyOTBiMTJmYTQ5YTg1MTQ无须刷新
2025-05-23 11:10:05.885 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MjdmOWQxNjk1ZGY1NDQzYmJjZThkYzY1N2I1OTNiNzI无须刷新
2025-05-23 11:10:05.916 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YmI5NzZlNWIyMTRhNDg4MGExZDA4MTdhZDIzMTc3NDk无须刷新
2025-05-23 11:10:05.943 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZmU2ZDU5NmIxNmE5NDBkNTlkMzI5OGI1YTM1Y2I0MzA无须刷新
2025-05-23 11:10:05.982 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzI4MGJkOWZjMGY2NDU0NjhmMDM3YzMyMjQzNTUyNDU无须刷新
2025-05-23 11:10:06.011 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MTc0ZWZhYWMzMWYwNGQxNTg0M2IyNzM2ZDMxNTM5ZjQ无须刷新
2025-05-23 11:10:06.037 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZjUzYzVmMTdmOGQyNGE4ZTgyZWFmMjliYmUwNmIzYTI无须刷新
2025-05-23 11:10:06.071 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDZhMmZkODg3N2NhNGU2Mzg0NjkyMzk1OWM4YTk0MDY无须刷新
2025-05-23 11:10:06.106 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MmY1NmRhMjAxZDU1NDlmOThkMGY5ZDM5OTE3ZDMzN2U无须刷新
2025-05-23 11:10:06.130 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NTQzODM1NjBiY2Y5NDMzMDhjMTVhMDRkNzg4YmUyNTY无须刷新
2025-05-23 11:10:06.155 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OWFhMjhmNjU2OGU0NGM1Nzk3NmE0ZmUxMDY0MGQyMWY无须刷新
2025-05-23 11:10:06.188 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzIyMjdjNDJlODIxNDFiM2IwMDI5MzU4ZDZlNTA2NzM无须刷新
2025-05-23 11:10:06.210 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ODQ4MDRkYWM4MDVhNGRhNGIyOGIxOGIyNjEzNzQ2ZWE无须刷新
2025-05-23 11:10:06.236 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NDU2OTViZTk3YWE4NDI1ZmFlNGIwOTAxZTYxODQxMmY无须刷新
2025-05-23 11:10:06.261 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Yzg0NmY4OTc1NGRlNDliM2FmZDg3ODg1NmNjZDg5ZmI无须刷新
2025-05-23 11:10:06.294 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Zjg5YTlhZDZlMzUyNDgzMWJjYzU1ZGRiOWFhMDUzNzg无须刷新
2025-05-23 11:10:06.320 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NjM5MWRmMGViYWE2NGVmYmIzYzMyY2VlMGI3Y2EwZmI无须刷新
2025-05-23 11:10:06.321 [scheduling-1] INFO  c.p.o.meiye.job.RefreshUserTokenTask - 定时任务:执行耗时:6s
2025-05-23 11:20:00.016 [scheduling-1] INFO  c.p.o.meiye.job.RefreshUserTokenTask - 定时任务:刷新user_token开始执行
2025-05-23 11:20:05.859 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzAwMWQzNGJjNzllNDc2Yjk1NjVhZjE2MDQyOWVjMmY无须刷新
2025-05-23 11:20:05.885 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzRkNjk1Y2NhZGU4NGM2OWI4MWVjNzdhZTQ5YWE0ZTA无须刷新
2025-05-23 11:20:05.923 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDI0NjZmZDk3MWYzNGNmOGIwM2E5NDQ0NDc1YThiNjk无须刷新
2025-05-23 11:20:05.960 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:N2JlZjdhNWYxYzg1NDQxY2JmYmY4OTQzYmQ1MzRkZTU无须刷新
2025-05-23 11:20:05.988 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MTZiZWJlZTRlNTFiNGM0MzgzMThlYzY5NGJlN2UxZmE无须刷新
2025-05-23 11:20:06.012 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OTc3NjI0OWQwYzlmNDUzNjk3OTRkY2EwZTI4MmQxNzc无须刷新
2025-05-23 11:20:06.055 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZjliNzFiYWMzMTQ2NDllMGFiNjczNzU0NmNmZTNkYTI无须刷新
2025-05-23 11:20:06.098 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:M2ZlNGNhZGU2ZTRiNGI1ZjgxNGNhMGRlNjM5M2ZlMmM无须刷新
2025-05-23 11:20:06.131 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzhkN2JkMmQ4YmIxNDhiMGEwMDJlN2IxOGExMmQ5YTQ无须刷新
2025-05-23 11:20:06.168 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Y2JjMzRjMTkxY2E4NGExN2FkYTYxMzEyNWE5YmFlZjE无须刷新
2025-05-23 11:20:06.197 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NGNjN2M3Yjc3MWExNDkyYjkyMGU1YjIwYmNjZTg5ODQ无须刷新
2025-05-23 11:20:06.220 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NmNmOWJjZjM0NTgzNDRmZTk1YWFkYzZhN2Q4NjAzNjk无须刷新
2025-05-23 11:20:06.254 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YjliMDk0ZTI2MzY2NGRjZmIyMjlmYWEwZTcxNjMxY2I无须刷新
2025-05-23 11:20:06.279 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzAyZmVlMWFmYTM2NDZkOGEyMTQ3Y2MyMzA1Y2EyYWE无须刷新
2025-05-23 11:20:06.304 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Mjg0MDhlYjVlZTkzNDg3M2E1N2ZjZGJjMWVlMTA2NTg无须刷新
2025-05-23 11:20:06.383 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDUzYTRkYzFhOGRlNDhhM2EyMDE3YWMzYjFiY2RlZWY无须刷新
2025-05-23 11:20:06.409 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NDQ4ZmQ4MzI0MTFlNDcxYzlkMWQ0MmUxM2ExNDUyZDY无须刷新
2025-05-23 11:20:06.434 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MjVkNDM2OGZiMmNlNDViZGJjN2MxODhjMzFiYTU0Mzg无须刷新
2025-05-23 11:20:06.465 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NmEzNWZhNzMwYjA1NGU5MGFkZjc0NzZkODZlNjAxMDc无须刷新
2025-05-23 11:20:06.492 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzNmNGRlNjhkZmU4NGNlMmFjZTc1NGE2ODBkY2M4N2I无须刷新
2025-05-23 11:20:06.513 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MzIyYTllYWNkOGZmNDg0NjkwZDA0NzU0N2JmNmI2MGU无须刷新
2025-05-23 11:20:06.538 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YTJkM2ZhYzQ5MDhmNDRhMDljNjI3MjRiNjY0Yzc3MWU无须刷新
2025-05-23 11:20:06.569 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YWE5YTBjMzA2YzFlNGEzMTgxZGZhZjBmN2QzMGFjYTQ无须刷新
2025-05-23 11:20:06.595 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YmFiMTc0NjcxMjY4NDBmNDkzNzc0OTQ3NmRjYzY3NjE无须刷新
2025-05-23 11:20:06.617 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Y2I0NmE0YmYyZjBiNDIzODg2NjU5NGEyMWJmZGYyOTY无须刷新
2025-05-23 11:20:06.644 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OTQ3M2E4ZWNkMDRkNDhkMjgyOTBiMTJmYTQ5YTg1MTQ无须刷新
2025-05-23 11:20:06.668 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MjdmOWQxNjk1ZGY1NDQzYmJjZThkYzY1N2I1OTNiNzI无须刷新
2025-05-23 11:20:06.695 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YmI5NzZlNWIyMTRhNDg4MGExZDA4MTdhZDIzMTc3NDk无须刷新
2025-05-23 11:20:06.720 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZmU2ZDU5NmIxNmE5NDBkNTlkMzI5OGI1YTM1Y2I0MzA无须刷新
2025-05-23 11:20:06.748 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzI4MGJkOWZjMGY2NDU0NjhmMDM3YzMyMjQzNTUyNDU无须刷新
2025-05-23 11:20:06.774 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MTc0ZWZhYWMzMWYwNGQxNTg0M2IyNzM2ZDMxNTM5ZjQ无须刷新
2025-05-23 11:20:06.810 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZjUzYzVmMTdmOGQyNGE4ZTgyZWFmMjliYmUwNmIzYTI无须刷新
2025-05-23 11:20:06.840 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDZhMmZkODg3N2NhNGU2Mzg0NjkyMzk1OWM4YTk0MDY无须刷新
2025-05-23 11:20:06.880 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MmY1NmRhMjAxZDU1NDlmOThkMGY5ZDM5OTE3ZDMzN2U无须刷新
2025-05-23 11:20:06.905 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NTQzODM1NjBiY2Y5NDMzMDhjMTVhMDRkNzg4YmUyNTY无须刷新
2025-05-23 11:20:06.929 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OWFhMjhmNjU2OGU0NGM1Nzk3NmE0ZmUxMDY0MGQyMWY无须刷新
2025-05-23 11:20:06.964 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzIyMjdjNDJlODIxNDFiM2IwMDI5MzU4ZDZlNTA2NzM无须刷新
2025-05-23 11:20:06.988 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ODQ4MDRkYWM4MDVhNGRhNGIyOGIxOGIyNjEzNzQ2ZWE无须刷新
2025-05-23 11:20:07.012 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NDU2OTViZTk3YWE4NDI1ZmFlNGIwOTAxZTYxODQxMmY无须刷新
2025-05-23 11:20:07.037 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Yzg0NmY4OTc1NGRlNDliM2FmZDg3ODg1NmNjZDg5ZmI无须刷新
2025-05-23 11:20:07.073 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Zjg5YTlhZDZlMzUyNDgzMWJjYzU1ZGRiOWFhMDUzNzg无须刷新
2025-05-23 11:20:07.105 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NjM5MWRmMGViYWE2NGVmYmIzYzMyY2VlMGI3Y2EwZmI无须刷新
2025-05-23 11:20:07.106 [scheduling-1] INFO  c.p.o.meiye.job.RefreshUserTokenTask - 定时任务:执行耗时:7s
2025-05-23 11:30:00.016 [scheduling-1] INFO  c.p.o.meiye.job.RefreshUserTokenTask - 定时任务:刷新user_token开始执行
2025-05-23 11:30:05.034 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzAwMWQzNGJjNzllNDc2Yjk1NjVhZjE2MDQyOWVjMmY无须刷新
2025-05-23 11:30:05.073 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzRkNjk1Y2NhZGU4NGM2OWI4MWVjNzdhZTQ5YWE0ZTA无须刷新
2025-05-23 11:30:05.104 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDI0NjZmZDk3MWYzNGNmOGIwM2E5NDQ0NDc1YThiNjk无须刷新
2025-05-23 11:30:05.144 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:N2JlZjdhNWYxYzg1NDQxY2JmYmY4OTQzYmQ1MzRkZTU无须刷新
2025-05-23 11:30:05.216 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MTZiZWJlZTRlNTFiNGM0MzgzMThlYzY5NGJlN2UxZmE无须刷新
2025-05-23 11:30:05.254 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OTc3NjI0OWQwYzlmNDUzNjk3OTRkY2EwZTI4MmQxNzc无须刷新
2025-05-23 11:30:05.282 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZjliNzFiYWMzMTQ2NDllMGFiNjczNzU0NmNmZTNkYTI无须刷新
2025-05-23 11:30:05.316 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:M2ZlNGNhZGU2ZTRiNGI1ZjgxNGNhMGRlNjM5M2ZlMmM无须刷新
2025-05-23 11:30:05.352 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzhkN2JkMmQ4YmIxNDhiMGEwMDJlN2IxOGExMmQ5YTQ无须刷新
2025-05-23 11:30:05.385 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Y2JjMzRjMTkxY2E4NGExN2FkYTYxMzEyNWE5YmFlZjE无须刷新
2025-05-23 11:30:05.409 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NGNjN2M3Yjc3MWExNDkyYjkyMGU1YjIwYmNjZTg5ODQ无须刷新
2025-05-23 11:30:05.435 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NmNmOWJjZjM0NTgzNDRmZTk1YWFkYzZhN2Q4NjAzNjk无须刷新
2025-05-23 11:30:05.460 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YjliMDk0ZTI2MzY2NGRjZmIyMjlmYWEwZTcxNjMxY2I无须刷新
2025-05-23 11:30:05.491 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzAyZmVlMWFmYTM2NDZkOGEyMTQ3Y2MyMzA1Y2EyYWE无须刷新
2025-05-23 11:30:05.517 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Mjg0MDhlYjVlZTkzNDg3M2E1N2ZjZGJjMWVlMTA2NTg无须刷新
2025-05-23 11:30:05.542 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDUzYTRkYzFhOGRlNDhhM2EyMDE3YWMzYjFiY2RlZWY无须刷新
2025-05-23 11:30:05.574 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NDQ4ZmQ4MzI0MTFlNDcxYzlkMWQ0MmUxM2ExNDUyZDY无须刷新
2025-05-23 11:30:05.608 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MjVkNDM2OGZiMmNlNDViZGJjN2MxODhjMzFiYTU0Mzg无须刷新
2025-05-23 11:30:05.633 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NmEzNWZhNzMwYjA1NGU5MGFkZjc0NzZkODZlNjAxMDc无须刷新
2025-05-23 11:30:05.658 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzNmNGRlNjhkZmU4NGNlMmFjZTc1NGE2ODBkY2M4N2I无须刷新
2025-05-23 11:30:05.690 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MzIyYTllYWNkOGZmNDg0NjkwZDA0NzU0N2JmNmI2MGU无须刷新
2025-05-23 11:30:05.718 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YTJkM2ZhYzQ5MDhmNDRhMDljNjI3MjRiNjY0Yzc3MWU无须刷新
2025-05-23 11:30:05.744 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YWE5YTBjMzA2YzFlNGEzMTgxZGZhZjBmN2QzMGFjYTQ无须刷新
2025-05-23 11:30:05.767 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YmFiMTc0NjcxMjY4NDBmNDkzNzc0OTQ3NmRjYzY3NjE无须刷新
2025-05-23 11:30:05.792 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Y2I0NmE0YmYyZjBiNDIzODg2NjU5NGEyMWJmZGYyOTY无须刷新
2025-05-23 11:30:05.816 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OTQ3M2E4ZWNkMDRkNDhkMjgyOTBiMTJmYTQ5YTg1MTQ无须刷新
2025-05-23 11:30:05.842 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MjdmOWQxNjk1ZGY1NDQzYmJjZThkYzY1N2I1OTNiNzI无须刷新
2025-05-23 11:30:05.869 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YmI5NzZlNWIyMTRhNDg4MGExZDA4MTdhZDIzMTc3NDk无须刷新
2025-05-23 11:30:05.898 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZmU2ZDU5NmIxNmE5NDBkNTlkMzI5OGI1YTM1Y2I0MzA无须刷新
2025-05-23 11:30:05.934 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzI4MGJkOWZjMGY2NDU0NjhmMDM3YzMyMjQzNTUyNDU无须刷新
2025-05-23 11:30:05.958 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MTc0ZWZhYWMzMWYwNGQxNTg0M2IyNzM2ZDMxNTM5ZjQ无须刷新
2025-05-23 11:30:06.000 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZjUzYzVmMTdmOGQyNGE4ZTgyZWFmMjliYmUwNmIzYTI无须刷新
2025-05-23 11:30:06.027 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDZhMmZkODg3N2NhNGU2Mzg0NjkyMzk1OWM4YTk0MDY无须刷新
2025-05-23 11:30:06.059 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MmY1NmRhMjAxZDU1NDlmOThkMGY5ZDM5OTE3ZDMzN2U无须刷新
2025-05-23 11:30:06.120 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NTQzODM1NjBiY2Y5NDMzMDhjMTVhMDRkNzg4YmUyNTY无须刷新
2025-05-23 11:30:06.155 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OWFhMjhmNjU2OGU0NGM1Nzk3NmE0ZmUxMDY0MGQyMWY无须刷新
2025-05-23 11:30:06.191 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzIyMjdjNDJlODIxNDFiM2IwMDI5MzU4ZDZlNTA2NzM无须刷新
2025-05-23 11:30:06.216 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ODQ4MDRkYWM4MDVhNGRhNGIyOGIxOGIyNjEzNzQ2ZWE无须刷新
2025-05-23 11:30:06.242 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NDU2OTViZTk3YWE4NDI1ZmFlNGIwOTAxZTYxODQxMmY无须刷新
2025-05-23 11:30:06.265 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Yzg0NmY4OTc1NGRlNDliM2FmZDg3ODg1NmNjZDg5ZmI无须刷新
2025-05-23 11:30:06.296 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Zjg5YTlhZDZlMzUyNDgzMWJjYzU1ZGRiOWFhMDUzNzg无须刷新
2025-05-23 11:30:06.320 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NjM5MWRmMGViYWE2NGVmYmIzYzMyY2VlMGI3Y2EwZmI无须刷新
2025-05-23 11:30:06.320 [scheduling-1] INFO  c.p.o.meiye.job.RefreshUserTokenTask - 定时任务:执行耗时:6s
2025-05-23 11:39:59.986 [scheduling-1] INFO  c.p.o.meiye.job.RefreshUserTokenTask - 定时任务:刷新user_token开始执行
2025-05-23 11:40:04.974 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzAwMWQzNGJjNzllNDc2Yjk1NjVhZjE2MDQyOWVjMmY无须刷新
2025-05-23 11:40:05.006 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzRkNjk1Y2NhZGU4NGM2OWI4MWVjNzdhZTQ5YWE0ZTA无须刷新
2025-05-23 11:40:05.033 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDI0NjZmZDk3MWYzNGNmOGIwM2E5NDQ0NDc1YThiNjk无须刷新
2025-05-23 11:40:05.060 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:N2JlZjdhNWYxYzg1NDQxY2JmYmY4OTQzYmQ1MzRkZTU无须刷新
2025-05-23 11:40:05.092 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MTZiZWJlZTRlNTFiNGM0MzgzMThlYzY5NGJlN2UxZmE无须刷新
2025-05-23 11:40:05.118 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OTc3NjI0OWQwYzlmNDUzNjk3OTRkY2EwZTI4MmQxNzc无须刷新
2025-05-23 11:40:05.144 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZjliNzFiYWMzMTQ2NDllMGFiNjczNzU0NmNmZTNkYTI无须刷新
2025-05-23 11:40:05.171 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:M2ZlNGNhZGU2ZTRiNGI1ZjgxNGNhMGRlNjM5M2ZlMmM无须刷新
2025-05-23 11:40:05.195 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzhkN2JkMmQ4YmIxNDhiMGEwMDJlN2IxOGExMmQ5YTQ无须刷新
2025-05-23 11:40:05.223 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Y2JjMzRjMTkxY2E4NGExN2FkYTYxMzEyNWE5YmFlZjE无须刷新
2025-05-23 11:40:05.252 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NGNjN2M3Yjc3MWExNDkyYjkyMGU1YjIwYmNjZTg5ODQ无须刷新
2025-05-23 11:40:05.275 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NmNmOWJjZjM0NTgzNDRmZTk1YWFkYzZhN2Q4NjAzNjk无须刷新
2025-05-23 11:40:05.300 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YjliMDk0ZTI2MzY2NGRjZmIyMjlmYWEwZTcxNjMxY2I无须刷新
2025-05-23 11:40:05.331 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzAyZmVlMWFmYTM2NDZkOGEyMTQ3Y2MyMzA1Y2EyYWE无须刷新
2025-05-23 11:40:05.356 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Mjg0MDhlYjVlZTkzNDg3M2E1N2ZjZGJjMWVlMTA2NTg无须刷新
2025-05-23 11:40:05.380 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDUzYTRkYzFhOGRlNDhhM2EyMDE3YWMzYjFiY2RlZWY无须刷新
2025-05-23 11:40:05.404 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NDQ4ZmQ4MzI0MTFlNDcxYzlkMWQ0MmUxM2ExNDUyZDY无须刷新
2025-05-23 11:40:05.428 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MjVkNDM2OGZiMmNlNDViZGJjN2MxODhjMzFiYTU0Mzg无须刷新
2025-05-23 11:40:05.451 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NmEzNWZhNzMwYjA1NGU5MGFkZjc0NzZkODZlNjAxMDc无须刷新
2025-05-23 11:40:05.477 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzNmNGRlNjhkZmU4NGNlMmFjZTc1NGE2ODBkY2M4N2I无须刷新
2025-05-23 11:40:05.509 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MzIyYTllYWNkOGZmNDg0NjkwZDA0NzU0N2JmNmI2MGU无须刷新
2025-05-23 11:40:05.535 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YTJkM2ZhYzQ5MDhmNDRhMDljNjI3MjRiNjY0Yzc3MWU无须刷新
2025-05-23 11:40:05.561 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YWE5YTBjMzA2YzFlNGEzMTgxZGZhZjBmN2QzMGFjYTQ无须刷新
2025-05-23 11:40:05.592 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YmFiMTc0NjcxMjY4NDBmNDkzNzc0OTQ3NmRjYzY3NjE无须刷新
2025-05-23 11:40:05.623 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Y2I0NmE0YmYyZjBiNDIzODg2NjU5NGEyMWJmZGYyOTY无须刷新
2025-05-23 11:40:05.645 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OTQ3M2E4ZWNkMDRkNDhkMjgyOTBiMTJmYTQ5YTg1MTQ无须刷新
2025-05-23 11:40:05.669 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MjdmOWQxNjk1ZGY1NDQzYmJjZThkYzY1N2I1OTNiNzI无须刷新
2025-05-23 11:40:05.696 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YmI5NzZlNWIyMTRhNDg4MGExZDA4MTdhZDIzMTc3NDk无须刷新
2025-05-23 11:40:05.731 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZmU2ZDU5NmIxNmE5NDBkNTlkMzI5OGI1YTM1Y2I0MzA无须刷新
2025-05-23 11:40:05.804 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzI4MGJkOWZjMGY2NDU0NjhmMDM3YzMyMjQzNTUyNDU无须刷新
2025-05-23 11:40:05.839 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MTc0ZWZhYWMzMWYwNGQxNTg0M2IyNzM2ZDMxNTM5ZjQ无须刷新
2025-05-23 11:40:05.865 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZjUzYzVmMTdmOGQyNGE4ZTgyZWFmMjliYmUwNmIzYTI无须刷新
2025-05-23 11:40:05.895 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDZhMmZkODg3N2NhNGU2Mzg0NjkyMzk1OWM4YTk0MDY无须刷新
2025-05-23 11:40:05.980 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MmY1NmRhMjAxZDU1NDlmOThkMGY5ZDM5OTE3ZDMzN2U无须刷新
2025-05-23 11:40:06.009 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NTQzODM1NjBiY2Y5NDMzMDhjMTVhMDRkNzg4YmUyNTY无须刷新
2025-05-23 11:40:06.037 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OWFhMjhmNjU2OGU0NGM1Nzk3NmE0ZmUxMDY0MGQyMWY无须刷新
2025-05-23 11:40:06.069 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzIyMjdjNDJlODIxNDFiM2IwMDI5MzU4ZDZlNTA2NzM无须刷新
2025-05-23 11:40:06.103 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ODQ4MDRkYWM4MDVhNGRhNGIyOGIxOGIyNjEzNzQ2ZWE无须刷新
2025-05-23 11:40:06.136 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NDU2OTViZTk3YWE4NDI1ZmFlNGIwOTAxZTYxODQxMmY无须刷新
2025-05-23 11:40:06.159 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Yzg0NmY4OTc1NGRlNDliM2FmZDg3ODg1NmNjZDg5ZmI无须刷新
2025-05-23 11:40:06.184 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Zjg5YTlhZDZlMzUyNDgzMWJjYzU1ZGRiOWFhMDUzNzg无须刷新
2025-05-23 11:40:06.209 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NjM5MWRmMGViYWE2NGVmYmIzYzMyY2VlMGI3Y2EwZmI无须刷新
2025-05-23 11:40:06.209 [scheduling-1] INFO  c.p.o.meiye.job.RefreshUserTokenTask - 定时任务:执行耗时:6s
2025-05-23 11:50:00.000 [scheduling-1] INFO  c.p.o.meiye.job.RefreshUserTokenTask - 定时任务:刷新user_token开始执行
2025-05-23 11:50:05.103 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzAwMWQzNGJjNzllNDc2Yjk1NjVhZjE2MDQyOWVjMmY无须刷新
2025-05-23 11:50:05.141 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzRkNjk1Y2NhZGU4NGM2OWI4MWVjNzdhZTQ5YWE0ZTA无须刷新
2025-05-23 11:50:05.179 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDI0NjZmZDk3MWYzNGNmOGIwM2E5NDQ0NDc1YThiNjk无须刷新
2025-05-23 11:50:05.208 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:N2JlZjdhNWYxYzg1NDQxY2JmYmY4OTQzYmQ1MzRkZTU无须刷新
2025-05-23 11:50:05.236 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MTZiZWJlZTRlNTFiNGM0MzgzMThlYzY5NGJlN2UxZmE无须刷新
2025-05-23 11:50:05.262 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OTc3NjI0OWQwYzlmNDUzNjk3OTRkY2EwZTI4MmQxNzc无须刷新
2025-05-23 11:50:05.299 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZjliNzFiYWMzMTQ2NDllMGFiNjczNzU0NmNmZTNkYTI无须刷新
2025-05-23 11:50:05.325 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:M2ZlNGNhZGU2ZTRiNGI1ZjgxNGNhMGRlNjM5M2ZlMmM无须刷新
2025-05-23 11:50:05.347 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzhkN2JkMmQ4YmIxNDhiMGEwMDJlN2IxOGExMmQ5YTQ无须刷新
2025-05-23 11:50:05.372 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Y2JjMzRjMTkxY2E4NGExN2FkYTYxMzEyNWE5YmFlZjE无须刷新
2025-05-23 11:50:05.396 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NGNjN2M3Yjc3MWExNDkyYjkyMGU1YjIwYmNjZTg5ODQ无须刷新
2025-05-23 11:50:05.419 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NmNmOWJjZjM0NTgzNDRmZTk1YWFkYzZhN2Q4NjAzNjk无须刷新
2025-05-23 11:50:05.445 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YjliMDk0ZTI2MzY2NGRjZmIyMjlmYWEwZTcxNjMxY2I无须刷新
2025-05-23 11:50:05.479 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzAyZmVlMWFmYTM2NDZkOGEyMTQ3Y2MyMzA1Y2EyYWE无须刷新
2025-05-23 11:50:05.514 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Mjg0MDhlYjVlZTkzNDg3M2E1N2ZjZGJjMWVlMTA2NTg无须刷新
2025-05-23 11:50:05.548 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDUzYTRkYzFhOGRlNDhhM2EyMDE3YWMzYjFiY2RlZWY无须刷新
2025-05-23 11:50:05.579 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NDQ4ZmQ4MzI0MTFlNDcxYzlkMWQ0MmUxM2ExNDUyZDY无须刷新
2025-05-23 11:50:05.608 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MjVkNDM2OGZiMmNlNDViZGJjN2MxODhjMzFiYTU0Mzg无须刷新
2025-05-23 11:50:05.632 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NmEzNWZhNzMwYjA1NGU5MGFkZjc0NzZkODZlNjAxMDc无须刷新
2025-05-23 11:50:05.656 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzNmNGRlNjhkZmU4NGNlMmFjZTc1NGE2ODBkY2M4N2I无须刷新
2025-05-23 11:50:05.682 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MzIyYTllYWNkOGZmNDg0NjkwZDA0NzU0N2JmNmI2MGU无须刷新
2025-05-23 11:50:05.711 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YTJkM2ZhYzQ5MDhmNDRhMDljNjI3MjRiNjY0Yzc3MWU无须刷新
2025-05-23 11:50:05.737 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YWE5YTBjMzA2YzFlNGEzMTgxZGZhZjBmN2QzMGFjYTQ无须刷新
2025-05-23 11:50:05.758 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YmFiMTc0NjcxMjY4NDBmNDkzNzc0OTQ3NmRjYzY3NjE无须刷新
2025-05-23 11:50:05.785 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Y2I0NmE0YmYyZjBiNDIzODg2NjU5NGEyMWJmZGYyOTY无须刷新
2025-05-23 11:50:05.816 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OTQ3M2E4ZWNkMDRkNDhkMjgyOTBiMTJmYTQ5YTg1MTQ无须刷新
2025-05-23 11:50:05.843 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MjdmOWQxNjk1ZGY1NDQzYmJjZThkYzY1N2I1OTNiNzI无须刷新
2025-05-23 11:50:05.873 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YmI5NzZlNWIyMTRhNDg4MGExZDA4MTdhZDIzMTc3NDk无须刷新
2025-05-23 11:50:05.909 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZmU2ZDU5NmIxNmE5NDBkNTlkMzI5OGI1YTM1Y2I0MzA无须刷新
2025-05-23 11:50:05.939 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzI4MGJkOWZjMGY2NDU0NjhmMDM3YzMyMjQzNTUyNDU无须刷新
2025-05-23 11:50:05.967 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MTc0ZWZhYWMzMWYwNGQxNTg0M2IyNzM2ZDMxNTM5ZjQ无须刷新
2025-05-23 11:50:06.002 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZjUzYzVmMTdmOGQyNGE4ZTgyZWFmMjliYmUwNmIzYTI无须刷新
2025-05-23 11:50:06.031 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDZhMmZkODg3N2NhNGU2Mzg0NjkyMzk1OWM4YTk0MDY无须刷新
2025-05-23 11:50:06.062 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MmY1NmRhMjAxZDU1NDlmOThkMGY5ZDM5OTE3ZDMzN2U无须刷新
2025-05-23 11:50:06.096 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NTQzODM1NjBiY2Y5NDMzMDhjMTVhMDRkNzg4YmUyNTY无须刷新
2025-05-23 11:50:06.138 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OWFhMjhmNjU2OGU0NGM1Nzk3NmE0ZmUxMDY0MGQyMWY无须刷新
2025-05-23 11:50:06.167 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzIyMjdjNDJlODIxNDFiM2IwMDI5MzU4ZDZlNTA2NzM无须刷新
2025-05-23 11:50:06.198 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ODQ4MDRkYWM4MDVhNGRhNGIyOGIxOGIyNjEzNzQ2ZWE无须刷新
2025-05-23 11:50:06.230 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NDU2OTViZTk3YWE4NDI1ZmFlNGIwOTAxZTYxODQxMmY无须刷新
2025-05-23 11:50:06.257 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Yzg0NmY4OTc1NGRlNDliM2FmZDg3ODg1NmNjZDg5ZmI无须刷新
2025-05-23 11:50:06.287 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Zjg5YTlhZDZlMzUyNDgzMWJjYzU1ZGRiOWFhMDUzNzg无须刷新
2025-05-23 11:50:06.311 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NjM5MWRmMGViYWE2NGVmYmIzYzMyY2VlMGI3Y2EwZmI无须刷新
2025-05-23 11:50:06.312 [scheduling-1] INFO  c.p.o.meiye.job.RefreshUserTokenTask - 定时任务:执行耗时:6s
2025-05-23 12:00:00.008 [scheduling-1] INFO  c.p.o.meiye.job.RefreshUserTokenTask - 定时任务:刷新user_token开始执行
2025-05-23 12:00:04.730 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzAwMWQzNGJjNzllNDc2Yjk1NjVhZjE2MDQyOWVjMmY无须刷新
2025-05-23 12:00:04.756 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzRkNjk1Y2NhZGU4NGM2OWI4MWVjNzdhZTQ5YWE0ZTA无须刷新
2025-05-23 12:00:04.781 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDI0NjZmZDk3MWYzNGNmOGIwM2E5NDQ0NDc1YThiNjk无须刷新
2025-05-23 12:00:04.807 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:N2JlZjdhNWYxYzg1NDQxY2JmYmY4OTQzYmQ1MzRkZTU无须刷新
2025-05-23 12:00:04.840 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MTZiZWJlZTRlNTFiNGM0MzgzMThlYzY5NGJlN2UxZmE无须刷新
2025-05-23 12:00:04.871 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OTc3NjI0OWQwYzlmNDUzNjk3OTRkY2EwZTI4MmQxNzc无须刷新
2025-05-23 12:00:04.895 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZjliNzFiYWMzMTQ2NDllMGFiNjczNzU0NmNmZTNkYTI无须刷新
2025-05-23 12:00:04.921 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:M2ZlNGNhZGU2ZTRiNGI1ZjgxNGNhMGRlNjM5M2ZlMmM无须刷新
2025-05-23 12:00:04.949 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzhkN2JkMmQ4YmIxNDhiMGEwMDJlN2IxOGExMmQ5YTQ无须刷新
2025-05-23 12:00:04.981 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Y2JjMzRjMTkxY2E4NGExN2FkYTYxMzEyNWE5YmFlZjE无须刷新
2025-05-23 12:00:05.005 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NGNjN2M3Yjc3MWExNDkyYjkyMGU1YjIwYmNjZTg5ODQ无须刷新
2025-05-23 12:00:05.032 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NmNmOWJjZjM0NTgzNDRmZTk1YWFkYzZhN2Q4NjAzNjk无须刷新
2025-05-23 12:00:05.057 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YjliMDk0ZTI2MzY2NGRjZmIyMjlmYWEwZTcxNjMxY2I无须刷新
2025-05-23 12:00:05.085 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzAyZmVlMWFmYTM2NDZkOGEyMTQ3Y2MyMzA1Y2EyYWE无须刷新
2025-05-23 12:00:05.110 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Mjg0MDhlYjVlZTkzNDg3M2E1N2ZjZGJjMWVlMTA2NTg无须刷新
2025-05-23 12:00:05.144 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDUzYTRkYzFhOGRlNDhhM2EyMDE3YWMzYjFiY2RlZWY无须刷新
2025-05-23 12:00:05.167 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NDQ4ZmQ4MzI0MTFlNDcxYzlkMWQ0MmUxM2ExNDUyZDY无须刷新
2025-05-23 12:00:05.190 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MjVkNDM2OGZiMmNlNDViZGJjN2MxODhjMzFiYTU0Mzg无须刷新
2025-05-23 12:00:05.222 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NmEzNWZhNzMwYjA1NGU5MGFkZjc0NzZkODZlNjAxMDc无须刷新
2025-05-23 12:00:05.252 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NzNmNGRlNjhkZmU4NGNlMmFjZTc1NGE2ODBkY2M4N2I无须刷新
2025-05-23 12:00:05.280 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MzIyYTllYWNkOGZmNDg0NjkwZDA0NzU0N2JmNmI2MGU无须刷新
2025-05-23 12:00:05.308 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YTJkM2ZhYzQ5MDhmNDRhMDljNjI3MjRiNjY0Yzc3MWU无须刷新
2025-05-23 12:00:05.334 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YWE5YTBjMzA2YzFlNGEzMTgxZGZhZjBmN2QzMGFjYTQ无须刷新
2025-05-23 12:00:05.365 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YmFiMTc0NjcxMjY4NDBmNDkzNzc0OTQ3NmRjYzY3NjE无须刷新
2025-05-23 12:00:05.389 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Y2I0NmE0YmYyZjBiNDIzODg2NjU5NGEyMWJmZGYyOTY无须刷新
2025-05-23 12:00:05.412 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OTQ3M2E4ZWNkMDRkNDhkMjgyOTBiMTJmYTQ5YTg1MTQ无须刷新
2025-05-23 12:00:05.441 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MjdmOWQxNjk1ZGY1NDQzYmJjZThkYzY1N2I1OTNiNzI无须刷新
2025-05-23 12:00:05.468 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YmI5NzZlNWIyMTRhNDg4MGExZDA4MTdhZDIzMTc3NDk无须刷新
2025-05-23 12:00:05.491 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZmU2ZDU5NmIxNmE5NDBkNTlkMzI5OGI1YTM1Y2I0MzA无须刷新
2025-05-23 12:00:05.516 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzI4MGJkOWZjMGY2NDU0NjhmMDM3YzMyMjQzNTUyNDU无须刷新
2025-05-23 12:00:05.543 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MTc0ZWZhYWMzMWYwNGQxNTg0M2IyNzM2ZDMxNTM5ZjQ无须刷新
2025-05-23 12:00:05.567 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZjUzYzVmMTdmOGQyNGE4ZTgyZWFmMjliYmUwNmIzYTI无须刷新
2025-05-23 12:00:05.595 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ZDZhMmZkODg3N2NhNGU2Mzg0NjkyMzk1OWM4YTk0MDY无须刷新
2025-05-23 12:00:05.622 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:MmY1NmRhMjAxZDU1NDlmOThkMGY5ZDM5OTE3ZDMzN2U无须刷新
2025-05-23 12:00:05.652 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NTQzODM1NjBiY2Y5NDMzMDhjMTVhMDRkNzg4YmUyNTY无须刷新
2025-05-23 12:00:05.681 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:OWFhMjhmNjU2OGU0NGM1Nzk3NmE0ZmUxMDY0MGQyMWY无须刷新
2025-05-23 12:00:05.704 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:YzIyMjdjNDJlODIxNDFiM2IwMDI5MzU4ZDZlNTA2NzM无须刷新
2025-05-23 12:00:05.739 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:ODQ4MDRkYWM4MDVhNGRhNGIyOGIxOGIyNjEzNzQ2ZWE无须刷新
2025-05-23 12:00:05.776 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NDU2OTViZTk3YWE4NDI1ZmFlNGIwOTAxZTYxODQxMmY无须刷新
2025-05-23 12:00:05.801 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Yzg0NmY4OTc1NGRlNDliM2FmZDg3ODg1NmNjZDg5ZmI无须刷新
2025-05-23 12:00:05.830 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:Zjg5YTlhZDZlMzUyNDgzMWJjYzU1ZGRiOWFhMDUzNzg无须刷新
2025-05-23 12:00:05.857 [scheduling-1] INFO  cn.pf.orch.meiye.service.SsoService - token:NjM5MWRmMGViYWE2NGVmYmIzYzMyY2VlMGI3Y2EwZmI无须刷新
2025-05-23 12:00:05.857 [scheduling-1] INFO  c.p.o.meiye.job.RefreshUserTokenTask - 定时任务:执行耗时:5s
2025-05-23 12:19:31.947 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=13m25s796ms).
2025-05-23 12:19:38.763 [redisson-netty-2-1] ERROR o.r.c.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0x308ae386, L:/***********:51674 - R:**********/**********:6379]
java.io.IOException: Operation timed out
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:256)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-23 12:19:38.763 [redisson-netty-2-23] ERROR o.r.c.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0xa44620a4, L:/***********:51669 - R:**********/**********:6379]
java.io.IOException: Operation timed out
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:256)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-23 12:19:38.763 [redisson-netty-2-28] ERROR o.r.c.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0xd4db12bb, L:/***********:51671 - R:**********/**********:6379]
java.io.IOException: Operation timed out
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:256)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-23 12:19:38.763 [redisson-netty-2-32] ERROR o.r.c.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0x6d14abcd, L:/***********:51673 - R:**********/**********:6379]
java.io.IOException: Operation timed out
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:256)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-23 12:19:38.763 [redisson-netty-2-22] ERROR o.r.c.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0xc23187c6, L:/***********:51667 - R:**********/**********:6379]
java.io.IOException: Operation timed out
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:256)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-23 12:19:38.763 [redisson-netty-2-29] ERROR o.r.c.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0x3366c979, L:/***********:51672 - R:**********/**********:6379]
java.io.IOException: Operation timed out
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:256)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-23 12:19:38.763 [redisson-netty-2-24] ERROR o.r.c.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0x62c2067d, L:/***********:51668 - R:**********/**********:6379]
java.io.IOException: Operation timed out
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:256)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-23 12:19:38.763 [redisson-netty-2-25] ERROR o.r.c.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0x6461eb33, L:/***********:51670 - R:**********/**********:6379]
java.io.IOException: Operation timed out
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:256)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-23 12:19:38.794 [redisson-netty-2-7] ERROR o.r.c.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0xb18e34cb, L:/***********:51676 - R:**********/**********:6379]
java.io.IOException: Operation timed out
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:256)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-23 12:19:38.794 [redisson-netty-2-6] ERROR o.r.c.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0x42e23da6, L:/***********:51675 - R:**********/**********:6379]
java.io.IOException: Operation timed out
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:256)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-23 12:19:38.830 [redisson-netty-2-9] ERROR o.r.c.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0xf9e44bab, L:/***********:51662 - R:**********/**********:6379]
java.io.IOException: Operation timed out
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:256)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-23 12:19:38.831 [redisson-netty-2-16] ERROR o.r.c.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0xc0aa9e64, L:/***********:51665 - R:**********/**********:6379]
java.io.IOException: Operation timed out
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:256)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-23 12:19:38.860 [redisson-netty-2-17] ERROR o.r.c.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0x758c559a, L:/***********:51666 - R:**********/**********:6379]
java.io.IOException: Operation timed out
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:256)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-23 12:19:38.860 [redisson-netty-2-8] ERROR o.r.c.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0x9121fef4, L:/***********:51661 - R:**********/**********:6379]
java.io.IOException: Operation timed out
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:256)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-23 12:19:38.887 [redisson-netty-2-3] ERROR o.r.c.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0x633290b8, L:/***********:51657 - R:**********/**********:6379]
java.io.IOException: Operation timed out
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:256)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-23 12:19:38.917 [redisson-netty-2-2] ERROR o.r.c.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0x0aba0e44, L:/***********:51658 - R:**********/**********:6379]
java.io.IOException: Operation timed out
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:256)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-23 12:19:41.105 [redisson-netty-2-12] ERROR o.r.c.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0xe68d3e67, L:/***********:51663 - R:**********/**********:6379]
java.io.IOException: Operation timed out
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:256)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-23 12:19:41.105 [redisson-netty-2-13] ERROR o.r.c.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0xe55f2abb, L:/***********:51664 - R:**********/**********:6379]
java.io.IOException: Operation timed out
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:256)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-23 12:19:41.608 [redisson-netty-2-4] ERROR o.r.c.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0xe6df3097, L:/***********:51659 - R:**********/**********:6379]
java.io.IOException: Operation timed out
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:256)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-23 12:19:42.641 [redisson-netty-2-5] ERROR o.r.c.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0x551f3e7a, L:/***********:51660 - R:**********/**********:6379]
java.io.IOException: Operation timed out
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:256)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-23 12:23:31.687 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=3m29s728ms).
2025-05-23 12:35:29.762 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=11m58s76ms).
2025-05-23 12:46:35.267 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=11m5s505ms).
2025-05-23 12:57:10.333 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=10m35s66ms).
2025-05-23 13:04:36.467 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=7m26s134ms).
2025-05-23 13:29:38.691 [scheduling-1] INFO  c.p.o.meiye.job.RefreshUserTokenTask - 定时任务:刷新user_token开始执行
2025-05-23 13:33:32.454 [scheduling-1] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Increase connection pool size. Node source: NodeSource [slot=null, addr=null, redisClient=null, redirect=null, entry=MasterSlaveEntry [masterEntry=[freeSubscribeConnectionsAmount=10, freeSubscribeConnectionsCounter=value:50:queue:0, freeConnectionsAmount=9, freeConnectionsCounter=value:49:queue:0, freezeReason=null, client=[addr=redis://**********:6379], nodeType=MASTER, firstFail=0]]], connection: RedisConnection@1637304317 [redisClient=[addr=redis://**********:6379], channel=[id: 0xd4db12bb, L:/***********:51671 ! R:**********/**********:6379], currentCommand=null, usage=1], command: (SCAN), params: [0, MATCH, GENN:MEIYE:CORE:AUTH_TOKEN:*] after 3 retry attempts; nested exception is org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Increase connection pool size. Node source: NodeSource [slot=null, addr=null, redisClient=null, redirect=null, entry=MasterSlaveEntry [masterEntry=[freeSubscribeConnectionsAmount=10, freeSubscribeConnectionsCounter=value:50:queue:0, freeConnectionsAmount=9, freeConnectionsCounter=value:49:queue:0, freezeReason=null, client=[addr=redis://**********:6379], nodeType=MASTER, firstFail=0]]], connection: RedisConnection@1637304317 [redisClient=[addr=redis://**********:6379], channel=[id: 0xd4db12bb, L:/***********:51671 ! R:**********/**********:6379], currentCommand=null, usage=1], command: (SCAN), params: [0, MATCH, GENN:MEIYE:CORE:AUTH_TOKEN:*] after 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:204)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:199)
	at org.redisson.spring.data.connection.RedissonConnection$1.doScan(RedissonConnection.java:281)
	at org.springframework.data.redis.core.ScanCursor.scan(ScanCursor.java:90)
	at org.springframework.data.redis.core.ScanCursor.doOpen(ScanCursor.java:132)
	at org.springframework.data.redis.core.ScanCursor.open(ScanCursor.java:121)
	at org.redisson.spring.data.connection.RedissonConnection.scan(RedissonConnection.java:296)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.scan(DefaultStringRedisConnection.java:4115)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at com.sun.proxy.$Proxy308.scan(Unknown Source)
	at cn.pf.orch.meiye.job.RefreshUserTokenTask.lambda$scanKeys$0(RefreshUserTokenTask.java:50)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:223)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:190)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:177)
	at cn.pf.orch.meiye.job.RefreshUserTokenTask.scanKeys(RefreshUserTokenTask.java:49)
	at cn.pf.orch.meiye.job.RefreshUserTokenTask.executeTask(RefreshUserTokenTask.java:33)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Increase connection pool size. Node source: NodeSource [slot=null, addr=null, redisClient=null, redirect=null, entry=MasterSlaveEntry [masterEntry=[freeSubscribeConnectionsAmount=10, freeSubscribeConnectionsCounter=value:50:queue:0, freeConnectionsAmount=9, freeConnectionsCounter=value:49:queue:0, freezeReason=null, client=[addr=redis://**********:6379], nodeType=MASTER, firstFail=0]]], connection: RedisConnection@1637304317 [redisClient=[addr=redis://**********:6379], channel=[id: 0xd4db12bb, L:/***********:51671 ! R:**********/**********:6379], currentCommand=null, usage=1], command: (SCAN), params: [0, MATCH, GENN:MEIYE:CORE:AUTH_TOKEN:*] after 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:325)
	at org.redisson.command.RedisExecutor.lambda$execute$2(RedisExecutor.java:169)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:999)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:860)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1367)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:877)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:940)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1247)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:174)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:167)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-05-23 13:33:33.621 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=28m57s154ms).
2025-05-23 13:42:02.199 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=6m58s563ms).
2025-05-23 13:42:30.109 [SpringApplicationShutdownHook] INFO  o.d.x.f.s.core.FileStorageService - 销毁存储平台 minio-1 成功
