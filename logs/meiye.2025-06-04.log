2025-06-04 16:30:06.258 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-04 16:30:06.274 [main] INFO  cn.pf.orch.Application - Starting Application using Java 1.8.0_401 on guoxiaoleideMacBook-Pro.local with PID 50043 (/Users/<USER>/IdeaProjects/workspace/pf-orch-meiye/pf-orch-meiye-service/target/classes started by gxl in /Users/<USER>/IdeaProjects/workspace/pf-orch-meiye)
2025-06-04 16:30:06.275 [main] INFO  cn.pf.orch.Application - The following 1 profile is active: "dev"
2025-06-04 16:30:07.115 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 16:30:07.116 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 16:30:07.140 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
2025-06-04 16:30:07.350 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=f6c70ece-5277-35ed-9eb6-b79ca9147cb1
2025-06-04 16:30:07.744 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8102 (http)
2025-06-04 16:30:07.749 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8102"]
2025-06-04 16:30:07.749 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-04 16:30:07.750 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.75]
2025-06-04 16:30:07.857 [main] INFO  o.a.c.c.C.[.[localhost].[/api/meiye] - Initializing Spring embedded WebApplicationContext
2025-06-04 16:30:07.857 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1521 ms
2025-06-04 16:30:08.557 [main] INFO  org.redisson.Version - Redisson 3.17.6
2025-06-04 16:30:08.573 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-06-04 16:30:08.882 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 10 connections initialized for **********/**********:6379
2025-06-04 16:30:08.949 [redisson-netty-2-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 10 connections initialized for **********/**********:6379
2025-06-04 16:30:09.295 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-04 16:30:09.544 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-04 16:30:10.355 [main] INFO  o.d.x.f.s.c.FileStorageServiceBuilder - 加载 MinIO 存储平台：minio-1
2025-06-04 16:30:11.417 [main] INFO  c.g.c.c.c.CaffeineMultiCacheManager - Create CaffeineCacheManager with names=[genn:cache:caffeine_manager], cacheSpec='maximumSize=10000,expireAfterWrite=300s', ttl=300
2025-06-04 16:30:11.536 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-04 16:30:12.386 [main] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-06-04 16:30:13.434 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8102"]
2025-06-04 16:30:13.444 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8102 (http) with context path '/api/meiye'
2025-06-04 16:30:13.447 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-06-04 16:30:13.451 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-06-04 16:30:13.476 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-06-04 16:30:13.658 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getFileUrlUsingPOST_1
2025-06-04 16:30:13.811 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_1
2025-06-04 16:30:13.830 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-06-04 16:30:13.845 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_2
2025-06-04 16:30:13.879 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_2
2025-06-04 16:30:13.880 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_3
2025-06-04 16:30:13.881 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: batchRemoveUsingPOST_1
2025-06-04 16:30:13.883 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: changeUsingPOST_1
2025-06-04 16:30:13.884 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2025-06-04 16:30:13.941 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_1
2025-06-04 16:30:13.944 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-06-04 16:30:13.950 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2025-06-04 16:30:13.953 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryUsingPOST_1
2025-06-04 16:30:13.958 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_2
2025-06-04 16:30:13.958 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingGET_1
2025-06-04 16:30:13.963 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2025-06-04 16:30:13.972 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_3
2025-06-04 16:30:14.032 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingGET_2
2025-06-04 16:30:14.053 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_3
2025-06-04 16:30:14.057 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryTreeRangeUsingPOST_1
2025-06-04 16:30:14.065 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_3
2025-06-04 16:30:14.070 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_4
2025-06-04 16:30:14.072 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: checkUsingPOST_1
2025-06-04 16:30:14.082 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_4
2025-06-04 16:30:14.089 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_5
2025-06-04 16:30:14.089 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: batchRemoveUsingPOST_2
2025-06-04 16:30:14.090 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPOST_1
2025-06-04 16:30:14.091 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sendApprovalUsingPOST_1
2025-06-04 16:30:14.092 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sendNotifyUsingPOST_1
2025-06-04 16:30:14.094 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: changeUsingPOST_2
2025-06-04 16:30:14.096 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2025-06-04 16:30:14.098 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: countUsingPOST_1
2025-06-04 16:30:14.106 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_5
2025-06-04 16:30:14.107 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_6
2025-06-04 16:30:14.107 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: batchRemoveUsingPOST_3
2025-06-04 16:30:14.109 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: changeUsingPOST_3
2025-06-04 16:30:14.112 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_3
2025-06-04 16:30:14.113 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingPOST_1
2025-06-04 16:30:14.128 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_6
2025-06-04 16:30:14.130 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_7
2025-06-04 16:30:14.132 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: batchRemoveUsingPOST_4
2025-06-04 16:30:14.136 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: changeUsingPOST_4
2025-06-04 16:30:14.144 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_4
2025-06-04 16:30:14.151 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_2
2025-06-04 16:30:14.158 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_7
2025-06-04 16:30:14.213 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_8
2025-06-04 16:30:14.215 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: batchRemoveUsingPOST_5
2025-06-04 16:30:14.216 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: changeUsingPOST_5
2025-06-04 16:30:14.220 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_5
2025-06-04 16:30:14.255 [main] INFO  cn.pf.orch.Application - Started Application in 9.28 seconds (JVM running for 10.285)
2025-06-04 16:30:14.641 [RMI TCP Connection(3)-127.0.0.1] INFO  o.a.c.c.C.[.[localhost].[/api/meiye] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-04 16:30:14.642 [RMI TCP Connection(3)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-04 16:30:14.643 [RMI TCP Connection(3)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-04 16:30:16.990 [SpringApplicationShutdownHook] INFO  o.d.x.f.s.core.FileStorageService - 销毁存储平台 minio-1 成功
2025-06-04 17:23:23.512 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-04 17:23:23.524 [main] INFO  cn.pf.orch.Application - Starting Application using Java 1.8.0_401 on guoxiaoleideMacBook-Pro.local with PID 53362 (/Users/<USER>/IdeaProjects/workspace/pf-orch-meiye/pf-orch-meiye-service/target/classes started by gxl in /Users/<USER>/IdeaProjects/workspace/pf-orch-meiye)
2025-06-04 17:23:23.524 [main] INFO  cn.pf.orch.Application - The following 1 profile is active: "dev"
2025-06-04 17:23:24.369 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 17:23:24.370 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 17:23:24.395 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-06-04 17:23:24.598 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=f6c70ece-5277-35ed-9eb6-b79ca9147cb1
2025-06-04 17:23:24.989 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8102 (http)
2025-06-04 17:23:24.997 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8102"]
2025-06-04 17:23:24.997 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-04 17:23:24.997 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.75]
2025-06-04 17:23:25.107 [main] INFO  o.a.c.c.C.[.[localhost].[/api/meiye] - Initializing Spring embedded WebApplicationContext
2025-06-04 17:23:25.107 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1519 ms
2025-06-04 17:23:25.769 [main] INFO  org.redisson.Version - Redisson 3.17.6
2025-06-04 17:23:25.784 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-06-04 17:23:26.017 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 10 connections initialized for **********/**********:6379
2025-06-04 17:23:26.080 [redisson-netty-2-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 10 connections initialized for **********/**********:6379
2025-06-04 17:23:26.376 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-04 17:23:26.545 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-04 17:23:27.333 [main] INFO  o.d.x.f.s.c.FileStorageServiceBuilder - 加载 MinIO 存储平台：minio-1
2025-06-04 17:23:28.272 [main] INFO  c.g.c.c.c.CaffeineMultiCacheManager - Create CaffeineCacheManager with names=[genn:cache:caffeine_manager], cacheSpec='maximumSize=10000,expireAfterWrite=300s', ttl=300
2025-06-04 17:23:28.398 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-04 17:23:28.905 [main] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-06-04 17:23:29.516 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8102"]
2025-06-04 17:23:29.523 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8102 (http) with context path '/api/meiye'
2025-06-04 17:23:29.526 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-06-04 17:23:29.529 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-06-04 17:23:29.548 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-06-04 17:23:29.658 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getFileUrlUsingPOST_1
2025-06-04 17:23:29.681 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_1
2025-06-04 17:23:29.691 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-06-04 17:23:29.700 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_2
2025-06-04 17:23:29.716 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_2
2025-06-04 17:23:29.717 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_3
2025-06-04 17:23:29.718 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: batchRemoveUsingPOST_1
2025-06-04 17:23:29.719 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: changeUsingPOST_1
2025-06-04 17:23:29.721 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_1
2025-06-04 17:23:29.752 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_1
2025-06-04 17:23:29.755 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-06-04 17:23:29.761 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2025-06-04 17:23:29.764 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryUsingPOST_1
2025-06-04 17:23:29.770 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_2
2025-06-04 17:23:29.770 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingGET_1
2025-06-04 17:23:29.774 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2025-06-04 17:23:29.783 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_3
2025-06-04 17:23:29.784 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingGET_2
2025-06-04 17:23:29.785 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_3
2025-06-04 17:23:29.789 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: queryTreeRangeUsingPOST_1
2025-06-04 17:23:29.797 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_3
2025-06-04 17:23:29.803 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_4
2025-06-04 17:23:29.804 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: checkUsingPOST_1
2025-06-04 17:23:29.811 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_4
2025-06-04 17:23:29.819 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_5
2025-06-04 17:23:29.819 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: batchRemoveUsingPOST_2
2025-06-04 17:23:29.819 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: closeUsingPOST_1
2025-06-04 17:23:29.821 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sendApprovalUsingPOST_1
2025-06-04 17:23:29.822 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sendNotifyUsingPOST_1
2025-06-04 17:23:29.823 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: changeUsingPOST_2
2025-06-04 17:23:29.825 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_2
2025-06-04 17:23:29.827 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: countUsingPOST_1
2025-06-04 17:23:29.833 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_5
2025-06-04 17:23:29.834 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_6
2025-06-04 17:23:29.834 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: batchRemoveUsingPOST_3
2025-06-04 17:23:29.835 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: changeUsingPOST_3
2025-06-04 17:23:29.837 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_3
2025-06-04 17:23:29.837 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingPOST_1
2025-06-04 17:23:29.842 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_6
2025-06-04 17:23:29.843 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_7
2025-06-04 17:23:29.843 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: batchRemoveUsingPOST_4
2025-06-04 17:23:29.844 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: changeUsingPOST_4
2025-06-04 17:23:29.846 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_4
2025-06-04 17:23:29.851 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_2
2025-06-04 17:23:29.853 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_7
2025-06-04 17:23:29.880 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_8
2025-06-04 17:23:29.880 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: batchRemoveUsingPOST_5
2025-06-04 17:23:29.881 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: changeUsingPOST_5
2025-06-04 17:23:29.883 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: saveUsingPOST_5
2025-06-04 17:23:29.902 [main] INFO  cn.pf.orch.Application - Started Application in 7.701 seconds (JVM running for 8.949)
2025-06-04 17:23:30.430 [RMI TCP Connection(3)-127.0.0.1] INFO  o.a.c.c.C.[.[localhost].[/api/meiye] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-04 17:23:30.430 [RMI TCP Connection(3)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-04 17:23:30.432 [RMI TCP Connection(3)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-04 17:23:38.669 [SpringApplicationShutdownHook] INFO  o.d.x.f.s.core.FileStorageService - 销毁存储平台 minio-1 成功
