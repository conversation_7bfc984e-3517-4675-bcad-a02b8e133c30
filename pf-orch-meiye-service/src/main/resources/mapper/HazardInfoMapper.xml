<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.pf.orch.meiye.mapper.HazardInfoMapper">
    <select id="selectByPage" resultType="cn.pf.orch.meiye.domain.hazard.dto.HazardInfoPageDTO">
        select ri.*,
        mar.address_id as address_id,
        mc.name as company_name,
        ml.name as address_name,
        ml.full_path_name as full_address_name,
        mar.address_detail as address_detail,
        rid.name as identify_name,
        rid.identify_type,
        rid.identify_detail,
        rid.start_time as identify_time
        from hazard_info ri
        inner join my_company mc on ri.company_id=mc.id
        inner join my_address_rel mar on ri.id=mar.biz_id and mar.biz_type='hazard'
        inner join hazard_identify rid on ri.hazard_identify_id=rid.id
        inner join meiye_location ml on ml.id=mar.address_id
        <where>
            <if test="query.companyIds != null and query.companyIds.size() > 0">
                ri.company_id in
                <foreach collection="query.companyIds" item="companyId" open="(" separator="," close=")">
                    #{companyId}
                </foreach>
            </if>
            <if test="query.code != null and query.code !='' ">
                and ri.code = #{query.code}
            </if>
            <if test="query.companyName != null and query.companyName !='' ">
                and mc.name like concat('%', #{query.companyName}, '%')
            </if>
            <if test="query.identifyName != null and query.identifyName !='' ">
                and rid.name like concat('%', #{query.identifyName}, '%')
            </if>
            <if test="query.remark != null and query.remark !='' ">
                and ri.remark like concat('%', #{query.remark}, '%')
            </if>
            <if test="query.level != null ">
                and ri.level = #{query.level}
            </if>
            <if test="query.hazardType != null">
                and ri.type = #{query.hazardType}
            </if>
            <if test="query.hazardStatus != null">
                and ri.status = #{query.hazardStatus}
            </if>
            <if test="query.controlStatus != null">
                and ri.control_status = #{query.controlStatus}
            </if>
            <if test="query.ownerName != null and query.ownerName !='' ">
                and ri.owner_name like concat('%', #{query.ownerName}, '%')
            </if>
            <if test="query.departmentName != null and query.departmentName !='' ">
                and ri.department_name like concat('%', #{query.departmentName}, '%')
            </if>
            <if test="query.rectifyOwnerName != null and query.rectifyOwnerName !='' ">
                and ri.rectify_owner_name like concat('%', #{query.rectifyOwnerName}, '%')
            </if>
            <if test="query.startTime != null">
                AND rid.start_time &gt;= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                AND rid.start_time &lt;= #{query.endTime}
            </if>
        </where>
        order by ri.id desc
    </select>

    <select id="selectDetailById" parameterType="long" resultType="cn.pf.orch.meiye.domain.hazard.dto.HazardInfoDTO">
        select ri.*,
               mar.address_id as address_id,
               mc.`name` as company_name,
               ml.`name` as address_name,
               ml.full_path_name as full_address_name,
               mar.address_detail as address_detail,
               rid.`name` as identify_name,
               rid.identify_type,
               rid.identify_detail,
               rid.start_time as identify_time
        from hazard_info ri
                 inner join my_company mc on ri.company_id=mc.id
                 inner join my_address_rel mar on ri.id=mar.biz_id and mar.biz_type='hazard'
                 inner join hazard_identify rid on ri.hazard_identify_id=rid.id
                 inner join meiye_location ml on ml.id=mar.address_id
        where ri.id = #{id}
    </select>

    <select id="selectLevelCount" resultType="cn.pf.orch.meiye.domain.hazard.dto.HazardCountDTO">
        select ri.`level`, COUNT(*) AS `count`
        from hazard_info ri
        inner join my_company mc on ri.company_id=mc.id
        inner join my_address_rel mar on ri.id=mar.biz_id and mar.biz_type='hazard'
        inner join hazard_identify rid on ri.hazard_identify_id=rid.id
        <where>
            <if test="query.companyIds != null and query.companyIds.size() > 0">
                ri.company_id in
                <foreach collection="query.companyIds" item="companyId" open="(" separator="," close=")">
                    #{companyId}
                </foreach>
            </if>
            <if test="query.code != null and query.code !='' ">
                and ri.code  = #{query.code}
            </if>
            <if test="query.companyName != null and query.companyName !='' ">
                and mc.name like concat('%', #{query.companyName}, '%')
            </if>
            <if test="query.identifyName != null and query.identifyName !='' ">
                and rid.name like concat('%', #{query.identifyName}, '%')
            </if>
            <if test="query.level != null ">
                and ri.level = #{query.level}
            </if>
            <if test="query.hazardType != null">
                and ri.type = #{query.hazardType}
            </if>
            <if test="query.hazardStatus != null">
                and ri.status  = #{query.hazardStatus}
            </if>
            <if test="query.controlStatus != null">
                and ri.control_status  = #{query.controlStatus}
            </if>
            <if test="query.ownerName != null and query.ownerName !='' ">
                and ri.owner_name like concat('%', #{query.ownerName}, '%')
            </if>
            <if test="query.startTime != null">
                AND rid.start_time &gt;= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                AND rid.start_time &lt;= #{query.startTime}
            </if>
        </where>
        group by ri.`level`
    </select>
</mapper>