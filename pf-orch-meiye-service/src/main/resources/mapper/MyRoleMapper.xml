<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.pf.orch.meiye.mapper.MyRoleMapper">

    <select id="selectByPage" resultType="cn.pf.orch.meiye.domain.system.dto.MyRoleDTO">
        select mr.*,mc.name as company_name
        from my_role mr left join my_company mc on mr.company_id = mc.id
            <where>
                mr.deleted=0 and mr.company_id in
                <foreach collection="companyIds" item="companyId" open="(" close=")" separator=",">
                    #{companyId}
                </foreach>
                <if test="query.name != null and query.name != ''">
                    and mr.name like concat('%', #{query.name}, '%')
                </if>
                <if test="query.authType != null">
                    and mr.auth_type = #{query.authType}
                </if>
                <if test="admin != null and admin == true">
                    and mr.type in ("system","customize")
                </if>
                <if test="admin != null and admin == false">
                    and mr.type = "customize"
                </if>
            </where>
    </select>

    <select id="selectDTOById" resultType="cn.pf.orch.meiye.domain.system.dto.MyRoleDTO">
        select mr.*,mc.name as company_name
        from my_role mr left join my_company mc on mr.company_id = mc.id
        where mr.id = #{id} and mr.deleted = 0
    </select>

    <select id="selectByUserIds" resultType="cn.pf.orch.meiye.domain.system.dto.MyUserRoleRelDTO">
        select murr.*,mr.name as role_name,mr.auth_type
            from my_user_role_rel murr
            left join my_role mr on murr.role_id = mr.id
        <where>
            and murr.user_id in
            <foreach collection="userIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            and murr.role_type in ('system','customize')
        </where>
    </select>

</mapper>