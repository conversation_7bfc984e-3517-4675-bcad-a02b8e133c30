<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.pf.orch.meiye.mapper.HazardCheckMapper">
    
    <select id="selectByPage" resultType="cn.pf.orch.meiye.domain.hazard.dto.HazardCheckPageDTO">
        SELECT
        rc.*,
        ri.code,
        rid.id as hazard_identify_id,
        ri.remark,
        ri.level as hazard_level,
        ri.type as hazard_type,
        ri.status,
        ri.control_status,
        ri.owner,
        ri.owner_name,
        ri.create_time as hazard_create_time,
        ri.approval_id,
        ri.department_id,
        ri.department_name,
        ri.rectify_money,
        ri.rectify_time,
        ri.rectify_owner,
        ri.rectify_owner_name,
        mar.address_id,
        rm.content,
        mc.`name` AS company_name,
        ml.`name` AS address_name,
        ml.full_path_name AS full_address_name,
        mar.address_detail AS address_detail,
        rid.`name` AS identify_name,
        rid.identify_type,
        rid.identify_detail,
        rid.start_time AS identify_time
        FROM hazard_check rc
        INNER JOIN hazard_measure rm ON rc.hazard_measure_id = rm.id
        INNER JOIN hazard_info ri ON rc.hazard_id = ri.id
        INNER JOIN my_company mc ON ri.company_id = mc.id
        INNER JOIN my_address_rel mar ON ri.id = mar.biz_id AND mar.biz_type = 'hazard'
        INNER JOIN hazard_identify rid ON ri.hazard_identify_id = rid.id
        INNER JOIN meiye_location ml ON ml.id = mar.address_id
        <where>
            <if test="query.companyIds != null and query.companyIds.size() > 0">
                rc.company_id in
                <foreach collection="query.companyIds" item="companyId" open="(" separator="," close=")">
                    #{companyId}
                </foreach>
            </if>
            <if test="query.code != null and query.code !='' ">
                and ri.code = #{query.code}
            </if>
            <if test="query.companyName != null and query.companyName !='' ">
                and mc.name like concat('%', #{query.companyName}, '%')
            </if>
            <if test="query.identifyName != null and query.identifyName !='' ">
                and rid.name like concat('%', #{query.identifyName}, '%')
            </if>
            <if test="query.hazardLevel != null">
                and ri.level = #{query.hazardLevel}
            </if>
            <if test="query.hazardType != null">
                and ri.type = #{query.hazardType}
            </if>
            <if test="query.controlStatus != null">
                and ri.control_status = #{query.controlStatus}
            </if>
            <if test="query.ownerName != null and query.ownerName !='' ">
                and ri.owner_name like concat('%', #{query.ownerName}, '%')
            </if>
            <if test="query.identifyStartTime != null">
                AND rid.start_time &gt;= #{query.identifyStartTime}
            </if>
            <if test="query.identifyEndTime != null">
                AND rid.start_time &lt;= #{query.identifyEndTime}
            </if>
            <if test="query.measureContent != null and query.measureContent !='' ">
                and rm.content like concat('%', #{query.measureContent}, '%')
            </if>
            <if test="query.checkLevel != null">
                and rc.level = #{query.checkLevel}
            </if>
            <if test="query.result != null">
                and rc.result = #{query.result}
            </if>
            <if test="query.checkUserName != null and query.checkUserName !='' ">
                and rc.create_user_name like concat('%', #{query.checkUserName}, '%')
            </if>
            <if test="query.checkStartTime != null">
                AND rid.create_time &gt;= #{query.checkStartTime}
            </if>
            <if test="query.checkEndTime != null">
                AND rid.create_time &lt;= #{query.checkEndTime}
            </if>
            <if test="query.search != null and query.search !='' ">
                AND (rm.content like concat('%', #{query.search}, '%') or ri.remark like concat('%', #{query.search}, '%'))
            </if>

        </where>
        order by rc.id desc
    </select>

    <select id="selectDetailById" resultType="cn.pf.orch.meiye.domain.hazard.dto.HazardCheckDTO">
        SELECT
            rc.*,
            rc.files as file_str,
            ri.code,
            rid.id as hazard_identify_id,
            ri.remark,
            ri.level as hazard_level,
            ri.type as hazard_type,
            ri.status,
            ri.control_status,
            ri.owner,
            ri.owner_name,
            ri.create_time as hazard_create_time,
            ri.approval_id,
            ri.department_id,
            ri.department_name,
            ri.rectify_money,
            ri.rectify_time,
            ri.rectify_owner,
            ri.rectify_owner_name,
            mar.address_id,
            rm.content,
            mc.`name` AS company_name,
            ml.`name` AS address_name,
            ml.full_path_name AS full_address_name,
            mar.address_detail AS address_detail,
            rid.`name` AS identify_name,
            rid.identify_type,
            rid.identify_detail,
            rid.start_time AS identify_time

        FROM hazard_check rc
                 INNER JOIN hazard_measure rm ON rc.hazard_measure_id = rm.id
                 INNER JOIN hazard_info ri ON rc.hazard_id = ri.id
                 INNER JOIN my_company mc ON ri.company_id = mc.id
                 INNER JOIN my_address_rel mar ON ri.id = mar.biz_id AND mar.biz_type = 'hazard'
                 INNER JOIN hazard_identify rid ON ri.hazard_identify_id = rid.id
                 INNER JOIN meiye_location ml ON ml.id = mar.address_id
        where rc.id = #{id}
    </select>
</mapper>