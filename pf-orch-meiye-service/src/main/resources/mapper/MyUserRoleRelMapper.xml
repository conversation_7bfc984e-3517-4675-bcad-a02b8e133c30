<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.pf.orch.meiye.mapper.MyUserRoleRelMapper">

    <insert id="saveBatch" parameterType="java.util.List">
        insert into my_user_role_rel (user_id, role_id,role_type)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.userId}, #{item.roleId}, #{item.roleType})
        </foreach>
    </insert>
</mapper>