<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.pf.orch.meiye.mapper.RiskInfoMapper">

    <select id="selectByPage" resultType="cn.pf.orch.meiye.domain.risk.dto.RiskInfoPageDTO">
        select ri.*,
            mar.address_id as address_id,
            mc.name as company_name,
            ml.name as address_name,
            ml.full_path_name as full_address_name,
            mar.address_detail as address_detail,
            rid.name as identify_name,
            rid.identify_type,
            rid.identify_detail,
            rid.start_time as identify_time
        from risk_info ri
        inner join my_company mc on ri.company_id=mc.id
        inner join my_address_rel mar on ri.id=mar.biz_id and mar.biz_type='risk'
        inner join risk_identify rid on ri.risk_identify_id=rid.id
        inner join meiye_location ml on ml.id=mar.address_id
        <where>
            <if test="query.companyIds != null and query.companyIds.size() > 0">
                ri.company_id in
                <foreach collection="query.companyIds" item="companyId" open="(" separator="," close=")">
                    #{companyId}
                </foreach>
            </if>
            <if test="query.code != null and query.code !='' ">
                and ri.code = #{query.code}
            </if>
            <if test="query.companyName != null and query.companyName !='' ">
                and mc.name like concat('%', #{query.companyName}, '%')
            </if>
            <if test="query.identifyName != null and query.identifyName !='' ">
                and rid.name like concat('%', #{query.identifyName}, '%')
            </if>
            <if test="query.remark != null and query.remark !='' ">
                and ri.remark like concat('%', #{query.remark}, '%')
            </if>
            <if test="query.level != null ">
                and ri.level = #{query.level}
            </if>
            <if test="query.riskType != null">
                and ri.type = #{query.riskType}
            </if>
            <if test="query.riskStatus != null">
                and ri.status = #{query.riskStatus}
            </if>
            <if test="query.controlStatus != null">
                and ri.control_status = #{query.controlStatus}
            </if>
            <if test="query.ownerName != null and query.ownerName !='' ">
                and ri.owner_name like concat('%', #{query.ownerName}, '%')
            </if>
            <if test="query.startTime != null">
                AND rid.start_time &gt;= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                AND rid.start_time &lt;= #{query.endTime}
            </if>
        </where>
        order by ri.id desc
    </select>

    <select id="selectDetailById" parameterType="long" resultType="cn.pf.orch.meiye.domain.risk.dto.RiskInfoDTO">
        select ri.*,
               mar.address_id as address_id,
               mc.`name` as company_name,
               ml.`name` as address_name,
               ml.full_path_name as full_address_name,
               mar.address_detail as address_detail,
               rid.`name` as identify_name,
               rid.identify_type,
               rid.identify_detail,
               rid.start_time as identify_time
        from risk_info ri
                 inner join my_company mc on ri.company_id=mc.id
                 inner join my_address_rel mar on ri.id=mar.biz_id and mar.biz_type='risk'
                 inner join risk_identify rid on ri.risk_identify_id=rid.id
                 inner join meiye_location ml on ml.id=mar.address_id
        where ri.id = #{id}
    </select>

    <select id="selectLevelCount" resultType="cn.pf.orch.meiye.domain.risk.dto.RiskCountDTO">
        select ri.`level`, COUNT(*) AS `count`
        from risk_info ri
                 inner join my_company mc on ri.company_id=mc.id
                 inner join my_address_rel mar on ri.id=mar.biz_id and mar.biz_type='risk'
                 inner join risk_identify rid on ri.risk_identify_id=rid.id
        <where>
            <if test="query.companyIds != null and query.companyIds.size() > 0">
                ri.company_id in
                <foreach collection="query.companyIds" item="companyId" open="(" separator="," close=")">
                    #{companyId}
                </foreach>
            </if>
            <if test="query.code != null and query.code !='' ">
                and ri.code  = #{query.code}
            </if>
            <if test="query.companyName != null and query.companyName !='' ">
                and mc.name like concat('%', #{query.companyName}, '%')
            </if>
            <if test="query.identifyName != null and query.identifyName !='' ">
                and rid.name like concat('%', #{query.identifyName}, '%')
            </if>
            <if test="query.level != null ">
                and ri.level = #{query.level}
            </if>
            <if test="query.riskType != null">
                and ri.type = #{query.riskType}
            </if>
            <if test="query.riskStatus != null">
                and ri.status  = #{query.riskStatus}
            </if>
            <if test="query.controlStatus != null">
                and ri.control_status  = #{query.controlStatus}
            </if>
            <if test="query.ownerName != null and query.ownerName !='' ">
                and ri.owner_name like concat('%', #{query.ownerName}, '%')
            </if>
            <if test="query.startTime != null">
                AND rid.start_time &gt;= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                AND rid.start_time &lt;= #{query.startTime}
            </if>
        </where>
        group by ri.`level`
    </select>

    <select id="homeTodoPage" resultType="cn.pf.orch.meiye.domain.system.dto.HomeTodoDTO">
        (
        SELECT ri.id as biz_id,
        'risk' as biz_type,
        ri.code,
        ri.status,
        ri.level,
        ri.type,
        ri.remark,
        ri.company_id,
        mc.name as company_name,
        ml.`name` as address_name,
        ml.full_path_name as full_address_name,
        mar.address_detail,
        ri.create_time,
        ri.control_status,
        ri.owner,
        ri.owner_name,
        '' as department_id,
        '' as department_name,
        '' as rectify_owner,
        '' as rectify_owner_name
        FROM risk_info ri
        inner join my_company mc on ri.company_id=mc.id
        inner join my_address_rel mar on ri.id=mar.biz_id and mar.biz_type='risk'
        inner join meiye_location ml on ml.id=mar.address_id
        <where>
            ri.status &lt;= 4
            and (
            ri.owner = #{query.openId} or ri.create_user_id = #{query.openId} or ri.update_user_id = #{query.openId}
            )
            and ri.deleted = 0
            <if test="query.code != null and query.code !='' ">
                and ri.code = #{query.code}
            </if>
            <if test="query.remark != null and query.remark !='' ">
                and ri.remark like concat('%', #{query.remark}, '%')
            </if>
            <if test="query.companyIds != null and query.companyIds.size() > 0">
                and ri.company_id in
                <foreach collection="query.companyIds" item="companyId" open="(" separator="," close=")">
                    #{companyId}
                </foreach>
            </if>
        </where>
        )
        UNION ALL
        (
        SELECT ri.id as biz_id,
        'hazard' as biz_type,
        ri.code,
        ri.status,
        ri.level,
        ri.type,
        ri.remark,
        ri.company_id,
        mc.name as company_name,
        ml.`name` as address_name,
        ml.full_path_name as full_address_name,
        mar.address_detail,
        ri.create_time,
        ri.control_status,
        ri.owner,
        ri.owner_name,
        ri.department_id,
        ri.department_name,
        ri.rectify_owner,
        ri.rectify_owner_name
        FROM hazard_info ri
        inner join my_company mc on ri.company_id=mc.id
        inner join my_address_rel mar on ri.id=mar.biz_id and mar.biz_type='hazard'
        inner join meiye_location ml on ml.id=mar.address_id
        <where>
            ri.status &lt;= 4
            and (
            ri.owner = #{query.openId} or ri.create_user_id = #{query.openId} or ri.update_user_id = #{query.openId}
            )
            and ri.deleted = 0
            <if test="query.code != null and query.code !='' ">
                and ri.code = #{query.code}
            </if>
            <if test="query.remark != null and query.remark !='' ">
                and ri.remark like concat('%', #{query.remark}, '%')
            </if>
            <if test="query.companyIds != null and query.companyIds.size() > 0">
                and ri.company_id in
                <foreach collection="query.companyIds" item="companyId" open="(" separator="," close=")">
                    #{companyId}
                </foreach>
            </if>
        </where>
        )
        ORDER BY create_time DESC
    </select>
</mapper>