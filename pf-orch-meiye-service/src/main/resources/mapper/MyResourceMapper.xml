<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.pf.orch.meiye.mapper.MyResourceMapper">



    <select id="selectByRoleId" parameterType="java.lang.Long" resultType="cn.pf.orch.meiye.domain.system.po.MyResourcePO">
        select *
        from my_resource
        <where>
            id in (
                select resource_id from my_role_resource_rel where role_id =#{roleId}
            )
        </where>
    </select>

    <select id="selectByOpenId" parameterType="java.lang.String" resultType="cn.pf.orch.meiye.domain.system.po.MyResourcePO">
        select distinct mr.*
        from my_resource mr
        left join my_role_resource_rel mrr on mr.id = mrr.resource_id
        left join my_user_role_rel mur on mrr.role_id = mur.role_id
        left join my_user mu on mur.user_id = mu.id
        where mu.id = #{userId}
        and mr.deleted = 0


    </select>

</mapper>