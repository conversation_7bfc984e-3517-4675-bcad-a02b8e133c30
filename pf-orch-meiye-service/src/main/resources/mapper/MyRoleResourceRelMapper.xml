<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.pf.orch.meiye.mapper.MyRoleResourceRelMapper">

    <delete id="deleteByRoleId" parameterType="java.lang.Long">
        delete from my_role_resource_rel
        <where>
            role_id = #{roleId}
        </where>
    </delete>

    <insert id="saveBatch" parameterType="list">
        INSERT INTO my_role_resource_rel (
        resource_id,
        role_id
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.resourceId},
            #{item.roleId})
        </foreach>
    </insert>

</mapper>