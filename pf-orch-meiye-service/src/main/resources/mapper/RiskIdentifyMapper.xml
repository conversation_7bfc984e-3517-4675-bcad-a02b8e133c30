<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.pf.orch.meiye.mapper.RiskIdentifyMapper">


    <select id="selectByPage" resultType="cn.pf.orch.meiye.domain.risk.dto.RiskIdentifyDTO">
        select ri.*,mc.name as company_name
        from risk_identify ri inner join my_company mc on ri.company_id = mc.id
        <where>
            ri.deleted=0
            <if test="query.companyIds != null and query.companyIds.size() > 0">
                and ri.company_id in
                <foreach collection="query.companyIds" item="companyId" open="(" separator="," close=")">
                    #{companyId}
                </foreach>
            </if>
            <if test="query.companyName != null and query.companyName != ''">
                and mc.name like concat('%', #{query.companyName}, '%')
            </if>
            <if test="query.name != null and query.name != ''">
                and ri.name like concat('%', #{query.name}, '%')
            </if>
            <if test="query.identifyType != null">
                and ri.identify_type = #{query.identifyType}
            </if>
            <if test="query.identifyDetail != null">
                and ri.identify_detail = #{query.identifyDetail}
            </if>
            <if test="query.status != null">
                and ri.status = #{query.status}
            </if>
            <if test="query.identifyOwnerName != null">
                and ri.identify_owner_name like concat('%', #{query.identifyOwnerName}, '%')
            </if>
            <if test="query.startTime != null">
                AND ri.start_time &gt;= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                AND ri.start_time &lt;= #{query.endTime}
            </if>
        </where>
        order by ri.id desc
    </select>
</mapper>