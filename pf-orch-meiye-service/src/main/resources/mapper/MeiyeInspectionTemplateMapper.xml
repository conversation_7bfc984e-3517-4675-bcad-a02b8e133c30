<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.pf.orch.meiye.mapper.MeiyeInspectionTemplateMapper">

    <select id="queryEmergencyCheckInfo" resultType="cn.pf.orch.meiye.domain.emergencyrescuecheck.dto.EmergencyRescueCheckInfoDTO">
        SELECT
        mc.id AS companyId,
        mc.name AS companyName,
        mit.id AS templateId,
        mit.inspection_type AS inspectionType,
        mit.serial_number AS serialNumber,
        mit.materials_methods AS materialsMethods,
        mit.project AS project,
        mcpr.description AS description,
        mit.reference_link AS referenceLink,
        mit.content AS content,
        mcpr.id AS problemId,
        mcpr.create_time AS createTime,
        mcpr.create_user_id AS createUserId,
        mcpr.create_user_name AS createUserName,
        mcpr.update_time AS updateTime,
        mcpr.update_user_id AS updateUserId,
        mcpr.update_user_name AS updateUserName,
        mcpr.attachment
        FROM
        (my_company mc CROSS JOIN meiye_inspection_template mit)
        LEFT JOIN (SELECT * FROM meiye_company_problem_recode WHERE del = 0) mcpr
        ON mc.id = mcpr.company_id
        AND mit.id = mcpr.emergency_rescue_check_temp_id
        WHERE
        mc.auth_type = 3
        AND (IF(#{query.companyId} IS NULL OR #{query.companyId} = '', 1=1, mc.id = #{query.companyId}))
        AND (IF(#{query.inspectionType} IS NULL OR #{query.inspectionType} = '', 1=1, mit.inspection_type = #{query.inspectionType}))
        AND (IF(#{query.project} IS NULL OR #{query.project} = '', 1=1, mit.project = #{query.project}))
        AND (IF(#{query.content} IS NULL OR #{query.content} = '', 1=1, mit.content LIKE CONCAT('%', #{query.content}, '%')))
        AND (IF(#{query.description} IS NULL OR #{query.description} = '', 1=1, mcpr.description LIKE CONCAT('%', #{query.description}, '%')))
        AND (IF(#{query.createTimeStart} IS NULL, 1=1, mcpr.create_time >= #{query.createTimeStart}))
        AND (IF(#{query.createTimeEnd} IS NULL, 1=1, mcpr.create_time &lt;= #{query.createTimeEnd}))
        AND (IF(#{query.problemId} IS NULL, 1=1, mcpr.id = #{query.problemId}))
        AND (
        (IF(#{query.keywords} IS NULL OR #{query.keywords} = '', 1=1, mit.inspection_type LIKE CONCAT('%', #{query.keywords}, '%')))
        OR (IF(#{query.keywords} IS NULL OR #{query.keywords} = '', 1=1, mit.content LIKE CONCAT('%', #{query.keywords}, '%')))
        OR (IF(#{query.keywords} IS NULL OR #{query.keywords} = '', 1=1, mcpr.description LIKE CONCAT('%', #{query.keywords}, '%')))
        )
        AND (IF(#{query.source} = 'web', 1=1, mcpr.id IS NOT NULL))
        <if test="query.rangeCompanyIds != null">
        and mc.id in
            <foreach item="item" collection="query.rangeCompanyIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY
        mcpr.create_time DESC
    </select>
</mapper>