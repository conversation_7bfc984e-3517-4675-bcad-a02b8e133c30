<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.pf.orch.meiye.mapper.MyCompanyMapper">

    <select id="selectByUserId" resultType="cn.pf.orch.meiye.domain.system.po.MyCompanyPO">
        select * from my_company where id = (
            select company_id from my_user where id = #{userId}
        )
    </select>

    <select id="selectByDepartmentIds" resultType="cn.pf.orch.meiye.domain.system.po.MyCompanyPO">
        select * from my_company
        <where>
            <foreach collection="departmentIds" separator=" OR " open="(" close=")" item="item">
                FIND_IN_SET(#{item}, department_ids)
            </foreach>
        </where>
    </select>
</mapper>