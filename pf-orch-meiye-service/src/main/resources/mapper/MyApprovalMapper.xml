<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.pf.orch.meiye.mapper.MyApprovalMapper">


    <select id="homeMessagePage" resultType="cn.pf.orch.meiye.domain.system.dto.HomeMessagesDTO">
        (
        SELECT
        ma.id as approval_id,
        ma.biz_id,
        ma.biz_type,
        ri.code,
        ri.remark,
        ma.operate_time,
        CASE
            WHEN ma.status = 'APPROVED' THEN '审核通过'
            WHEN ma.status = 'REJECTED' THEN '被驳回'
            ELSE '被驳回'
        END AS `status`
        FROM my_approval ma
        INNER JOIN risk_info ri ON ma.biz_id = ri.id and ma.biz_type = 'risk'
        <where>
            ma.status in ('APPROVED','REJECTED')
            and (
            ri.owner = #{query.openId} or ri.create_user_id = #{query.openId} or ri.update_user_id = #{query.openId}
            )
            and ma.operate_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
            and ri.deleted = 0
            <if test="query.code != null and query.code !='' ">
                and ri.code = #{query.code}
            </if>
            <if test="query.remark != null and query.remark !='' ">
                and ri.remark like concat('%', #{query.remark}, '%')
            </if>
            <if test="query.companyIds != null and query.companyIds.size() > 0">
                and ri.company_id in
                <foreach collection="query.companyIds" item="companyId" open="(" separator="," close=")">
                    #{companyId}
                </foreach>
            </if>
        </where>
        )
        UNION ALL
        (
        SELECT
        ma.id as approval_id,
        ma.biz_id,
        ma.biz_type,
        ri.code,
        ri.remark,
        ma.operate_time,
        CASE
        WHEN ma.status = 'APPROVED' THEN '审核通过'
        WHEN ma.status = 'REJECTED' THEN '被驳回'
        ELSE '被驳回'
        END AS `status`
        FROM my_approval ma
        INNER JOIN hazard_info ri ON ma.biz_id = ri.id and ma.biz_type = 'hazard'
        <where>
            ma.status in ('APPROVED','REJECTED')
            and (
            ri.owner = #{query.openId} or ri.create_user_id = #{query.openId} or ri.update_user_id = #{query.openId}
            )
            and ma.operate_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
            and ri.deleted = 0
            <if test="query.code != null and query.code !='' ">
                and ri.code = #{query.code}
            </if>
            <if test="query.remark != null and query.remark !='' ">
                and ri.remark like concat('%', #{query.remark}, '%')
            </if>
            <if test="query.companyIds != null and query.companyIds.size() > 0">
                and ri.company_id in
                <foreach collection="query.companyIds" item="companyId" open="(" separator="," close=")">
                    #{companyId}
                </foreach>
            </if>
        </where>
        )
        ORDER BY operate_time DESC
    </select>
</mapper>