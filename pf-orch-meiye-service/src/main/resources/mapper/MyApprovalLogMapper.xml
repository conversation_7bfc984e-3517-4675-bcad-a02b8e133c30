<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.pf.orch.meiye.mapper.MyApprovalLogMapper">

    <select id="selectLastApprovalIds" resultType="cn.pf.orch.meiye.domain.risk.po.MyApprovalLogPO">
        SELECT *
        FROM my_approval_log a
        WHERE a.id IN (
            SELECT MAX(id)
            FROM my_approval_log
            WHERE approval_id IN
            <foreach collection="approvalIds" item="approvalId" open="(" separator="," close=")">
                #{approvalId}
            </foreach>
            GROUP BY approval_id
        );
    </select>
</mapper>