<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.pf.orch.meiye.mapper.MyUserMapper">

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO my_user (
        id, name, email, mobile, gender, auth_type, company_id, avatar_key, avatar_origin,
        employee_no,department_id, employee_type, enterprise_email, is_frozen
        ) VALUES
        <foreach collection="list" item="user" separator=",">
            (
            #{user.id}, #{user.name}, #{user.email}, #{user.mobile}, #{user.gender}, #{user.authType},
            #{user.companyId}, #{user.avatarKey}, #{user.avatarOrigin}, #{user.employeeNo},#{user.departmentId},
            #{user.employeeType}, #{user.enterpriseEmail}, #{user.isFrozen}
            )
        </foreach>
    </insert>


    <select id="queryList" resultType="cn.pf.orch.meiye.domain.system.dto.MyUserDTO">
        select mu.*,mc.name as company_name
        from my_user mu inner join my_company mc on mu.company_id = mc.id
        <where>
            and mu.deleted = 0 and mc.deleted = 0
            <if test="companyIds != null and companyIds.size() > 0">
                and mu.company_id in
                <foreach collection="companyIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.name != null and query.name != ''">
                and mu.name like concat('%', #{query.name}, '%')
            </if>
            <if test="query.authType != null">
                and mu.auth_type = #{query.authType}
            </if>
            <if test="query.approvalSign != null">
                and mu.approval_sign = #{query.approvalSign}
            </if>
        </where>
    </select>

    <delete id="deleteByIds" parameterType="list">
        delete from my_user where id in
        <foreach collection="idList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>
</mapper>