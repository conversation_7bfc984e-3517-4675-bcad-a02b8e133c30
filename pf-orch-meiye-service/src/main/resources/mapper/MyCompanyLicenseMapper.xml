<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.pf.orch.meiye.mapper.MyCompanyLicenseMapper">

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO my_company_license (
        company_id, type, name, licenses
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.companyId},
            #{item.type},
            #{item.name},
            #{item.licenses}
            )
        </foreach>
    </insert>
</mapper>