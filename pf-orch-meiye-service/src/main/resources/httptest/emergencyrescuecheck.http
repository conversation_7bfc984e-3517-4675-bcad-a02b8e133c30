### 条件查询
POST http://localhost:8102/api/meiye/emergencyrescuecheck/queryEmergencyCheckInfo
Content-Type: application/json
token: YjhmNDk2NWNjOTNiNGZmZWE5ODJhZTk5MzE1OWQ0NWI
X-Request-Source: web

{"pageNo":1,"pageSize":10,"keywords":"问题"}

### 查看检查表
GET http://localhost:8102/api/meiye/emergencyrescuecheck/checkTemplates


### 下拉
GET http://localhost:8102/api/meiye/emergencyrescuecheck/dropDown

### 新增问题
POST http://localhost:8102/api/meiye/emergencyrescuecheck/addProblem
Content-Type: application/json
token: MTRlOTliNjRkOWRkNDkwM2I1NGZjODQ1ZTBiYTAyOGE

{
  "companyId": 16,
  "emergencyRescueCheckTempId": 8,
  "description": "这是问题描述2222",
  "attachment": "key1,key2"
}

### 编辑问题
POST http://localhost:8102/api/meiye/emergencyrescuecheck/updateProblem
Content-Type: application/json

{
  "id": 1,
  "description": "这是问题新的描述",
  "attachment": "key1, key3"
}


### 安全生产标准化
GET http://localhost:8102/api/meiye/safetyGenerationStandard/detail?id=2
token: ZTE5Y2EzNTMzYzRmNDEzZjlmOTFiZDM4NTQ0ZDExMmQ222
