### 安全敏感信息add
POST http://localhost:8102/api/meiye/securitysensitive/add
Content-Type: application/json
token: NjdhODA1MDUwNDk0NGVhOGE3NzZjMzE0YTU5NzA0ZTQ

{
  "companyId": 16,
  "companyName": "煤矿1",
  "controlMeasures": "22",
  "controlResult": "222",
  "responsibleDepartment": "部门测试111111",
  "responsibleDepartmentId": "od-9b75d43fc27241202ce9d8cc0099945d",
  "responsiblePerson": "宋晓龙",
  "responsiblePersonNumber": "00097",
  "responsiblePersonOpenId": "ou_2548c8795916707054be6b8177ae4f66",
  "sensitiveCategory": 1,
  "sensitiveDescription": "22"
}

### 安全敏感信息
GET http://localhost:8102/api/meiye/securitysensitive/delete?id=2

### 安全敏感信息
POST http://localhost:8102/api/meiye/securitysensitive/update

### 查询
POST http://localhost:8102/api/meiye/securitysensitive/pageQuery
Content-Type: application/json
token: NjdhODA1MDUwNDk0NGVhOGE3NzZjMzE0YTU5NzA0ZTQ

{
}



### 事故
POST http://localhost:8102/api/meiye/safetyaccident/add
Content-Type: application/json
token: MjQxZjU4MDI2MGMxNGFhMWIyZGQ0ZmE5MGMxMmQxNWQ

{
  "accidentNature": "4",
  "accidentCategory": "4",
  "accidentName": "呃呃",
  "occurrenceTime": "2025-05-11 00:00:00",
  "reportTime": "2025-05-12 00:00:00",
  "deathToll": "2",
  "economicLoss": "2222",
  "locationId": 5,
  "minorInjury": "2",
  "seriousInjury": "2",
  "responsiblePerson": "尹亚亭",
  "responsiblePersonOpenId": "ou_410f7e0537d380d35ed1056c0a35e4f6",
  "responsiblePersonNumber": "4324242",
  "companyId": 16,
  "companyName": "煤矿1"
}

### 事故更新
POST http://localhost:8102/api/meiye/safetyaccident/update
Content-Type: application/json
token: YmU2Zjc0MTJlMTE3NDg5ZjgzYWEyNWFjNGFmZjA0NzY

{
  "id": 3,
  "deathToll": 200,
  "seriousInjury": 300,
  "minorInjury": 500
}

### 事故删除
GET http://localhost:8102/api/meiye/safetyaccident/delete?id=3
token: ZmIwY2NmYWZhZGRjNGI1YmE2YjUyNTljMjE0MmU0NDA

### 事故查询
POST http://localhost:8102/api/meiye/safetyaccident/page
Content-Type: application/json

{
  "companyId": 17
}

### 事故详情
GET http://localhost:8102/api/meiye/safetyaccident/detail?id=1
token: YTc2NDc2MTI5ZDYwNDdlY2JlNzU2MmFlZmY0NjdkZjE

### 三伟
POST http://localhost:8102/api/meiye/safetyviolation/add
Content-Type: application/json

{
  "companyId": 17,
  "compa"
  "violationDetails": "未佩戴回黄毛",
  "violationType": "1",
  "personName": "张三",
  "personDepartment": "采煤一队",
  "personJobNumber": "EMP1001",
  "personJobOpenid": "openid123",
  "personShift": "早班",
  "personPosition": "采煤工",
  "violationDate": "2023-06-20",
  "violationLocationId": 2001,
  "violationLocationName": "3号采煤工作面",
  "reportDate": "2023-06-21",
  "punishment": "停工培训3天",
  "clause": "《煤矿安全规程》第56条",
  "fine": 500.00,
  "discoveryMethod": "安全检查",
  "discoverer": "李四"
}

### 修改
POST http://localhost:8102/api/meiye/safetyviolation/update
Content-Type: application/json

{
  "id": 3,
  "companyId": 17,
  "violationDetails": "未佩戴安全帽进入作业区域11111",
  "violationType": 1,
  "personName": "张三",
  "personDepartment": "采煤一队",
  "personJobNumber": "EMP1001",
  "personJobOpenid": "openid123",
  "personShift": "早班",
  "personPosition": "采煤工",
  "violationDate": "2023-06-20",
  "violationLocationId": 1,
  "violationLocationName": "3号采煤工作面",
  "reportDate": "2023-06-21",
  "punishment": "停工培训3天",
  "clause": "《煤矿安全规程》第56条",
  "fine": 500.00,
  "discoveryMethod": "安全检查",
  "discoverer": "李四",
  "createUserId": "user001",
  "createUserName": "系统管理员",
  "updateTime": "2023-06-21 10:30:00",
  "updateUserId": "user002"
}

### 查询
POST http://localhost:8102/api/meiye/safetyviolation/query
Content-Type: application/json
token: N2Q1OTNmNjAyNjE0NDYyY2FhZjQ4NjlmYmU1ZjVhODA

{

}

### 删除
GET http://localhost:8102/api/meiye/safetyviolation/delete?id=1

### 详情
GET http://localhost:8102/api/meiye/safetyviolation/detail?id=3
token: ZmIwY2NmYWZhZGRjNGI1YmE2YjUyNTljMjE0MmU0NDA

### 应急救援检查
POST http://localhost:8102/api/meiye/emergencyrescuecheck/queryCheckTemplateDetail
Content-Type: application/json
token: YzhhODhhNjEwYzU2NGU1ZWJkYWYzODViNzViN2EzNTM

{
  "companyId": 17,
  "inspectionType": "应急救援检查表",
  "project": "应急规章制度",
  "problemId": 4
}
