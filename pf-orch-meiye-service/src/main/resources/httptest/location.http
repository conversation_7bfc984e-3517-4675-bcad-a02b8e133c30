### 地点add
POST http://localhost:8102/api/meiye/location/addLocation
Content-Type: application/json

{
  "companyId": 16,
  "companyName": "煤矿1",
  "name": "测试煤矿"
}

### 查询地址树
POST http://localhost:8102/api/meiye/location/queryAllLocationTree
Content-Type: application/json
token: MzIyNTcyZjE5YzRmNDIxMmE5YzI1OTkyZWM2NDBiYzU

{}

### 条件查询
POST http://localhost:8102/api/meiye/location/queryLocation
Content-Type: application/json
token: ZmU2ZDU5NmIxNmE5NDBkNTlkMzI5OGI1YTM1Y2I0MzA

{
}


### 更新
POST localhost:8102/api/meiye/location/updateLocation
Content-Type: application/json
token: NzMxZTZjZmU0MDllNGZhZTg2Y2E0MjA2Yzc2NjU4NjU

{
  "id": 12,
  "oldLocationName": "eee",
  "newLocationName": "111"
}

### 删除地址
POST localhost:8102/api/meiye/location/deleteLocation
Content-Type: application/json
token: ZmU2ZDU5NmIxNmE5NDBkNTlkMzI5OGI1YTM1Y2I0MzA

{
  "id": 12,
  "level": 1
}
