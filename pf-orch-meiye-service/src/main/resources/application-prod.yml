knife4j:
  enable: false
spring:
  redis:
    redisson:
      config: |
        {
          "singleServerConfig": {
            "address": "redis://***********:6379",
            "password": "dajfPR80redis@a91",
            "connectionPoolSize": 50,
            "subscriptionConnectionMinimumIdleSize": 10,
            "subscriptionConnectionPoolSize": 50,
            "connectionMinimumIdleSize": 10,
            "idleConnectionTimeout": 10000,
            "connectTimeout": 10000,
            "timeout": 3000,
            "retryAttempts": 3,
            "retryInterval": 1500,
            "database": 0
          }
        }
dromara:
  x-file-storage: #文件存储配置
    default-platform: minio-1 #默认使用的存储平台
    thumbnail-suffix: ".min.jpg"
    minio:
      - platform: minio-1 # 存储平台标识
        enable-storage: true  # 启用存储
        access-key: ryShVeAq1Fv5G8KX1nKn
        secret-key: FwwF7aQ4q4BqFwseidw8H3IcwOYq71QBWa6VCf5S
        end-point: http://coe-file.pengfeijituan.com:9002/
        bucket-name: meiye-safe
        domain: http://coe-file.pengfeijituan.com:9002/ # 访问域名，注意“/”结尾，例如：http://abc.obs.com/
        base-path: ${spring.application.name}/
feishu:
  mini:
    appId: cli_a7b87645c85dd00b
    appSecret: ********************************
genn:
  database:
    multi:
      db:
        pf_orch_meiye:
          primary: true
          master:
            jdbcUrl: jdbc:mysql://***********:3306/pf_orch_meiye?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=UTC&rewriteBatchedStatements=true
            username: root
            password: pet17QN297d1c5
            driverClassName: com.mysql.cj.jdbc.Driver
            connectionTimeout: 10000
            minimumIdle: 2
            maximumPoolSize: 10
  meiye:
    approval:
      - code: risk
        approvalCode: 427DF26F-B80B-48E1-A4D5-E7ED1FD6195B
      - code: hazard
        approvalCode: 7E61FDA6-127D-479B-97AF-0CFF8780F4EB
    card-send:
      template:
        riskId: AAqd0pcmRc3Uu
        hazardId: AAqd0pghQvZai
      jumpUrl:
        riskPcUrl: http://meiye-safe-pc.pengfeijituan.com:21020/risk-detail
        riskAppUrl: http://meiye-safe-mini.pengfeijituan.com:21020/secure/riskDetail
        hazardPcUrl: http://meiye-safe-pc.pengfeijituan.com:21020/danger-detail
        hazardAppUrl: http://meiye-safe-mini.pengfeijituan.com:21020/danger/dangerDetail
    permission:
      enable: true
      # 鉴权接口白名单
      excludePatterns:
        - /user/getUserInfo
        - /app/**
        - /file/**
        - /execl/**
        - /test/**
        - /tool/**
        - /dict/**
    login:
      adminTelephones: ["+8618435186300","+8617703437741",
                        "19139640302","+8619139640302",
                        "18734422650","+8618734422650",
                        "18604097016","+8618604097016",
                        "17703437741","+8617703437741",
                        "+8618221551921","18221551921","+86-18221551921",
                        "+86-18435172247","+8618435172247","18435172247","15635460187","+8615635460187"]
