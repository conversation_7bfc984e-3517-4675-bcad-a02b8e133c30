knife4j:
  enable: true
spring:
  redis:
    redisson:
      config: |
        {
          "singleServerConfig": {
            "address": "redis://**********:6379",
            "password": "dajf780redis@a88",
            "connectionPoolSize": 50,
            "subscriptionConnectionMinimumIdleSize": 10,
            "subscriptionConnectionPoolSize": 50,
            "connectionMinimumIdleSize": 10,
            "idleConnectionTimeout": 10000,
            "connectTimeout": 10000,
            "timeout": 3000,
            "retryAttempts": 3,
            "retryInterval": 1500,
            "database": 0
          }
        }
dromara:
  x-file-storage: #文件存储配置
    default-platform: minio-1 #默认使用的存储平台
    thumbnail-suffix: ".min.jpg"
    minio:
      - platform: minio-1 # 存储平台标识
        enable-storage: true  # 启用存储
        access-key: FHZArYPFxUOjadtHNGbo
        secret-key: iSjOzz2RB1ibZ2jV4WpOi2Tolp7kTZNfPDfB18rg
        end-point: http://coe-file-sit.pengfeijituan.com:9000/
        bucket-name: meiye-safe
        domain: http://coe-file-sit.pengfeijituan.com:9000/ # 访问域名，注意“/”结尾，例如：http://abc.obs.com/
        base-path: ${spring.application.name}/
feishu:
  mini:
    appId: cli_a779c00b87b6500c
    appSecret: ********************************
genn:
  database:
    multi:
      db:
        pf_orch_meiye:
          primary: true
          master:
            jdbcUrl: jdbc:mysql://**********:3306/pf_orch_meiye?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=UTC&rewriteBatchedStatements=true
            username: meiye
            password: pf123456.
            driverClassName: com.mysql.cj.jdbc.Driver
            connectionTimeout: 10000
            minimumIdle: 2
            maximumPoolSize: 10
  meiye:
    approval:
      - code: risk
        approvalCode: 41383010-832A-4C24-93CC-8179D286E39A
      - code: hazard
        approvalCode: 07D2AEA3-A80C-4C60-8119-B35C6F422712
    card-send:
      template:
        riskId: AAqR5zNE3taOw
        hazardId: AAq4chMxPyDaa
        sensitiveId: AAqIm5PDYHLO8
      jumpUrl:
        riskPcUrl: http://meiye-safe-pc-sit.pengfeijituan.com:21006/risk-detail
        riskAppUrl: http://meiye-safe-mini-sit.pengfeijituan.com:21006/secure/riskDetail
        hazardPcUrl: http://meiye-safe-pc-sit.pengfeijituan.com:21006/danger-detail
        hazardAppUrl: http://meiye-safe-mini-sit.pengfeijituan.com:21006/danger/dangerDetail
    permission:
      enable: true
      # 鉴权接口白名单
      excludePatterns:
        - /user/getUserInfo
        - /app/**
        - /file/**
        - /execl/**
        - /test/**
        - /tool/**
        - /dict/**
    login:
      adminTelephones: ["+8618435186300",
                        "19139640302","+8619139640302",
                        "17703437741","+8617703437741",
                        "18734422650","+8618734422650",
                        "18604097016","+8618604097016",
                        "17703437741","+8617703437741",
                        "+8618221551921","18221551921","+86-18221551921",
                        "+86-18435172247","+8618435172247","18435172247",
                        "15635460187","+8615635460187"]

