knife4j:
  enable: true
spring:
  redis:
    redisson:
      config: |
        {
          "singleServerConfig": {
            "address": "redis://**********:6379",
            "password": "dajf780redis@a88",
            "connectionPoolSize": 50,
            "subscriptionConnectionMinimumIdleSize": 10,
            "subscriptionConnectionPoolSize": 50,
            "connectionMinimumIdleSize": 10,
            "idleConnectionTimeout": 10000,
            "connectTimeout": 10000,
            "timeout": 3000,
            "retryAttempts": 3,
            "retryInterval": 1500,
            "database": 0
          }
        }
dromara:
  x-file-storage: #文件存储配置
    default-platform: minio-1 #默认使用的存储平台
    thumbnail-suffix: ".min.jpg"
    minio:
      - platform: minio-1 # 存储平台标识
        enable-storage: true  # 启用存储
        access-key: FHZArYPFxUOjadtHNGbo
        secret-key: iSjOzz2RB1ibZ2jV4WpOi2Tolp7kTZNfPDfB18rg
        end-point: http://coe-file-sit.pengfeijituan.com:9000/
        bucket-name: meiye-safe
        domain: http://coe-file-sit.pengfeijituan.com:9000/ # 访问域名，注意“/”结尾，例如：http://abc.obs.com/
        base-path: ${spring.application.name}/
#    default-platform: huawei-obs-1 #默认使用的存储平台
#    thumbnail-suffix: ".min.jpg"
#    huawei-obs:
#      - platform: huawei-obs-1 # 存储平台标识
#        enable-storage: true  # 启用存储
#        access-key: ERMZYOVKRBTVCAIBTIJ2
#        secret-key: phJPIFwjjdnFnjWCOG5mihaMhLBsMUnlneQED4mE
#        end-point: obs.cn-north-4.myhuaweicloud.com
#        bucket-name: test-kangjian
#        domain: https://test-kangjian.obs.cn-north-4.myhuaweicloud.com:443/ # 访问域名，注意“/”结尾，例如：http://abc.obs.com/
#        base-path: ${spring.application.name}/
#    default-platform: amazon-s3-1
#    thumbnail-suffix: ".min.jpg"
#    amazon-s3: # 0.0.7 及以前的版本，配置名称是：aws-s3
#      - platform: amazon-s3-1 # 存储平台标识
#        enable-storage: true  # 启用存储
#        access-key: AKLTNDA5ODZkYjcwYTNjNDllNzk2N2FkN2JjMjI5ZGNiZWQ
#        secret-key: T0dRM1l6SXpOalV6WkRBNE5HRmhZMkpqWmpVMU1HSXhZMlE1WkdaaE9UUQ==
#        end-point: tos-s3-cn-beijing.volces.com
#        bucket-name: pro-pf-meeting
#        domain: pro-pf-meeting.tos-cn-beijing.volces.com
#        base-path: ${spring.application.name}/
feishu:
  mini:
    appId: cli_a779c00b87b6500c
    appSecret: ********************************
genn:
  database:
    multi:
      db:
        pf_orch_meiye:
          primary: true
          master:
            jdbcUrl: jdbc:mysql://**********:3306/pf_orch_meiye?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=UTC&rewriteBatchedStatements=true
            username: meiye
            password: pf123456.
            driverClassName: com.mysql.cj.jdbc.Driver
            connectionTimeout: 10000
            minimumIdle: 2
            maximumPoolSize: 10
  meiye:
    approval:
      - code: risk
        approvalCode: 41383010-832A-4C24-93CC-8179D286E39A
      - code: hazard
        approvalCode: 07D2AEA3-A80C-4C60-8119-B35C6F422712
    card-send:
      template:
        riskId: AAqR5zNE3taOw
        hazardId: AAq4chMxPyDaa
      jumpUrl:
        riskPcUrl: http://meiye-safe-pc-sit.pengfeijituan.com:21006/risk-detail
        riskAppUrl: http://meiye-safe-mini-sit.pengfeijituan.com:21006/secure/riskDetail
        hazardPcUrl: http://meiye-safe-pc-sit.pengfeijituan.com:21006/danger-detail
        hazardAppUrl: http://meiye-safe-mini-sit.pengfeijituan.com:21006/danger/dangerDetail
    permission:
      enable: true
      # 鉴权接口白名单
      excludePatterns:
        - /user/getUserInfo
        - /app/**
        - /file/**
        - /execl/**
        - /test/**
        - /tool/**
    login:
      adminTelephones: ["18435186300"]