package cn.pf.orch.feishu.model.callback;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 事件回调返回对象
 * @date 2024-12-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EventCallBackDTO {

    /**
     * 请填写 URL 以使订阅生效。
     * 填写后，飞书服务器会向其发送一个 HTTP POST 以验证地址有效期，请求格式为 JSON ，带 CHALLENGE 参数。
     * 应用接收此请求后，需要解析出 CHALLENGE 值，并在 1 秒内回复 CHALLENGE 值。
     */
    private String challenge;
}
