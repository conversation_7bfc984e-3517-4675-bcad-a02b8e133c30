package cn.pf.orch.feishu.model;

import cn.pf.orch.feishu.model.enums.MessageType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 机器人请求实体模型
 *
 * <AUTHOR>
 */
@Data
public class RobotRequest {

    /**
     * 机器人消息类型
     */
    @JsonProperty("msg_type")
    private MessageType msgType;

    /**
     * 机器人消息内容
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private RobotContent content;

    /**
     * 卡片消息内容
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private RobotContent card;

    /**
     * 签名需要的时间戳
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String timestamp;

    /**
     * 签名
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String sign;
}
