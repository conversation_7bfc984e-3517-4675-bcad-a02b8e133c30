package cn.pf.orch.feishu.model.callback;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 飞书事件请求header
 */
@Data
public class EventCallbackHeaderCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 事件 ID。
     */
    @JsonProperty("event_id")
    private String eventId;

    /**
     * 事件类型。
     */
    @JsonProperty("event_type")
    private String eventType;

    /**
     * 事件创建时间戳（单位：毫秒）。
     */
    @JsonProperty("create_time")
    private String createTime;

    /**
     * 事件 Token。
     */
    private String token;

    /**
     * 应用 ID。
     */
    @JsonProperty("app_id")
    private String appId;

    /**
     * 租户 Key。
     */
    @JsonProperty("tenant_key")
    private String tenantKey;
}
