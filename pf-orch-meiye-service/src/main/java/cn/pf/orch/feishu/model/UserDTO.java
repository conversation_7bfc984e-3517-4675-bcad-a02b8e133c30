package cn.pf.orch.feishu.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class UserDTO {

    @JsonProperty("union_id")
    private String unionId;
    /**
     * 用户的user_id，租户内用户的唯一标识，不同ID的说明参见 [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
     * <p> 示例值：3e3cf96b
     */
    @JsonProperty("user_id")
    private String userId;
    /**
     * 用户的open_id，应用内用户的唯一标识，不同ID的说明参见 [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
     * <p> 示例值：ou_7dab8a3d3cdcc9da365777c7ad535d62
     */
    @JsonProperty("open_id")
    private String openId;
    /**
     * 用户名
     * <p> 示例值：张三
     */
    @JsonProperty("name")
    private String name;

    @JsonProperty("avatar_key")
    private String avatarKey;

    @JsonProperty("avatar")
    private AvatarInfoDTO avatar;
}
