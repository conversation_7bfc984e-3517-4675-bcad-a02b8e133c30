package cn.pf.orch.feishu.config;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.core.utils.thread.BaseThreadDecorator;
import cn.genn.core.utils.thread.ThreadDecorator;

/**
 * 线程装饰器,用于多线程传递飞书信息
 */
public class FeishuAppDecorator extends BaseThreadDecorator {
    public FeishuAppDecorator() {
        super();
    }

    public FeishuAppDecorator(ThreadDecorator threadDecorator) {
        super(threadDecorator);
    }
    @Override
    protected Object beforeExecOnCurrThread() {
        return FeishuAppContext.get();
    }

    @Override
    protected void doOnNewThread(Object object) {
        if (object instanceof FeishuAppContext) {
            FeishuAppContext.set((FeishuAppContext) JsonUtils.clone(object, FeishuAppContext.class));
        }
    }

    @Override
    protected void afterExecOnNewThread() {
        FeishuAppContext.remove();
    }
}
