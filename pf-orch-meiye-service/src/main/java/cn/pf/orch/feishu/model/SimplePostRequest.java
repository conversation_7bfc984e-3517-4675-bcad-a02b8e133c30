package cn.pf.orch.feishu.model;

import cn.genn.core.model.KVStruct;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SimplePostRequest {

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * at的openId
     */
    private List<String> atOpenIds;

    /**
     * 是否at所有人
     */
    private boolean atAll;

    /**
     * 图片key
     */
    private List<String> imgKeys;

    /**
     * 链接
     * key: 链接地址
     * value: 链接标题
     */
    private List<KVStruct<String, String>> links;
}
