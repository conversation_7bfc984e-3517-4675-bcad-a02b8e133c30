package cn.pf.orch.feishu.model.post.tag;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AtTag extends PostTag {

    /**
     * 用户的 Open ID。
     * - @ 单个用户时，user_id字段必须是有效值。
     * - @ 所有人时，填 all。
     */
    @JsonProperty("user_id")
    private String userId;

    /**
     * 用户的姓名。非必填
     */
    @JsonProperty("user_name")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String userName;

    @JsonIgnore
    private boolean isAll;

    @Override
    public String getTag() {
        return "at";
    }

    public AtTag(String userId) {
        this.userId = userId;
    }

    public AtTag(String userId, String userName) {
        this.userId = userId;
        this.userName = userName;
    }
}
