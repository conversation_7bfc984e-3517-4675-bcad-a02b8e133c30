package cn.pf.orch.feishu.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分享群
 * <AUTHOR>
 * @see cn.genn.third.feishu.robot.enums.MessageType#SHARE_CHAT
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ShareChatRobotContent extends RobotContent {

    @JsonProperty("share_chat_id")
    private String shareChatId;
}
