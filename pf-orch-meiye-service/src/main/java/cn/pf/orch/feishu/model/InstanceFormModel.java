package cn.pf.orch.feishu.model;

import cn.pf.orch.feishu.model.enums.InstanceFormTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表单类型请参考[控件值说明]部分,保证字段对应
 * https://open.feishu.cn/document/server-docs/approval-v4/instance/create?appId=cli_a78f67b43193d00c
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InstanceFormModel {

    private InstanceFormTypeEnum type;

    private Object value;

    /**
     * 组件是否必输:true:必输
     */
    private boolean required;
}
