package cn.pf.orch.feishu.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class AvatarInfoDTO {
    /**
     * 72*72像素头像链接
     * <p> 示例值：https://foo.icon.com/xxxx
     */
    @JsonProperty("avatar_72")
    private String avatar72;
    /**
     * 240*240像素头像链接
     * <p> 示例值：https://foo.icon.com/xxxx
     */
    @JsonProperty("avatar_240")
    private String avatar240;
    /**
     * 640*640像素头像链接
     * <p> 示例值：https://foo.icon.com/xxxx
     */
    @JsonProperty("avatar_640")
    private String avatar640;
    /**
     * 原始头像链接
     * <p> 示例值：https://foo.icon.com/xxxx
     */
    @JsonProperty("avatar_origin")
    private String avatarOrigin;
}
