package cn.pf.orch.feishu.service;

import cn.genn.core.utils.http.OkHttpUtil;
import cn.genn.core.utils.http.model.HttpResponse;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.pf.orch.feishu.common.FeishuCacheConstants;
import cn.pf.orch.feishu.common.FeishuConstants;
import cn.pf.orch.feishu.config.AppConfig;
import cn.pf.orch.feishu.config.FeishuAppContext;
import cn.pf.orch.feishu.exception.FeishuErrorCode;
import cn.pf.orch.feishu.exception.ThirdException;
import cn.pf.orch.feishu.model.ContactSearchUserDTO;
import cn.pf.orch.feishu.model.ContactSearchUserResponse;
import cn.pf.orch.feishu.model.FeishuResponse;
import cn.pf.orch.feishu.util.FeishuInvokeUtil;
import com.google.common.collect.Lists;
import com.lark.oapi.Client;
import com.lark.oapi.service.contact.v3.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 通讯录
 */
@Slf4j
public class ContactService {

    private final Client feishuClient;
    private final StringRedisTemplate redisTemplate;

    public ContactService(AppConfig appConfig) {
        feishuClient = appConfig.getFeishuClient();
        redisTemplate = appConfig.getRedisTemplate();
    }

    /**
     * 飞书openId获取飞书用户信息
     * 支持查询离职员工
     *
     * @param openId
     * @return
     */
    public GetUserRespBody getUserInfo(String openId) {
        if (StrUtil.isBlank(openId)) {
            return null;
        }
        GetUserReq req = GetUserReq.newBuilder()
                .userIdType("open_id")
                .userId(openId)
                .build();
        return FeishuInvokeUtil.executeRequest(req, feishuClient.contact().user()::get, FeishuErrorCode.USER_INFO_FAIL);
    }

    /**
     * 手机号获取用户openId
     * 支持查询离职员工
     *
     * @param telephone
     * @return
     */
    public BatchGetIdUserRespBody getOpenId(String telephone) {
        if (StrUtil.isBlank(telephone)) {
            throw new ThirdException(FeishuErrorCode.TELEPHONE_NOT_EXIST);
        }
        BatchGetIdUserReq req = BatchGetIdUserReq.newBuilder()
                .userIdType("open_id")
                .batchGetIdUserReqBody(BatchGetIdUserReqBody.newBuilder()
                        .mobiles(new String[]{telephone})
                        .includeResigned(true)
                        .build())
                .build();
        return FeishuInvokeUtil.executeRequest(req, feishuClient.contact().user()::batchGetId, FeishuErrorCode.GET_OPEN_ID_FAIL);
    }

    /**
     * openId批量查询用户
     * 支持查询离职员工
     */
    public List<User> userBatch(List<String> openIds) {
        List<User> userInfos = new ArrayList<>();
        List<String> notCachedUserIds = new ArrayList<>();

        // 从Redis中获取用户信息
        for (String openId : openIds) {
            String cacheKey = FeishuCacheConstants.getCacheFeishuUserInfo(openId);
            String cachedUserInfo = redisTemplate.opsForValue().get(cacheKey);
            if (cachedUserInfo != null) {
                User userInfo = JsonUtils.parse(cachedUserInfo, User.class);
                userInfos.add(userInfo);
            } else {
                notCachedUserIds.add(openId);
            }
        }
        // 从飞书中获取未缓存的用户信息
        if (!notCachedUserIds.isEmpty()) {
            // 飞书单次查询最大50人
            List<List<String>> partition = Lists.partition(notCachedUserIds, 40);
            List<User> fetchedUserInfos = partition.stream().flatMap(openIdList -> this.getUserBatch(openIdList).stream()).distinct().collect(Collectors.toList());
            userInfos.addAll(fetchedUserInfos);
            // 缓存从飞书中获取的用户信息到Redis
            for (User userInfo : fetchedUserInfos) {
                String cacheKey = FeishuCacheConstants.getCacheFeishuUserInfo(userInfo.getOpenId());
                String userInfoJson = JsonUtils.toJson(userInfo);
                redisTemplate.opsForValue().set(cacheKey, userInfoJson, 1, TimeUnit.DAYS);
            }
        }
        return userInfos;
    }

    /**
     * 模糊查询用户
     * 不支持查询离职员工
     */
    public ContactSearchUserResponse searchUser(ContactSearchUserDTO model) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + FeishuAppContext.get().getUserAccessToken());
        Map<String, String> params = new HashMap<>();
        params.put("query", model.getQuery());
        params.put("page_size", String.valueOf(model.getPageSize()));
        params.put("page_token", model.getPageToken());
        try (HttpResponse httpResponse = OkHttpUtil.getInstance().get(FeishuConstants.getContactSearchUserUrl(), headers, params)) {
            if (httpResponse.getCode() != 200) {
                log.error("search user fail, code: {}, msg: {}", httpResponse.getCode(), httpResponse.string());
                throw new ThirdException(FeishuErrorCode.SEARCH_USER_INFO_FAIL);
            }
            FeishuResponse<ContactSearchUserResponse> resp = httpResponse.toObj(FeishuResponse.class, ContactSearchUserResponse.class);
            if (resp.getCode() != 0) {
                log.error("search user fail, code: {}, msg: {}", resp.getCode(), resp.getMsg());
                throw new ThirdException(FeishuErrorCode.SEARCH_USER_INFO_FAIL.buildCode(), resp.getMsg());
            }
            log.info("search user file resp.data: {}", JsonUtils.toJson(resp.getData()));
            return resp.getData();
        } catch (IOException e) {
            log.error("search user file  error", e);
            throw new ThirdException(FeishuErrorCode.SEARCH_USER_INFO_FAIL);
        }
    }

    //-------------以下是飞书SDK简单包装-------------------


    protected List<User> getUserBatch(List<String> openIds) {
        BatchUserReq req = BatchUserReq.newBuilder()
                .userIdType("open_id")
                .userIds(openIds.toArray(new String[0]))
                .build();
        BatchUserRespBody body = FeishuInvokeUtil.executeRequest(req, feishuClient.contact().v3().user()::batch, FeishuErrorCode.SEARCH_USER_INFO_FAIL);
        return Arrays.asList(body.getItems());
    }

    /**
     * 获取企业通讯录一级部门ids
     */
    public List<String> getContactScope() {
        ListScopeReq req = ListScopeReq.newBuilder().pageSize(100).build();
        ListScopeRespBody body = FeishuInvokeUtil.executeRequest(req, feishuClient.contact().v3().scope()::list, FeishuErrorCode.CONTACT_INFO_FAIL);
        List<String> resultList = Arrays.asList(body.getDepartmentIds());
        while (resultList.size() == 100) {
            req = ListScopeReq.newBuilder().pageSize(100).pageToken(body.getPageToken()).build();
            body = FeishuInvokeUtil.executeRequest(req, feishuClient.contact().v3().scope()::list, FeishuErrorCode.CONTACT_INFO_FAIL);
            resultList.addAll(Arrays.asList(body.getDepartmentIds()));
        }
        return resultList;
    }

    /**
     * 批量获取部门信息
     */
    public List<Department> getDepartmentBatch(List<String> departmentIds) {
        List<Department> allDepartments = new ArrayList<>();
        // 飞书单次查询最大50个部门
        List<List<String>> partition = Lists.partition(departmentIds, 50);
        for (List<String> departmentIdList : partition) {
            BatchDepartmentReq req = BatchDepartmentReq.newBuilder()
                    .departmentIds(departmentIdList.toArray(new String[0]))
                    .build();
            BatchDepartmentRespBody body = FeishuInvokeUtil.executeRequest(req, feishuClient.contact().v3().department()::batch, FeishuErrorCode.DEPARTMENT_INFO_FAIL);
            allDepartments.addAll(Arrays.asList(body.getItems()));
        }
        return allDepartments;
    }

    /**
     * 获取子部门列表
     */
    public List<Department> getDepartmentChildren(String departmentId) {
        List<Department> resultList = new ArrayList<>();
        ChildrenDepartmentReq req = ChildrenDepartmentReq.newBuilder()
                .departmentId(departmentId)
                .pageSize(50)
                .build();
        ChildrenDepartmentRespBody body = FeishuInvokeUtil.executeRequest(req, feishuClient.contact().v3().department()::children, FeishuErrorCode.CHILDREN_DEPARTMENT_INFO_FAIL);
        if (ObjUtil.isNotNull(body) && ObjUtil.isNotNull(body.getItems()) && body.getItems().length > 0) {
            resultList = Arrays.asList(body.getItems());
            while (resultList.size() == 50) {
                req = ChildrenDepartmentReq.newBuilder().pageSize(50).pageToken(body.getPageToken()).build();
                body = FeishuInvokeUtil.executeRequest(req, feishuClient.contact().v3().department()::children, FeishuErrorCode.CHILDREN_DEPARTMENT_INFO_FAIL);
                resultList.addAll(Arrays.asList(body.getItems()));
            }
        }
        return resultList;
    }

    /**
     * 获取通讯录范围内所有父部门信息.子部门在前
     */
    public List<Department> getParentDepartment(String departmentId) {
        ParentDepartmentReq req = ParentDepartmentReq.newBuilder()
                .departmentId(departmentId)
                .build();
        ParentDepartmentRespBody body = FeishuInvokeUtil.executeRequest(req, feishuClient.contact().v3().department()::parent, FeishuErrorCode.PARENT_DEPARTMENT_INFO_FAIL);
        return body == null || body.getItems() == null ? new ArrayList<>() : Arrays.asList(body.getItems());
    }

    /**
     * 获取部门下用户信息
     */
    public List<User> getUserListByDepartment(String departmentId) {
        List<User> resultList = new ArrayList<>();
        FindByDepartmentUserReq req = FindByDepartmentUserReq.newBuilder()
                .departmentId(departmentId)
                .pageSize(50)
                .build();
        FindByDepartmentUserRespBody body = FeishuInvokeUtil.executeRequest(req, feishuClient.contact().v3().user()::findByDepartment, FeishuErrorCode.USER_INFO_FAIL);
        if (ObjUtil.isNotNull(body) && ObjUtil.isNotNull(body.getItems()) && body.getItems().length > 0) {
            resultList.addAll(Arrays.asList(body.getItems()));
            while (resultList.size() == 50) {
                req = FindByDepartmentUserReq.newBuilder().departmentId(departmentId).pageSize(50).pageToken(body.getPageToken()).build();
                body = FeishuInvokeUtil.executeRequest(req, feishuClient.contact().v3().user()::findByDepartment, FeishuErrorCode.USER_INFO_FAIL);
                resultList.addAll(Arrays.asList(body.getItems()));
            }
        }
        return resultList;
    }
}
