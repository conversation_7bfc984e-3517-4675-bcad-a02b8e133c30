package cn.pf.orch.feishu.model.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum FeishuInstanceStatusEnum {
    PENDING("PENDING","审批中"),
    APPROVED("APPROVED","通过"),
    REJECTED("REJECTED","拒绝"),
    CANCELED("CANCELED","撤回"),
    DELETED("DELETED","删除"),
    ;

    /**
     * code来源与飞书的事件配置
     */
    @JsonValue
    private final String code;

    private final String description;

    private static final Map<String, FeishuInstanceStatusEnum> VALUES = new HashMap<>();
    static {
        for (final FeishuInstanceStatusEnum item : FeishuInstanceStatusEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static FeishuInstanceStatusEnum of(String code) {
        return VALUES.get(code);
    }
}
