package cn.pf.orch.feishu.service;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.util.ObjUtil;
import cn.pf.orch.feishu.common.FeishuCacheConstants;
import cn.pf.orch.feishu.config.AppConfig;
import cn.pf.orch.feishu.config.FeishuAppContext;
import cn.pf.orch.feishu.exception.FeishuErrorCode;
import cn.pf.orch.feishu.util.FeishuInvokeUtil;
import com.lark.oapi.Client;
import com.lark.oapi.core.request.RequestOptions;
import com.lark.oapi.service.calendar.v4.enums.PrimaryCalendarUserIdTypeEnum;
import com.lark.oapi.service.calendar.v4.model.PrimaryCalendarReq;
import com.lark.oapi.service.calendar.v4.model.PrimaryCalendarRespBody;
import com.lark.oapi.service.calendar.v4.model.UserCalendar;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.concurrent.TimeUnit;

/**
 * 日历
 */
@Slf4j
public class CalendarService {

    private final Client feishuClient;
    private final StringRedisTemplate redisTemplate;

    public CalendarService(AppConfig appConfig) {
        feishuClient = appConfig.getFeishuClient();
        redisTemplate = appConfig.getRedisTemplate();
    }


    /**
     * 获取用户主日历信息
     * @return
     */
    public UserCalendar getUserMainInfo(){
        String cacheKey = FeishuCacheConstants.getCalendarMainInfo(FeishuAppContext.get().getOpenId());
        String cacheCalendarInfo = redisTemplate.opsForValue().get(cacheKey);
        //获取缓存
        if (cacheCalendarInfo != null) {
            return JsonUtils.parse(cacheCalendarInfo, UserCalendar.class);
        }
        //调用飞书
        String userAccessToken = FeishuAppContext.get().getUserAccessToken();
        PrimaryCalendarRespBody userMainInfo = this.getUserMainInfo(userAccessToken);
        if(ObjUtil.isNull(userMainInfo) || userMainInfo.getCalendars().length == 0){
            return null;
        }
        UserCalendar calendar = userMainInfo.getCalendars()[0];
        redisTemplate.opsForValue().set(cacheKey, JsonUtils.toJson(calendar), 7, TimeUnit.DAYS);
        return calendar;
    }

    //-------------以下是飞书SDK简单包装-------------------

    /**
     * 获取用户主日历信息
     *
     * @return
     */
    protected PrimaryCalendarRespBody getUserMainInfo(String userAccessToken) {
        PrimaryCalendarReq req = PrimaryCalendarReq.newBuilder()
                .userIdType(PrimaryCalendarUserIdTypeEnum.OPEN_ID)
                .build();
        RequestOptions options = RequestOptions.newBuilder().userAccessToken(userAccessToken).build();
        return FeishuInvokeUtil.executeRequest(req, options, feishuClient.calendar().v4().calendar()::primary, FeishuErrorCode.CALENDAR_INFO_ERROR);
    }
}
