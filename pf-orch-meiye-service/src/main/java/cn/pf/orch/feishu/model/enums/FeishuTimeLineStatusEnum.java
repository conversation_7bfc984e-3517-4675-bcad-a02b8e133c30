package cn.pf.orch.feishu.model.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum FeishuTimeLineStatusEnum {

    DONE("START","审批开始"),
    PASS("PASS","通过"),
    REJECT("REJECT","拒绝"),
    AUTO_PASS("AUTO_PASS","自动通过"),
    AUTO_REJECT("AUTO_REJECT","自动拒绝"),
    REMOVE_REPEAT("REMOVE_REPEAT","去重"),
    TRANSFER("TRANSFER","转交"),
    ADD_APPROVER_BEFORE("ADD_APPROVER_BEFORE","前加签"),
    ADD_APPROVER("ADD_APPROVER","并加签"),
    ADD_APPROVER_AFTER("ADD_APPROVER_AFTER","后加签"),
    DELETE_APPROVER("DELETE_APPROVER","减签"),
    ROLLBACK_SELECTED("ROLLBACK_SELECTED","指定回退"),
    ROLLBACK("ROLLBACK","全部回退"),
    CANCEL("CANCEL","撤回"),
    DELETE("DELETE","删除"),
    CC("CC","抄送"),
    ;

    /**
     * code来源与飞书的事件配置
     */
    @JsonValue
    private final String code;

    private final String description;

    private static final Map<String, FeishuTimeLineStatusEnum> VALUES = new HashMap<>();
    static {
        for (final FeishuTimeLineStatusEnum item : FeishuTimeLineStatusEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static FeishuTimeLineStatusEnum of(String code) {
        return VALUES.get(code);
    }
}
