package cn.pf.orch.feishu.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.pf.orch.feishu.exception.FeishuErrorCode;
import cn.pf.orch.feishu.exception.ThirdException;
import cn.pf.orch.feishu.model.*;

import java.util.Objects;

public class AssertUtil {

    public static void assertCreateCalendarEventModel(CreateCalendarEventModel createCalendarEvent) {
        if (StrUtil.isBlank(createCalendarEvent.getCalendarId())) {
            throw new ThirdException(FeishuErrorCode.CALENDAR_ID_NOT_EXIST);
        }
        if (StrUtil.isBlank(createCalendarEvent.getSummary())) {
            throw new ThirdException(FeishuErrorCode.SUMMARY_NOT_EXIST);
        }
        if (StrUtil.isBlank(createCalendarEvent.getOrganizerId())) {
            throw new ThirdException(FeishuErrorCode.ORGANIZER_ID_NOT_EXIST);
        }
        if (Objects.isNull(createCalendarEvent.getDate()) &&
                (Objects.isNull(createCalendarEvent.getStartTime()) || Objects.isNull(createCalendarEvent.getEndTime()))) {
            throw new ThirdException(FeishuErrorCode.START_END_TIME_NOT_EXIST);
        }
    }

    public static void assertCalendarEventModel(CalendarEventModel calendarEvent) {
        if (StrUtil.isBlank(calendarEvent.getCalendarId())) {
            throw new ThirdException(FeishuErrorCode.CALENDAR_ID_NOT_EXIST);
        }
        if (StrUtil.isBlank(calendarEvent.getEventId())) {
            throw new ThirdException(FeishuErrorCode.EVENT_ID_NOT_EXIST);
        }
    }

    public static void assertUpdateCalendarEventModel(UpdateCalendarEventModel updateCalendarEvent) {
        if (StrUtil.isBlank(updateCalendarEvent.getCalendarId())) {
            throw new ThirdException(FeishuErrorCode.CALENDAR_ID_NOT_EXIST);
        }
        if (StrUtil.isBlank(updateCalendarEvent.getEventId())) {
            throw new ThirdException(FeishuErrorCode.EVENT_ID_NOT_EXIST);
        }
    }

    public static void assertListCalendarEventModel(ListCalendarEventModel listCalendarEvent) {
        if (StrUtil.isBlank(listCalendarEvent.getCalendarId())) {
            throw new ThirdException(FeishuErrorCode.CALENDAR_ID_NOT_EXIST);
        }
        if ((listCalendarEvent.getStartTime() == null && listCalendarEvent.getEndTime() != null) ||
                (listCalendarEvent.getStartTime() != null && listCalendarEvent.getEndTime() == null)) {
            throw new ThirdException(FeishuErrorCode.START_END_TIME_NOT_EXIST);
        }
    }

    public static void assertGetCalendarEventInstancesModel(GetCalendarEventInstancesModel getCalendarEventInstances) {
        if (StrUtil.isBlank(getCalendarEventInstances.getCalendarId())) {
            throw new ThirdException(FeishuErrorCode.CALENDAR_ID_NOT_EXIST);
        }
        if (StrUtil.isBlank(getCalendarEventInstances.getEventId())) {
            throw new ThirdException(FeishuErrorCode.EVENT_ID_NOT_EXIST);
        }
        if ((getCalendarEventInstances.getStartTime() == null && getCalendarEventInstances.getEndTime() != null) ||
                (getCalendarEventInstances.getStartTime() != null && getCalendarEventInstances.getEndTime() == null)) {
            throw new ThirdException(FeishuErrorCode.START_END_TIME_NOT_EXIST);
        }
    }

    public static void assertGetCalendarEventAttendeesModel(GetCalendarEventAttendeesModel getCalendarEventAttendees) {
        if (StrUtil.isBlank(getCalendarEventAttendees.getCalendarId())) {
            throw new ThirdException(FeishuErrorCode.CALENDAR_ID_NOT_EXIST);
        }
        if (StrUtil.isBlank(getCalendarEventAttendees.getEventId())) {
            throw new ThirdException(FeishuErrorCode.EVENT_ID_NOT_EXIST);
        }
    }

    public static void assertCreateCalendarEventAttendeesModel(CreateCalendarEventAttendeesModel createCalendarEventAttendees) {
        if (StrUtil.isBlank(createCalendarEventAttendees.getCalendarId())) {
            throw new ThirdException(FeishuErrorCode.CALENDAR_ID_NOT_EXIST);
        }
        if (StrUtil.isBlank(createCalendarEventAttendees.getEventId())) {
            throw new ThirdException(FeishuErrorCode.EVENT_ID_NOT_EXIST);
        }
        if (Objects.isNull(createCalendarEventAttendees.getAttendUsers()) || createCalendarEventAttendees.getAttendUsers().isEmpty()) {
            throw new ThirdException(FeishuErrorCode.CREATE_CALENDAR_EVENT_ATTENDEES_NOT_EXIST);
        }
    }

    public static void assertDeleteCalendarEventAttendeesModel(DeleteCalendarEventAttendeesModel deleteCalendarEventAttendees) {
        if (StrUtil.isBlank(deleteCalendarEventAttendees.getCalendarId())) {
            throw new ThirdException(FeishuErrorCode.CALENDAR_ID_NOT_EXIST);
        }
        if (StrUtil.isBlank(deleteCalendarEventAttendees.getEventId())) {
            throw new ThirdException(FeishuErrorCode.EVENT_ID_NOT_EXIST);
        }
    }

    public static void checkApprovalInstanceModel(ApprovalInstanceModel model) {
        if (StrUtil.isBlank(model.getOpenId())) {
            throw new ThirdException(FeishuErrorCode.OPEN_ID_NOT_EXIST);
        }
        if (StrUtil.isBlank(model.getApprovalCode())) {
            throw new ThirdException(FeishuErrorCode.APPROVAL_CODE_NOT_EXIST);
        }
        if (CollUtil.isEmpty(model.getFormList())) {
            throw new ThirdException(FeishuErrorCode.FORM_NOT_EXIST);
        }
    }
}
