package cn.pf.orch.feishu.config;

import cn.hutool.core.util.ObjUtil;
import cn.pf.orch.feishu.FeishuAppClient;
import cn.pf.orch.feishu.exception.ThirdException;
import cn.pf.orch.feishu.properties.FeishuProperties;
import com.lark.oapi.Client;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.concurrent.TimeUnit;

@Configuration
public class FeishuAutoConfig {

    @Bean
    public Client feishuClient(FeishuProperties properties){
        if (properties.isEnabled()) {
            return Client.newBuilder(properties.getAppId(), properties.getAppSecret())
                    .requestTimeout(3, TimeUnit.SECONDS) // 设置httpclient 超时时间，默认永不超时
                    .build();
        }
        throw new ThirdException("飞书配置异常!");
    }

    @Bean
    public FeishuAppClient feishuAppClient(FeishuProperties properties, Client feishuClient, StringRedisTemplate redisTemplate) {
        if(ObjUtil.isNotNull(feishuClient)){
            return new FeishuAppClient
                    .Builder(properties,feishuClient,redisTemplate)
                    .build();
        }
        throw new ThirdException("飞书配置异常!!");
    }
}
