package cn.pf.orch.feishu.service;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.pf.orch.feishu.config.AppConfig;
import cn.pf.orch.feishu.exception.FeishuErrorCode;
import cn.pf.orch.feishu.model.RobotRequest;
import cn.pf.orch.feishu.model.RobotRequestBuilder;
import cn.pf.orch.feishu.util.FeishuInvokeUtil;
import com.lark.oapi.Client;
import com.lark.oapi.service.im.v1.enums.CreateMessageReceiveIdTypeEnum;
import com.lark.oapi.service.im.v1.model.*;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * 应用机器人
 */
@Slf4j
public class RobotService {

    private final Client feishuClient;

    public RobotService(AppConfig appConfig) {
        feishuClient = appConfig.getFeishuClient();
    }

    /**
     * 发送文本消息
     */
    public CreateMessageRespBody sendText(String openId, String text) {
        RobotRequest request = RobotRequestBuilder.Text.create().content(text).build();
        return this.sendMessage(openId, "text", JsonUtils.toJson(request.getContent()));
    }

    /**
     * 发送卡片消息,只支持模版卡片
     * <a href="https://open.feishu.cn/cardkit/editor">...</a>
     *
     * @return
     */
    public CreateMessageRespBody sendCard(String openId, String templateId, Map<String, Object> templateVariable) {
        RobotRequest request = RobotRequestBuilder.CardTemplate.create().template(templateId).variable(templateVariable).build();
        return this.sendMessage(openId, "interactive", JsonUtils.toJson(request.getCard()));
    }

    /**
     * 更新卡片消息
     * <a href="https://open.feishu.cn/document/server-docs/im-v1/message-card/patch?appId=cli_a78f67b43193d00c">...</a>
     *
     * @return
     */
    public UpdateMessageRespBody updateCard(String messageId, String templateId, Map<String, Object> templateVariable) {
        RobotRequest request = RobotRequestBuilder.CardTemplate.create().template(templateId).variable(templateVariable).build();
        return this.updateMessage(messageId, "interactive", JsonUtils.toJson(request.getCard()));
    }
    //-------------以下是飞书SDK简单包装-------------------

    public CreateMessageRespBody sendMessage(String openId, String msgType, String content) {
        CreateMessageReq req = CreateMessageReq.newBuilder()
                .receiveIdType(CreateMessageReceiveIdTypeEnum.OPEN_ID)
                .createMessageReqBody(CreateMessageReqBody.newBuilder()
                        .receiveId(openId)
                        .msgType(msgType)
                        .content(content).build()).build();
        return FeishuInvokeUtil.executeRequest(req, feishuClient.im().message()::create, FeishuErrorCode.SEND_MESSAGE_FAIL);
    }

    public UpdateMessageRespBody updateMessage(String messageId, String msgType, String content) {
        UpdateMessageReq req = UpdateMessageReq.newBuilder()
                .messageId(messageId)
                .updateMessageReqBody(UpdateMessageReqBody.newBuilder()
                        .msgType(msgType)
                        .content(content).build())
                .build();
        return FeishuInvokeUtil.executeRequest(req, feishuClient.im().v1().message()::update, FeishuErrorCode.UPDATE_MESSAGE_FAIL);
    }
}
