package cn.pf.orch.feishu.util;

import cn.pf.orch.feishu.model.*;
import com.lark.oapi.service.calendar.v4.enums.*;
import com.lark.oapi.service.calendar.v4.model.*;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Objects;

public class BuildRequestUtil {

    public static CreateCalendarAclReq buildCreateCalendarAclRequest(CreateCalendarEventModel createCalendarEvent) {
        return CreateCalendarAclReq.newBuilder()
                .calendarId(createCalendarEvent.getCalendarId())
                .userIdType(CreateCalendarAclUserIdTypeEnum.OPEN_ID)
                .calendarAcl(CalendarAcl.newBuilder()
                        .role(CalendarAclCalendarAccessRoleEnum.WRITER)
                        .scope(AclScope.newBuilder()
                                .type(AclScopeAclScopeTypeEnum.USER)
                                .userId(createCalendarEvent.getOrganizerId())
                                .build())
                        .build())
                .build();
    }

    public static CreateCalendarEventReq buildCreateCalendarEventRequest(CreateCalendarEventModel createCalendarEvent) {
        String data = Objects.isNull(createCalendarEvent.getDate()) ? null :
                createCalendarEvent.getDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        CalendarEvent calendarEvent = CalendarEvent.newBuilder()
                .summary(createCalendarEvent.getSummary())
                .description(createCalendarEvent.getDescription())
                .needNotification(true)
                .startTime(TimeInfo.newBuilder()
                        .date(data)
                        .timestamp(Objects.isNull(createCalendarEvent.getStartTime()) ? null :
                                String.valueOf(createCalendarEvent.getStartTime().atZone(ZoneId.of("Asia/Shanghai")).toInstant().getEpochSecond()))
                        .timezone("Asia/Shanghai")
                        .build())
                .endTime(TimeInfo.newBuilder()
                        .date(data)
                        .timestamp(Objects.isNull(createCalendarEvent.getEndTime()) ? null :
                                String.valueOf(createCalendarEvent.getEndTime().atZone(ZoneId.of("Asia/Shanghai")).toInstant().getEpochSecond()))
                        .timezone("Asia/Shanghai")
                        .build())
                .vchat(Vchat.newBuilder()
                        .vcType(VchatVcTypeEnum.VC)
                        .iconType(VchatIconTypeEnum.VC)
                        .meetingSettings(MeetingSettings.newBuilder()
                                .ownerId(createCalendarEvent.getOrganizerId())
                                .joinMeetingPermission(MeetingSettingsJoinMeetingPermissionTypeEnum.ONLYSAMEORGANIZATION)
                                .allowAttendeesStart(false)
                                .autoRecord(true)
                                .openLobby(true)
                                .build())
                        .build())
                .visibility(CalendarEventEventVisibilityEnum.DEFAULT)
                .attendeeAbility(CalendarEventEventAttendeeAbilityEnum.CANINVITEOTHERS)
                .freeBusyStatus(CalendarEventEventFreeBusyStatusEnum.BUSY)
                .color(-1)
                .reminders(new Reminder[]{Reminder.newBuilder().minutes(5).build()})
                .recurrence(createCalendarEvent.getRecurrence())
                .status(CalendarEventEventStatusEnum.CONFIRMED)
                .createTime(String.valueOf(LocalDateTime.now().atZone(ZoneId.of("Asia/Shanghai")).toInstant().getEpochSecond()))
                .eventOrganizer(EventOrganizer.newBuilder()
                        .userId(createCalendarEvent.getOrganizerId())
                        .displayName(createCalendarEvent.getOrganizerName())
                        .build())
                .attendees(new CalendarEventAttendee[]{CalendarEventAttendee.newBuilder()
                        .type(CalendarEventAttendeeEventAttendeeTypeEnum.USER)
                        .isOrganizer(true)
                        .userId(createCalendarEvent.getOrganizerId())
                        .displayName(createCalendarEvent.getOrganizerName())
                        .build()})
                .build();
        return CreateCalendarEventReq.newBuilder()
                .calendarId(createCalendarEvent.getCalendarId())
                .userIdType(CreateCalendarEventUserIdTypeEnum.OPEN_ID)
                .calendarEvent(calendarEvent)
                .build();
    }

    public static DeleteCalendarEventReq buildDeleteCalendarEventRequest(CalendarEventModel calendarEvent) {
        return DeleteCalendarEventReq.newBuilder()
                .eventId(calendarEvent.getEventId())
                .calendarId(calendarEvent.getCalendarId())
                .needNotification(DeleteCalendarEventNeedNotificationEnum.TRUE)
                .build();
    }

    public static PatchCalendarEventReq buildPatchCalendarEventRequest(UpdateCalendarEventModel updateCalendarEvent) {
        String data = Objects.isNull(updateCalendarEvent.getDate()) ? null :
                updateCalendarEvent.getDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        CalendarEvent calendarEvent = CalendarEvent.newBuilder()
                .summary(updateCalendarEvent.getSummary())
                .description(updateCalendarEvent.getDescription())
                .startTime(TimeInfo.newBuilder()
                        .date(data)
                        .timestamp(Objects.isNull(updateCalendarEvent.getStartTime()) ? null :
                                String.valueOf(updateCalendarEvent.getStartTime().atZone(ZoneId.of("Asia/Shanghai")).toInstant().getEpochSecond()))
                        .timezone("Asia/Shanghai")
                        .build())
                .endTime(TimeInfo.newBuilder()
                        .date(data)
                        .timestamp(Objects.isNull(updateCalendarEvent.getEndTime()) ? null :
                                String.valueOf(updateCalendarEvent.getEndTime().atZone(ZoneId.of("Asia/Shanghai")).toInstant().getEpochSecond()))
                        .timezone("Asia/Shanghai")
                        .build())
                .recurrence(updateCalendarEvent.getRecurrence())
                .build();
        return PatchCalendarEventReq.newBuilder()
                .eventId(updateCalendarEvent.getEventId())
                .calendarId(updateCalendarEvent.getCalendarId())
                .userIdType(PatchCalendarEventUserIdTypeEnum.OPEN_ID)
                .calendarEvent(calendarEvent)
                .build();
    }

    public static GetCalendarEventReq buildGetCalendarEventRequest(CalendarEventModel calendarEvent) {
        return GetCalendarEventReq.newBuilder()
                .eventId(calendarEvent.getEventId())
                .calendarId(calendarEvent.getCalendarId())
                .needAttendee(true)
                .maxAttendeeNum(100)
                .userIdType(GetCalendarEventUserIdTypeEnum.OPEN_ID)
                .build();
    }

    public static ListCalendarEventReq buildListCalendarEventRequest(ListCalendarEventModel listCalendarEvent) {
        return ListCalendarEventReq.newBuilder()
                .calendarId(listCalendarEvent.getCalendarId())
                .pageSize(listCalendarEvent.getPageSize())
                .pageToken(listCalendarEvent.getPageToken())
                .syncToken(listCalendarEvent.getSyncToken())
                .startTime(Objects.isNull(listCalendarEvent.getStartTime()) ? null :
                        String.valueOf(listCalendarEvent.getStartTime().atZone(ZoneId.of("Asia/Shanghai")).toInstant().getEpochSecond()))
                .endTime(Objects.isNull(listCalendarEvent.getEndTime()) ? null :
                        String.valueOf(listCalendarEvent.getEndTime().atZone(ZoneId.of("Asia/Shanghai")).toInstant().getEpochSecond()))
                .build();
    }

    public static InstancesCalendarEventReq buildInstancesCalendarEventRequest(GetCalendarEventInstancesModel getCalendarEventInstances) {
        return InstancesCalendarEventReq.newBuilder()
                .calendarId(getCalendarEventInstances.getCalendarId())
                .eventId(getCalendarEventInstances.getEventId())
                .pageSize(getCalendarEventInstances.getPageSize())
                .pageToken(getCalendarEventInstances.getPageToken())
                .startTime(Objects.isNull(getCalendarEventInstances.getStartTime()) ? null :
                        String.valueOf(getCalendarEventInstances.getStartTime().atZone(ZoneId.of("Asia/Shanghai")).toInstant().getEpochSecond()))
                .endTime(Objects.isNull(getCalendarEventInstances.getEndTime()) ? null :
                        String.valueOf(getCalendarEventInstances.getEndTime().atZone(ZoneId.of("Asia/Shanghai")).toInstant().getEpochSecond()))
                .build();
    }

    public static ListCalendarEventAttendeeReq buildListCalendarEventAttendeeRequest(GetCalendarEventAttendeesModel getCalendarEventAttendees) {
        return ListCalendarEventAttendeeReq.newBuilder()
                .calendarId(getCalendarEventAttendees.getCalendarId())
                .eventId(getCalendarEventAttendees.getEventId())
                .pageSize(getCalendarEventAttendees.getPageSize())
                .pageToken(getCalendarEventAttendees.getPageToken())
                .build();
    }

    public static CreateCalendarEventAttendeeReq buildCreateCalendarEventAttendeeRequest(CreateCalendarEventAttendeesModel createCalendarEventAttendees) {
        ArrayList<CalendarEventAttendee> calendarEventAttendees = new ArrayList<>();
        for (AttendUserModel attendUser : createCalendarEventAttendees.getAttendUsers()) {
            calendarEventAttendees.add(CalendarEventAttendee.newBuilder()
                    .userId(attendUser.getUserId())
                    .isOrganizer(attendUser.getIsOrganizer())
                    .type(CalendarEventAttendeeEventAttendeeTypeEnum.USER)
                    .isOptional(false)
                    .build());
        }
        return CreateCalendarEventAttendeeReq.newBuilder()
                .calendarId(createCalendarEventAttendees.getCalendarId())
                .eventId(createCalendarEventAttendees.getEventId())
                .userIdType(CreateCalendarEventAttendeeUserIdTypeEnum.OPEN_ID)
                .createCalendarEventAttendeeReqBody(CreateCalendarEventAttendeeReqBody.newBuilder()
                        .attendees(calendarEventAttendees.toArray(new CalendarEventAttendee[0]))
                        .needNotification(true)
                        .build())
                .build();
    }

    public static BatchDeleteCalendarEventAttendeeReq buildBatchDeleteCalendarEventAttendeeRequest(DeleteCalendarEventAttendeesModel deleteCalendarEventAttendees) {
        ArrayList<CalendarEventAttendeeId> calendarEventAttendeeIds = new ArrayList<>();
        if (deleteCalendarEventAttendees.getUserIds() != null && !deleteCalendarEventAttendees.getUserIds().isEmpty()) {
            for (String userId : deleteCalendarEventAttendees.getUserIds()) {
                calendarEventAttendeeIds.add(CalendarEventAttendeeId.newBuilder()
                        .type(CalendarEventAttendeeIdEventAttendeeTypeEnum.USER)
                        .userId(userId)
                        .build());
            }
        }
        return BatchDeleteCalendarEventAttendeeReq.newBuilder()
                .calendarId(deleteCalendarEventAttendees.getCalendarId())
                .eventId(deleteCalendarEventAttendees.getEventId())
                .userIdType(BatchDeleteCalendarEventAttendeeUserIdTypeEnum.OPEN_ID)
                .batchDeleteCalendarEventAttendeeReqBody(BatchDeleteCalendarEventAttendeeReqBody.newBuilder()
                        .attendeeIds(deleteCalendarEventAttendees.getAttendeeIds() != null ?
                                deleteCalendarEventAttendees.getAttendeeIds().toArray(new String[0]) : new String[0])
                        .deleteIds(calendarEventAttendeeIds.toArray(new CalendarEventAttendeeId[0]))
                        .needNotification(true)
                        .build())
                .build();
    }
}
