package cn.pf.orch.feishu.config;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

/**
 * 飞书应用上下文
 * 生命周期为一次请求,用于user_token传递等;
 *
 */
@Setter
@Getter
public class FeishuAppContext {

    @JsonIgnore
    private static final ThreadLocal<FeishuAppContext> HOLDER = new ThreadLocal<>();

    /**
     * user_access_token
     */
    protected String userAccessToken;

    /**
     * openId
     */
    protected String openId;

    public static void set(FeishuAppContext context) {
        HOLDER.set(context);
    }

    public static FeishuAppContext get() {
        return HOLDER.get();
    }

    public static void remove() {
        HOLDER.remove();
    }
}
