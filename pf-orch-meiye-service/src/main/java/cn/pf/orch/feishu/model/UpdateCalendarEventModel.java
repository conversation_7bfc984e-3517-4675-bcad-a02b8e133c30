package cn.pf.orch.feishu.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpdateCalendarEventModel {

    // 日历ID
    String calendarId;

    // 日程ID
    String eventId;

    // 日程标题
    private String summary;

    // 日程描述
    private String description;

    // 日程开始时间
    private LocalDateTime startTime;

    // 日程结束时间
    private LocalDateTime endTime;

    // 全天，与开始结束时间互斥
    private LocalDateTime date;

    // 重复性规则
    private String recurrence;
}
