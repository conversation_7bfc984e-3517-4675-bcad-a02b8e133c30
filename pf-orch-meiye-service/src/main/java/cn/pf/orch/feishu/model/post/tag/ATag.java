package cn.pf.orch.feishu.model.post.tag;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ATag extends PostTag {

    /**
     * 链接文本
     */
    private String text;

    /**
     * 链接地址
     */
    private String href;

    @Override
    public String getTag() {
        return "a";
    }

    public ATag(String text, String href) {
        this.text = text;
        this.href = href;
    }
}
