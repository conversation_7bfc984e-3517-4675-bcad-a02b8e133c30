package cn.pf.orch.feishu.common;

public class FeishuCacheConstants {

    /**
     * tenant_token redis key
     */
    public static final String CACHE_TENANT_TOKEN = "GENN:CORE:FEISHU:TENANT_TOKEN";
    // 保存用户token
    public static final String CACHE_USER_ACCESS_CODE = "GENN:CORE:FEISHU:USER_ACCESS_TOKEN:";
    // 保存刷新用户token
    public static final String CACHE_REFRESH_USER_ACCESS_CODE = "GENN:CORE:FEISHU:REFRESH_USER_ACCESS_TOKEN:";
    // jsapi临时授权凭证
    public static final String CACHE_JS_SDK_TICKET = "GENN:CORE:FEISHU:JS_SDK_TICKET";
    // 企业信息
    public static final String CACHE_TENANT_INFO = "GENN:CORE:FEISHU:TENANT_INFO";
    // 飞书用户缓存
    public static final String CACHE_FEISHU_USER_INFO = "GENN:CORE:FEISHU:USER_INFO:";
    // 用户主日历缓存
    public static final String CACHE_CALENDAR_MAIN_INFO = "GENN:CORE:FEISHU:CALENDAR_MAIN_INFO:";


    public static String getCacheUserAccessCode(String userToken) {
        return CACHE_USER_ACCESS_CODE + userToken;
    }

    public static String getCacheRefreshUserAccessCode(String refreshToken) {
        return CACHE_REFRESH_USER_ACCESS_CODE + refreshToken;
    }

    public static String getCacheFeishuUserInfo(String openId) {
        return CACHE_FEISHU_USER_INFO + openId;
    }

    public static String getCalendarMainInfo(String openId) {
        return CACHE_CALENDAR_MAIN_INFO + openId;
    }

}
