// package cn.pf.orch.feishu.util;
//
// import cn.pf.orch.feishu.model.RecurrenceModel;
// import cn.pf.orch.feishu.model.enums.CycleEnum;
// import org.dmfs.rfc5545.DateTime;
// import org.dmfs.rfc5545.recur.Freq;
// import org.dmfs.rfc5545.recur.RecurrenceRule;
//
// public class RecurrenceUtil {
//
//     public static String getRecurrence(RecurrenceModel recurrenceModel) {
//         RecurrenceRule rule = null;
//         switch (recurrenceModel.getCycleEnum()) {
//             case DAILY:
//                 rule = new RecurrenceRule(Freq.DAILY, RecurrenceRule.RfcMode.RFC5545_STRICT);
//                 break;
//             case WEEKLY:
//                 rule = new RecurrenceRule(Freq.WEEKLY, RecurrenceRule.RfcMode.RFC5545_STRICT);
//                 break;
//             case MONTHLY:
//                 rule = new RecurrenceRule(Freq.MONTHLY, RecurrenceRule.RfcMode.RFC5545_STRICT);
//                 break;
//             case YEARLY:
//                 rule = new RecurrenceRule(Freq.YEARLY, RecurrenceRule.RfcMode.RFC5545_STRICT);
//                 break;
//             default:
//                 return null;
//         }
//         if (recurrenceModel.getInterval() != null) {
//             rule.setInterval(recurrenceModel.getInterval());
//         }
//         if (recurrenceModel.getCount() != null) {
//             rule.setCount(recurrenceModel.getCount());
//         }
//         if (recurrenceModel.getUntil() != null) {
//             rule.setUntil(recurrenceModel.getUntil());
//         }
//         return rule.toString();
//     }
//
//     public static void main(String[] args) {
//         RecurrenceModel recurrenceModel = RecurrenceModel.builder()
//                 .cycleEnum(CycleEnum.WEEKLY)
//                 .interval(2)
//                 .count(20)
//                 .until(DateTime.now())
//                 .build();
//         String recurrence = getRecurrence(recurrenceModel);
//         System.out.println("recurrence = " + recurrence);
//     }
// }
