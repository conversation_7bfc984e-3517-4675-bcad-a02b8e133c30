package cn.pf.orch.meiye.service.safetygenerationstandard.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.pf.orch.meiye.domain.safetygenerationstandard.po.ScoringSystemDetailTemplatePO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.query.ScoredDetailTemplateQuery;
import cn.pf.orch.meiye.mapper.ScoringSystemDetailTemplateMapper;
import cn.pf.orch.meiye.service.safetygenerationstandard.ScoringSystemDetailTemplateService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 煤矿管理项目详情表(ScoringSystemDetailTemplate)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-21 10:56:09
 */
@Service
public class ScoringSystemDetailTemplateServiceImpl extends ServiceImpl<ScoringSystemDetailTemplateMapper, ScoringSystemDetailTemplatePO> implements ScoringSystemDetailTemplateService {

    @Resource
    private ScoringSystemDetailTemplateMapper scoringSystemDetailTemplateMapper;

    @Override
    public List<ScoringSystemDetailTemplatePO> selectDetailTemplateByCondition(ScoredDetailTemplateQuery query) {
        LambdaQueryWrapper<ScoringSystemDetailTemplatePO> wrapper = Wrappers.lambdaQuery(ScoringSystemDetailTemplatePO.class)
                .eq(ObjectUtil.isNotNull(query.getScoringSystemId()), ScoringSystemDetailTemplatePO::getScoringSystemId, query.getScoringSystemId())
                .eq(ObjectUtil.isNotNull(query.getId()), ScoringSystemDetailTemplatePO::getId, query.getId());
        return scoringSystemDetailTemplateMapper.selectList(wrapper);
    }

    @Override
    public ScoringSystemDetailTemplatePO selectById(Long id) {
        return scoringSystemDetailTemplateMapper.selectById(id);
    }
}

