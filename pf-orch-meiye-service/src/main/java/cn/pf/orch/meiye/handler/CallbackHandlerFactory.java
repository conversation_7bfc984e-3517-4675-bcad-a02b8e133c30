package cn.pf.orch.meiye.handler;

import cn.pf.orch.meiye.enums.feishu.EventCallbackEnum;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 回调工厂
 * @date 2024-12-25
 */
@Component
public class CallbackHandlerFactory {

    @Resource
    private Map<EventCallbackEnum, CallbackHandler> callbackHandlerMap;

    public <T> CallbackHandler<T> getCallbackHandler(EventCallbackEnum event) {
        return callbackHandlerMap.get(event);
    }
}
