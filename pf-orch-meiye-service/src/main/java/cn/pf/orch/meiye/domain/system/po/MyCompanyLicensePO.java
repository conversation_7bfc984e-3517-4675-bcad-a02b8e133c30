package cn.pf.orch.meiye.domain.system.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * MyCompanyLicensePO对象
 *
 * <AUTHOR>
 * @desc 公司许可证
 */
@Data
@Accessors(chain = true)
@TableName(value = "my_company_license", autoResultMap = true)
public class MyCompanyLicensePO {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 公司id
     */
    @TableField("company_id")
    private Long companyId;

    /**
     * 许可证类型
     */
    @TableField("type")
    private String type;

    /**
     * 许可证名称
     */
    @TableField("name")
    private String name;

    /**
     * 文件id
     */
    @TableField("licenses")
    private String licenses;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;

    /**
     * 创建用户名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

}

