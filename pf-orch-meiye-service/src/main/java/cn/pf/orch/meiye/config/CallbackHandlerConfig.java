package cn.pf.orch.meiye.config;

import cn.pf.orch.meiye.enums.feishu.EventCallbackEnum;
import cn.pf.orch.meiye.handler.CallbackHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 回调处理器配置
 * @date 2024-12-25
 */
@Configuration
public class CallbackHandlerConfig {

    @Resource
    private List<CallbackHandler> callbackHandlers;

    /**
     * 订阅回调处理器
     * @return
     */
    @Bean
    public Map<EventCallbackEnum, CallbackHandler> callbackHandlerMap() {
        return callbackHandlers.stream().collect(
                java.util.stream.Collectors.toMap(CallbackHandler::event, java.util.function.Function.identity()));
    }



}
