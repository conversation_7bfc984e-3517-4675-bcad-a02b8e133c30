package cn.pf.orch.meiye.interfaces.system;

import cn.pf.orch.meiye.domain.system.dto.MyResourceTreeDTO;
import cn.pf.orch.meiye.service.system.ResourceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 资源管理
 * <AUTHOR>
 */
@Api(tags = "系统管理-资源管理")
@RestController
@RequestMapping("/resource")
public class ResourceController {

    @Resource
    private ResourceService resourceService;

    /**
     * 查询用户权限范围内的资源树
     * @return
     */
    @PostMapping("/queryTreeRange")
    @ApiOperation(value = "查询用户权限范围内的资源树")
    public List<MyResourceTreeDTO> queryTreeRange(){
        return resourceService.queryTreeRange();
    }

}
