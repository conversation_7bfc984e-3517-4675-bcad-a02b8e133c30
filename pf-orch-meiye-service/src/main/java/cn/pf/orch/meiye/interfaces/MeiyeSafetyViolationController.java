package cn.pf.orch.meiye.interfaces;

import cn.pf.orch.meiye.config.CurrentUserHolder;
import cn.pf.orch.meiye.domain.safetyviolation.command.AddCommand;
import cn.pf.orch.meiye.domain.safetyviolation.command.UpdateCommand;
import cn.pf.orch.meiye.domain.safetyviolation.dto.MeiyeSafetyViolationDTO;
import cn.pf.orch.meiye.domain.safetyviolation.query.PageQuery;
import cn.pf.orch.meiye.service.safetyviolation.MeiyeSafetyViolationService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;

@RestController
@Api(tags = "三违模块")
@RequestMapping("/safetyviolation")
public class MeiyeSafetyViolationController {

    @Resource
    private MeiyeSafetyViolationService service;


    @ApiOperation(value = "新增")
    @PostMapping("/add")
    public void add(@RequestBody
                        @Validated
                        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
                        AddCommand command) {
        service.add(command);
    }


    @ApiOperation(value = "更新")
    @PostMapping("update")
    public void update(@RequestBody
                           @Validated
                           @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
                           UpdateCommand command) {
        service.update(command);
    }


    @ApiOperation(value = "删除")
    @GetMapping("delete")
    public void delete(@NotNull Long id) {
        service.delete(id);
    }


    @ApiOperation(value = "分页")
    @PostMapping("query")
    public Page<MeiyeSafetyViolationDTO> query(@RequestBody @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) PageQuery query) {
        List<Long> rangeCompanyIds = CurrentUserHolder.getRangeCompanyIds();
        query.setRangeCompanyIds(rangeCompanyIds);
        return service.page(query);
    }

    @ApiOperation("详情")
    @GetMapping("detail")
    public MeiyeSafetyViolationDTO detail(@NotNull Long id) {
        return service.detail(id);
    }
}
