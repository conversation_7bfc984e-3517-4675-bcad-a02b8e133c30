package cn.pf.orch.meiye.mapper;

import cn.pf.orch.meiye.domain.system.po.MyResourcePO;
import cn.pf.orch.meiye.enums.AuthTypeEnum;
import cn.pf.orch.meiye.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MyResourceMapper extends BaseMapper<MyResourcePO> {

    default List<MyResourcePO> selectByAuthType(AuthTypeEnum authType){
        LambdaQueryWrapper<MyResourcePO> wrapper = Wrappers.lambdaQuery(MyResourcePO.class)
                .in(MyResourcePO::getAuthType, AuthTypeEnum.arrange(authType))
                .eq(MyResourcePO::getStatus, StatusEnum.ENABLE);
        return selectList(wrapper);
    }

    List<MyResourcePO> selectByRoleId(Long roleId);

    List<MyResourcePO> selectByOpenId(String userId);
}
