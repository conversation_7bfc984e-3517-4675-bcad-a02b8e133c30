package cn.pf.orch.meiye.mapper;

import cn.pf.orch.meiye.domain.system.po.MyUserRoleRelPO;
import cn.pf.orch.meiye.enums.RoleTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MyUserRoleRelMapper extends BaseMapper<MyUserRoleRelPO> {


    default List<MyUserRoleRelPO> selectByRoleIds(List<Long> roleIds) {
        LambdaQueryWrapper<MyUserRoleRelPO> wrapper = Wrappers.lambdaQuery(MyUserRoleRelPO.class)
                .in(MyUserRoleRelPO::getRoleId, roleIds);
        return selectList(wrapper);
    }

    default List<MyUserRoleRelPO> selectByUserId(String userId) {
        LambdaQueryWrapper<MyUserRoleRelPO> wrapper = Wrappers.lambdaQuery(MyUserRoleRelPO.class)
                .eq(MyUserRoleRelPO::getUserId, userId);
        return selectList(wrapper);
    }

    default void deleteByUserIdAndCustomize(String userId) {
        LambdaQueryWrapper<MyUserRoleRelPO> wrapper = Wrappers.lambdaQuery(MyUserRoleRelPO.class)
                .eq(MyUserRoleRelPO::getUserId, userId)
                .eq(MyUserRoleRelPO::getRoleType, RoleTypeEnum.CUSTOMIZE);
        delete(wrapper);
    }

    default void deleteByUserIds(List<String> userIds) {
        LambdaQueryWrapper<MyUserRoleRelPO> wrapper = Wrappers.lambdaQuery(MyUserRoleRelPO.class)
                .in(MyUserRoleRelPO::getUserId, userIds);
        delete(wrapper);
    }

    void saveBatch(List<MyUserRoleRelPO> list);
}
