package cn.pf.orch.meiye.domain.feishu;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SendSecurityDTO {

    // 单位名称
    private String companyName;

    // 敏感信息类别
    private String sensitiveCategory;

    // 敏感信息描述
    private String sensitiveDescription;

    // 管控措施
    private String controlMeasures;

    // 责任人部门
    private String responsibleDepartment;

    // 责任人
    private String responsiblePerson;

    // 责任人工号
    private String responsiblePersonNumber;

    // 管控结果
    private String controlResult;

    // 通知人openid
    private List<String> openIds;

}
