package cn.pf.orch.meiye.domain.safetygenerationstandard.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@ApiModel(value = "查询体系详情模板信息")
@AllArgsConstructor
public class ScoredDetailTemplateQuery {

    private Long id;

    @ApiModelProperty(value = "管理项id")
    private Long scoringSystemId;

    @ApiModelProperty(value = "项目内容")
    private String itemContent;

    public ScoredDetailTemplateQuery(Long scoringSystemId) {
        this.scoringSystemId = scoringSystemId;
    }

}
