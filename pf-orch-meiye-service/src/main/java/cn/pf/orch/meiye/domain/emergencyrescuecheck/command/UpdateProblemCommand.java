package cn.pf.orch.meiye.domain.emergencyrescuecheck.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class UpdateProblemCommand {

    @ApiModelProperty(value = "问题id ")
    @NotNull
    private Long id;

    @ApiModelProperty(value = "问题描述")
    private String description;

    @ApiModelProperty(value = "问题附件，多个附件key用英文逗号分隔")
    private String attachment;
}
