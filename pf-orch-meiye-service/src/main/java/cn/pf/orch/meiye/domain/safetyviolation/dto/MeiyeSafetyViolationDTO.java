package cn.pf.orch.meiye.domain.safetyviolation.dto;

import cn.pf.orch.meiye.enums.DelEnum;
import cn.pf.orch.meiye.enums.DiscovererUnitsEnum;
import cn.pf.orch.meiye.enums.DiscoveryMethodEnum;
import cn.pf.orch.meiye.enums.ViolationTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@ApiModel("三违信息")
public class MeiyeSafetyViolationDTO {
    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "煤矿ID", example = "123")
    private Long companyId;

    @ApiModelProperty(value = "煤矿名称", example = "某某煤矿")
    private String companyName;

    @ApiModelProperty(value = "违章经过及原因", example = "未佩戴安全帽进入作业区域")
    private String violationDetails;

    @ApiModelProperty(value = "三违性质", example = "严重违章")
    private ViolationTypeEnum violationType;

    @ApiModelProperty(value = "被查出人名称", example = "张三")
    private String personName;

    @ApiModelProperty(value = "被查出人单位", example = "采煤一队")
    private String personDepartment;

    @ApiModelProperty(value = "被查出人工号", example = "CM001")
    private String personJobNumber;

    @ApiModelProperty(value = "被查出人openId", example = "oXZ8Y5XZ8Y5XZ8Y5XZ8Y5XZ8Y5XZ8")
    private String personJobOpenid;

    @ApiModelProperty(value = "被查出人班别")
    private String personShift;

    @ApiModelProperty(value = "班次")
    private String classes;

    @ApiModelProperty(value = "被查出人职务", example = "采煤工")
    private String personPosition;

    @ApiModelProperty(value = "违章日期", example = "2023-01-15")
    private LocalDate violationDate;

    @ApiModelProperty(value = "违章地点ID", example = "456")
    private Long violationLocationId;

    @ApiModelProperty(value = "地点名称", example = "3号采煤工作面")
    private String violationLocationName;

    @ApiModelProperty(value = "地点全路径")
    private String locationFullPathName;

    @ApiModelProperty(value = "详细地点")
    private String detailLocationName;

    @ApiModelProperty(value = "报送日期", example = "2023-01-16")
    private LocalDate reportDate;

    @ApiModelProperty(value = "处理措施", example = "停工学习3天")
    private String punishment;

    @ApiModelProperty(value = "处罚条款", example = "安全规程第23条")
    private String clause;

    @ApiModelProperty(value = "罚款金额(元)", example = "500.00")
    private Double fine;

    @ApiModelProperty(value = "查出方式", example = "现场检查")
    private DiscoveryMethodEnum discoveryMethod;

    @ApiModelProperty(value = "查出人", example = "李四")
    private String discoverer;

    @ApiModelProperty(value = "查处人工号", example = "李四")
    private String discovererNumber;

    @ApiModelProperty(value = "查处人openid", example = "李四")
    private String discovererOpenId;

    @ApiModelProperty(value = "查处人部门名称", example = "李四")
    private String discovererDepartment;

    @ApiModelProperty(value = "查处人部门id", example = "李四")
    private String discovererDepartmentId;

    @ApiModelProperty(value = "是否删除(0:未删除,1:已删除)", example = "0")
    private DelEnum del;

    @ApiModelProperty(value = "创建人id", example = "user001")
    private String createUserId;

    @ApiModelProperty(value = "创建人", example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "更新人id", example = "user001")
    private String updateUserId;

    @ApiModelProperty(value = "更新人", example = "管理员")
    private String updateUserName;

    @ApiModelProperty(value = "创建时间", example = "2023-01-16 10:00:00")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间", example = "2023-01-16 10:30:00")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "查出人单位类别",example = "集团，区域，煤矿")
    private DiscovererUnitsEnum discovererUnits;

}
