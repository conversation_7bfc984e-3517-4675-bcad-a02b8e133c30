package cn.pf.orch.meiye.assembler;

import cn.pf.orch.meiye.domain.securitysensitive.command.AddCommand;
import cn.pf.orch.meiye.domain.securitysensitive.command.UpdateCommand;
import cn.pf.orch.meiye.domain.securitysensitive.dto.MeiyeSecuritySensitiveInfoDTO;
import cn.pf.orch.meiye.domain.securitysensitive.po.MeiyeSecuritySensitiveInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MeiyeSecuritySensitiveInfoAssmbler {

    MeiyeSecuritySensitiveInfo addCommandToPo(AddCommand command);

    MeiyeSecuritySensitiveInfo updateCommandToPo(UpdateCommand command);

    @Mapping(target = "responsiblePersonOpenId", source = "responsiblePersonOpenId")
    MeiyeSecuritySensitiveInfoDTO poToDto(MeiyeSecuritySensitiveInfo po);
    List<MeiyeSecuritySensitiveInfoDTO> poToDtos(List<MeiyeSecuritySensitiveInfo> po);

}
