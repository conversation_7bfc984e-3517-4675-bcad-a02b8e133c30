package cn.pf.orch.meiye.domain.emergencyrescuecheck.po;

import java.util.Date;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

/**
 * 检查项目模板表(MeiyeInspectionTemplate)表实体类
 *
 * <AUTHOR>
 * @since 2025-04-15 11:29:44
 */
@Data
public class MeiyeInspectionTemplate extends Model<MeiyeInspectionTemplate> {

    private Long id;
    //检查类型
    private String inspectionType;
    //项目名称
    private String project;
    //检查内容
    private String content;
    //检查主要资料及方法
    private String materialsMethods;
    // 序号
    private Integer serialNumber;
    //依据链接
    private String referenceLink;
    //创建人id
    private String createUserId;
    //修改人id
    private String updateUserId;

    private Date createTime;

    private Date updateTime;

}

