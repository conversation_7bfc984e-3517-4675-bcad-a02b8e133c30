package cn.pf.orch.meiye.aspect;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.pf.orch.meiye.config.CurrentUserHolder;
import cn.pf.orch.meiye.domain.system.po.MyUserLogPO;
import cn.pf.orch.meiye.enums.OperateTypeEnum;
import cn.pf.orch.meiye.mapper.MyUserLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Valid
@Aspect
@Component
public class UserLogAspect {

    @Resource
    private MyUserLogMapper myUserLogMapper;

    @Pointcut("@annotation(userLog)")
    public void logOperationPointcut(UserLog userLog) {
    }

    @AfterReturning(pointcut = "logOperationPointcut(userLog)", returning = "result", argNames = "joinPoint,userLog,result")
    public void afterReturning(JoinPoint joinPoint, UserLog userLog, Object result) {
        try {
            // 获取方法参数
            Object[] args = joinPoint.getArgs();

            // 获取方法参数名
            MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
            Method method = methodSignature.getMethod();
            Class<?> declaringClass = method.getDeclaringClass();
            String[] parameterNames = methodSignature.getParameterNames();

            log.info(String.format("[日志切面]切点的方法名：%s 切点的类名：%s", methodSignature.getName(), declaringClass.getName()));

            // 构建参数名到参数值的映射
            StringBuilder requestSb = new StringBuilder().append("[");
            Map<String, Object> paramMap = new HashMap<>();
            for (int i = 0; i < parameterNames.length; i++) {
                paramMap.put(parameterNames[i], args[i]);
                requestSb.append("\"").append(parameterNames[i]).append("\"").append(":").append(args[i]).append(",");
            }
            requestSb.append("]");

            // 将返回值放入上下文中
            paramMap.put("result", result);

            // 获取上下文中的参数
            Map<String, Object> contextMap = UserOperationContext.context.get();
            paramMap.putAll(contextMap);
            // 登录上下文
            if (CurrentUserHolder.hasCurrentUser()) {
                log.info(String.format("[日志切面]登录上下文: %s", JsonUtils.toJson(CurrentUserHolder.getCurrentUser())));
                paramMap.put("username", CurrentUserHolder.getName());
                paramMap.put("fsOpenId", CurrentUserHolder.getOpenId());
            }

            // 构建SpEL上下文
            StandardEvaluationContext context = new StandardEvaluationContext();
            for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
                context.setVariable(entry.getKey(), entry.getValue());
            }

            // 解析detail字段
            String detail = userLog.detail();
            ExpressionParser parser = new SpelExpressionParser();
            if (userLog.detail().contains("#")) {
                detail = parser.parseExpression(userLog.detail()).getValue(context, String.class);
            }

            // 操作类型
            OperateTypeEnum operateTypeEnum = userLog.operateType();
            if (paramMap.containsKey("operateType")) {
                operateTypeEnum = (OperateTypeEnum) paramMap.get("operateType");
            }

            // 记录日志
            String res = JsonUtils.toJson(result);
            MyUserLogPO userLogPO = MyUserLogPO.builder()
                    .operateType(operateTypeEnum)
                    .companyId(CurrentUserHolder.hasCompanyId()?CurrentUserHolder.getCompanyId():0)
                    .request(requestSb.length() > 1024 * 5 ? requestSb.substring(0, 1024 * 5) : requestSb.toString())
                    .result(res.length() > 1024 * 5 ? res.substring(0, 1024 * 5) : res)
                    .detail(detail.replaceAll("null", "")).build();
            myUserLogMapper.insert(userLogPO);
        } catch (Exception ex) {
            log.error(String.format("[用户日志切面]操作(%s), 详情(%s)处理失败, 失败原因: %s",
                            userLog.operateType(), userLog.detail(), ex.getMessage()),
                    ex);
        } finally {
            // 清除上下文
            UserOperationContext.clear();
        }
    }
}
