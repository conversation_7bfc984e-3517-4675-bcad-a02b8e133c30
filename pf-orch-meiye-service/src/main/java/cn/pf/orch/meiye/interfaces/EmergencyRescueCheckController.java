package cn.pf.orch.meiye.interfaces;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.pf.orch.meiye.assembler.EmergencyRescueCheckAssembler;
import cn.pf.orch.meiye.config.CurrentUserHolder;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.command.AddProblemCommand;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.command.UpdateProblemCommand;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.dto.CheckTemplateDTO;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.dto.EmergencyRescueCheckInfoDTO;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.dto.InspectionDropDownDTO;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.po.MeiyeGist;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.query.DetailQuery;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.query.EmergencyRescueCheckInfoQuery;
import cn.pf.orch.meiye.service.emergencyrescuecheck.MeiyeCompanyProblemRecodeService;
import cn.pf.orch.meiye.service.emergencyrescuecheck.MeiyeGistService;
import cn.pf.orch.meiye.service.emergencyrescuecheck.MeiyeInspectionTemplateService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Api(tags = "应急救援检查")
@RestController
@RequestMapping("/emergencyrescuecheck")
public class EmergencyRescueCheckController {

    @Resource
    private MeiyeInspectionTemplateService inspectionTemplateService;

    @Resource
    private MeiyeGistService meiyeGistService;

    @Resource
    private MeiyeCompanyProblemRecodeService companyProblemRecodeService;

    @Resource
    private EmergencyRescueCheckAssembler assembler;


    @ApiOperation("获取煤矿检查模块项")
    @PostMapping("/queryEmergencyCheckInfo")
    public Page<EmergencyRescueCheckInfoDTO> queryCheckTemplate(@RequestBody EmergencyRescueCheckInfoQuery query, HttpServletRequest request) {
        String source = request.getHeader("X-Request-Source");
        List<Long> rangeCompanyIds = CurrentUserHolder.getRangeCompanyIds();
        query.setRangeCompanyIds(rangeCompanyIds);
        query.setSource(Optional.ofNullable(source).orElse("app"));
        return inspectionTemplateService.queryEmergencyCheckInfo(query);
    }

    @ApiOperation("详情信息")
    @PostMapping("/queryCheckTemplateDetail")
    public EmergencyRescueCheckInfoDTO queryCheckTemplateDetail(@RequestBody @Validated DetailQuery query) {
        Page<EmergencyRescueCheckInfoDTO> emergencyRescueCheckInfoDTOPage = inspectionTemplateService.queryEmergencyCheckInfo(assembler.detailQueryToQuery(query));
        return Optional.ofNullable(emergencyRescueCheckInfoDTOPage)
                .map(Page::getRecords)
                .filter(list -> !list.isEmpty())
                .map(list -> list.get(0))
                .orElse(null);
    }


    // 查看检查表
    @ApiOperation("查看检查表")
    @GetMapping("/checkTemplates")
    public Map<String, List<CheckTemplateDTO>> checkTemplates(){
        return inspectionTemplateService.checkTemplates();
    }


    // 查看依据
    @ApiOperation("查看依据")
    @GetMapping("/checkGist")
    public List<MeiyeGist> checkGist(@NotEmpty String referenceLink){
        List<String> list = Arrays.asList(referenceLink.split(","));
        log.info("checkGist param:{},list:{}", referenceLink, list);
        return meiyeGistService.queryByIds(list);
    }

    // 新增问题
    @ApiOperation("新增问题")
    @PostMapping("addProblem")
    public void addProblem(@RequestBody @Validated AddProblemCommand addProblem){
        companyProblemRecodeService.addProblem(addProblem);
    }

    // 编辑问题
    @ApiOperation("编辑问题")
    @PostMapping("/updateProblem")
    public void updateProblem(@RequestBody @Validated UpdateProblemCommand updateProblem){
        companyProblemRecodeService.updateById(updateProblem);
    }

    @ApiOperation("获取下拉数据")
    @GetMapping("/dropDown")
    public List<InspectionDropDownDTO> dropDown(){
        return inspectionTemplateService.dropDown();
    }


    /**
     *
     * @param id = 问题id 不是 模板id
     */
    @ApiOperation("删除问题")
    @GetMapping("/delete")
    public void deleteProblem(@NotNull Long id){
        companyProblemRecodeService.delete(id);
    }

}
