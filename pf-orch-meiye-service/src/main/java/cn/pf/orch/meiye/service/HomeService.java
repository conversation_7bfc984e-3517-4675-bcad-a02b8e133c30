package cn.pf.orch.meiye.service;

import cn.genn.core.model.page.PageResultDTO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.pf.orch.meiye.assembler.HomeAssembler;
import cn.pf.orch.meiye.config.CurrentUserHolder;
import cn.pf.orch.meiye.domain.risk.po.MyApprovalLogPO;
import cn.pf.orch.meiye.domain.system.dto.HomeMessagesDTO;
import cn.pf.orch.meiye.domain.system.dto.HomeTodoDTO;
import cn.pf.orch.meiye.domain.system.query.HomePageQuery;
import cn.pf.orch.meiye.mapper.MyApprovalLogMapper;
import cn.pf.orch.meiye.mapper.MyApprovalMapper;
import cn.pf.orch.meiye.mapper.RiskInfoMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class HomeService {

    @Resource
    private RiskInfoMapper riskInfoMapper;
    @Resource
    private MyApprovalMapper approvalMapper;
    @Resource
    private MyApprovalLogMapper approvalLogMapper;
    @Resource
    private HomeAssembler assembler;


    public PageResultDTO<HomeMessagesDTO> messagesPage(HomePageQuery query) {
        List<Long> rangeCompanyIds = CurrentUserHolder.getRangeCompanyIds();
        query.setCompanyIds(rangeCompanyIds);
        query.setOpenId(CurrentUserHolder.getOpenId());
        PageResultDTO<HomeMessagesDTO> pageResult = assembler.messageToPage(approvalMapper.homeMessagePage(new Page<>(query.getPageNo(), query.getPageSize()), query));
        if(CollUtil.isNotEmpty(pageResult.getList())){
            //审核人,审核意见填充
            List<String> approvalIds = pageResult.getList().stream().map(HomeMessagesDTO::getApprovalId).distinct().collect(Collectors.toList());
            List<MyApprovalLogPO> logPOS = approvalLogMapper.selectLastApprovalIds(approvalIds);
            Map<String, MyApprovalLogPO> logMap = logPOS.stream().collect(Collectors.toMap(MyApprovalLogPO::getApprovalId, Function.identity()));
            for (HomeMessagesDTO dto : pageResult.getList()) {
                MyApprovalLogPO myApprovalLogPO = logMap.get(dto.getApprovalId());
                if(ObjUtil.isNotNull(myApprovalLogPO)){
                    dto.setComment(myApprovalLogPO.getComment());
                    dto.setApprovalOwner(myApprovalLogPO.getOpenId());
                    dto.setApprovalUserName(myApprovalLogPO.getUserName());
                }
            }
        }
        return pageResult;
    }

    public PageResultDTO<HomeTodoDTO> todoPage(HomePageQuery query) {
        List<Long> rangeCompanyIds = CurrentUserHolder.getRangeCompanyIds();
        query.setCompanyIds(rangeCompanyIds);
        query.setOpenId(CurrentUserHolder.getOpenId());
        PageResultDTO<HomeTodoDTO> pageResult = assembler.todoToPage(riskInfoMapper.homeTodoPage(new Page<>(query.getPageNo(), query.getPageSize()), query));
        return pageResult;
    }
}
