package cn.pf.orch.meiye.domain.location.po;

import java.time.LocalDateTime;
import java.util.Date;

import cn.genn.core.model.enums.DeletedEnum;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 地点信息表(MeiyeLocation)表实体类
 *
 * <AUTHOR>
 * @since 2025-04-11 08:48:50
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("meiye_location")
public class MeiyeLocation extends Model<MeiyeLocation> {
    //主键ID
    private Long id;
    //上级地点id
    private Long parentId;
    //地点名称
    private String name;
    //当前地点级别
    private Integer level;
    //全路径名称(如"中国|广东省|深圳市")
    private String fullPathName;
    //全路径ID(如"1|5|12")
    private String fullPathId;
    @TableField("company_id")
    private Long companyId;
    @TableField("company_name")
    private String companyName;
    //创建时间
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    //创建人
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;

    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    //更新时间
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    //更新人
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    @TableField(value = "deleted")
    @TableLogic
    private DeletedEnum deleted;

}

