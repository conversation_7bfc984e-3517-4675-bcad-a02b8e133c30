package cn.pf.orch.meiye.exception;

import cn.genn.core.exception.BaseException;
import cn.genn.core.exception.CommonCode;
import cn.genn.core.exception.MessageCodeWrap;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AuthException extends BaseException {
    private static final long serialVersionUID = -3702323089829534951L;

    public AuthException() {
        super(CommonCode.FAIL);
    }

    public AuthException(String message) {
        super(CommonCode.FAIL.buildCode(), message);
    }

    public AuthException(String code, String message) {
        super(code, message);
    }

    public AuthException(String code, String message, Throwable throwable) {
        super(code, message, throwable);
    }

    public AuthException(MessageCodeWrap messageCode, Object... args) {
        super(messageCode, args);
    }
}
