package cn.pf.orch.meiye.domain.risk.command;

import cn.pf.orch.meiye.enums.IdentifyRiskDetailEnum;
import cn.pf.orch.meiye.enums.IdentifyTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
public class MyRiskIdentifySaveCommand {

    @ApiModelProperty(value = "名称")
    @NotBlank(message = "名称不能为空")
    private String name;

    @ApiModelProperty(value = "公司id")
    @NotNull(message = "煤矿不能为空")
    private Long companyId;

    @ApiModelProperty(value = "辨识类型")
//    @NotNull(message = "辨识类型不能为空")
    private IdentifyTypeEnum identifyType;

    @ApiModelProperty(value = "类别详情")
    private IdentifyRiskDetailEnum identifyDetail;

    @ApiModelProperty(value = "责任人")
    @NotBlank(message = "责任人不能为空")
    private String identifyOwner;

    @ApiModelProperty(value = "责任人名称")
    @NotBlank(message = "责任人名称不能为空")
    private String identifyOwnerName;

    @ApiModelProperty(value = "开始时间")
    @NotNull(message = "辨识日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    @NotNull(message = "截止日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

}
