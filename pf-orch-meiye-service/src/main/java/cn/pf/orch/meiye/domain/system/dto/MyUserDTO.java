package cn.pf.orch.meiye.domain.system.dto;

import cn.genn.core.model.enums.DeletedEnum;
import cn.pf.orch.meiye.enums.ApprovalSignEnum;
import cn.pf.orch.meiye.enums.AuthTypeEnum;
import cn.pf.orch.meiye.enums.GenderEnum;
import cn.pf.orch.meiye.enums.UserTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * MyUserDTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MyUserDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户的open_id，应用内用户的唯一标识")
    private String id;

    @ApiModelProperty(value = "用户名")
    private String name;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "性别")
    private GenderEnum gender;

    @ApiModelProperty(value = "权限类型（集团，区域公司，煤矿）")
    private AuthTypeEnum authType;

    @ApiModelProperty(value = "所属公司")
    private Long companyId;

    @ApiModelProperty(value = "所属公司名称")
    private String companyName;

    @ApiModelProperty(value = "头像的文件Key")
    private String avatarKey;

    @ApiModelProperty(value = "用户头像信息")
    private String avatarOrigin;

    @ApiModelProperty(value = "工号")
    private String employeeNo;

    @ApiModelProperty(value = "员工类型")
    private Integer employeeType;

    @ApiModelProperty(value = "用户类型(1:内部;2外部)")
    private UserTypeEnum type;

    @ApiModelProperty(value = "企业邮箱")
    private String enterpriseEmail;

    @ApiModelProperty(value = "是否暂停用户")
    private Boolean isFrozen;

    @ApiModelProperty(value = "审批标签")
    private ApprovalSignEnum approvalSign;

    @ApiModelProperty(value = "逻辑删除（0：未删除；1：删除）")
    private DeletedEnum deleted;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建用户ID")
    private String createUserId;

    @ApiModelProperty(value = "创建用户名")
    private String createUserName;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新用户ID")
    private String updateUserId;

    @ApiModelProperty(value = "更新用户名")
    private String updateUserName;

    @ApiModelProperty(value = "角色信息")
    private List<MyUserRoleRelDTO> roleRel = new ArrayList<>();

    @ApiModelProperty(value = "角色拼接")
    private String roleNames;

    @ApiModelProperty(value = "系统角色")
    private MyUserRoleRelDTO systemRole;

    @ApiModelProperty(value = "自定义角色")
    private List<MyUserRoleRelDTO> customizeRole = new ArrayList<>();

}

