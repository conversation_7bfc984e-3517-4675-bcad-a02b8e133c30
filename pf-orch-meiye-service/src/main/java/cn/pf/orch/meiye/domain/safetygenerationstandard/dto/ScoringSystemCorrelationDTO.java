package cn.pf.orch.meiye.domain.safetygenerationstandard.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 管理项评分列表
 */
@Data
@ApiModel("管理项评分列表")
public class ScoringSystemCorrelationDTO {

    @ApiModelProperty(value = "管理部分得分表ID")
    private Long scoringSystemCorrelationId;

    @ApiModelProperty(value = "管理项名称")
    private String commonType;

    @ApiModelProperty(value = "标准分")
    private BigDecimal standardScore;

    @ApiModelProperty(value = "权重")
    private BigDecimal weight;

    @ApiModelProperty(value = "实际得分")
    private BigDecimal actualScore;

    @ApiModelProperty(value = "实际进度", hidden = true)
    private BigDecimal progress;

    @ApiModelProperty(value = "已评分数量")
    private long scoredNumber;

    @ApiModelProperty(value = "项目总数")
    private long totalSize;
}
