package cn.pf.orch.meiye.service.safetygenerationstandard;

import cn.pf.orch.meiye.domain.safetygenerationstandard.command.ScoredSystemDetailCommand;
import cn.pf.orch.meiye.domain.safetygenerationstandard.dto.ScoredStatusNumDTO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.dto.ScoringSystemDetailCorrelationDTO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.po.ScoringDetailCorrelationPO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.query.ScoredStatusNumQuery;
import cn.pf.orch.meiye.domain.safetygenerationstandard.query.ScoredSystemDetailQuery;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 管理项明细评分记录表(ScoringDetailCorrelation)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-22 16:49:34
 */
public interface ScoringDetailCorrelationService extends IService<ScoringDetailCorrelationPO> {


    /**
     * 初始化煤矿评分体系详情信息
     * @param scoredRecordId: 煤矿安全体系id
     * @param scoredSystemTemplateId: 管理项模板id
     * @param scoredSystemCorrelationId: 管理项评分表id
     */
    void initCompanyScoredDetail(Long scoredRecordId, Long scoredSystemTemplateId, Long scoredSystemCorrelationId);


    /**
     * 查询管理项明细评分列表
     * @param query
     * @return
     */
    List<ScoringSystemDetailCorrelationDTO> queryScoringSystemDetailCorrelationList(ScoredSystemDetailQuery query);


    /**
     * 管理项明细评分
     * @param scoreds
     */
    void scoring(List<ScoredSystemDetailCommand> scoreds);


    /**
     * 查询管理项明细评分状态数量
     * @return
     */
    ScoredStatusNumDTO queryScoredStatusNum(ScoredStatusNumQuery query);


    void deleteByScoringRecordId(Long scoringRecordId);


}

