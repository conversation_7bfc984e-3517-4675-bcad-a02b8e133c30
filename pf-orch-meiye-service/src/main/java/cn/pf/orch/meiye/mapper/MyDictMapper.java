package cn.pf.orch.meiye.mapper;

import cn.genn.core.model.enums.DeletedEnum;
import cn.hutool.core.collection.CollUtil;
import cn.pf.orch.meiye.domain.system.po.DictPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MyDictMapper extends BaseMapper<DictPO> {

    default List<DictPO> queryDict(List<Integer> dictTypeIdList) {
        LambdaQueryWrapper<DictPO> wrapper = Wrappers.lambdaQuery(DictPO.class)
                .in(CollUtil.isNotEmpty(dictTypeIdList), DictPO::getDictTypeId, dictTypeIdList)
                .eq(DictPO::getDeleted, DeletedEnum.NOT_DELETED);
        return selectList(wrapper);
    }
}
