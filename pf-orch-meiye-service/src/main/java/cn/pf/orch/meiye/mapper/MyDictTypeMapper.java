package cn.pf.orch.meiye.mapper;

import cn.hutool.core.collection.CollUtil;
import cn.pf.orch.meiye.domain.system.po.DictTypePO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MyDictTypeMapper extends BaseMapper<DictTypePO> {

    default List<DictTypePO> queryDictType(List<String> dictTypeList) {
        LambdaQueryWrapper<DictTypePO> wrapper = Wrappers.lambdaQuery(DictTypePO.class)
                .in(CollUtil.isNotEmpty(dictTypeList), DictTypePO::getDictType, dictTypeList);
        return selectList(wrapper);
    }
}
