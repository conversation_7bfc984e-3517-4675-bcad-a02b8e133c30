package cn.pf.orch.meiye.domain.system.query;

import cn.genn.core.model.page.PageSortQuery;
import cn.pf.orch.meiye.enums.AuthTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/6
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class MyRolePageQuery extends PageSortQuery implements Serializable {

    @ApiModelProperty(value = "名称")
    @Size(max = 32, message = "名称长度不能大于32")
    private String name;

    @ApiModelProperty(value = "公司层级")
    private AuthTypeEnum authType;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

}
