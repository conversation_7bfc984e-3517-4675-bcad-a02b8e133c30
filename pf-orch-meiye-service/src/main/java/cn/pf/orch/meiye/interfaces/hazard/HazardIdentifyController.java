package cn.pf.orch.meiye.interfaces.hazard;

import cn.genn.core.model.page.PageResultDTO;
import cn.pf.orch.meiye.domain.hazard.command.MyHazardIdentifySaveCommand;
import cn.pf.orch.meiye.domain.hazard.command.MyHazardIdentifyUpdateCommand;
import cn.pf.orch.meiye.domain.hazard.dto.HazardIdentifyDTO;
import cn.pf.orch.meiye.domain.hazard.query.HazardIdentifyQuery;
import cn.pf.orch.meiye.service.hazard.HazardIdentifyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api(tags = "事故隐患-排查计划")
@RestController
@RequestMapping("/hazard/identify")
public class HazardIdentifyController {

    @Resource
    private HazardIdentifyService hazardIdentifyService;

    @PostMapping("/page")
    @ApiOperation(value = "分页查询列表")
    public PageResultDTO<HazardIdentifyDTO> page(@ApiParam(value = "查询类") @RequestBody HazardIdentifyQuery query) {
        return hazardIdentifyService.page(query);
    }

    @PostMapping("/get")
    @ApiOperation(value = "根据id查询")
    public HazardIdentifyDTO get(@ApiParam(value = "查询类") @RequestParam Long id) {
        return hazardIdentifyService.get(id);
    }

    @PostMapping("/save")
    @ApiOperation(value = "添加")
    public Boolean save(@RequestBody @Validated MyHazardIdentifySaveCommand command) {
        return hazardIdentifyService.save(command);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public Boolean change(@RequestBody @Validated MyHazardIdentifyUpdateCommand command) {
        return hazardIdentifyService.change(command);
    }

    @PostMapping("/batch/delete")
    @ApiOperation(value = "批量删除")
    public Boolean batchRemove(@RequestBody List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return true;
        }
        return hazardIdentifyService.batchRemove(idList);
    }

    @PostMapping("/list")
    @ApiOperation(value = "获取权限范围内未到期辨识计划")
    public List<HazardIdentifyDTO> list() {
        return hazardIdentifyService.list();
    }
}
