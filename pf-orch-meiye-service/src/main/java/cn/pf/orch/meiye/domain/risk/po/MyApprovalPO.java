package cn.pf.orch.meiye.domain.risk.po;

import cn.genn.core.model.enums.DeletedEnum;
import cn.pf.orch.meiye.enums.ApprovalBizTypeEnum;
import cn.pf.orch.meiye.enums.feishu.FeishuInstanceStatusEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * MyApprovalPO对象
 *
 * <AUTHOR>
 * @desc 审批表
 */
@Data
@Accessors(chain = true)
@TableName(value = "my_approval", autoResultMap = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MyApprovalPO {

    /**
     * 飞书审批id
     */
    @TableId
    private String id;

    /**
     * 业务类型
     */
    @TableField("biz_type")
    private ApprovalBizTypeEnum bizType;

    /**
     * 业务id
     */
    @TableField("biz_id")
    private Long bizId;

    /**
     * 审批内容json
     */
    @TableField("content")
    private String content;

    /**
     * 发起人open_id
     */
    @TableField("promoter")
    private String promoter;

    /**
     * 审批状态
     */
    @TableField("status")
    private FeishuInstanceStatusEnum status;

    /**
     * 发生时间
     */
    @TableField("operate_time")
    private LocalDateTime operateTime;

    /**
     * 公司id
     */
    @TableField("company_id")
    private Long companyId;

    /**
     * 删除状态
     */
    @TableField("deleted")
    private DeletedEnum deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;

    /**
     * 创建人
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人id
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    /**
     * 更新人
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

}

