package cn.pf.orch.meiye.assembler;

import cn.genn.core.model.page.PageResultDTO;
import cn.pf.orch.meiye.domain.risk.dto.RiskCheckDTO;
import cn.pf.orch.meiye.domain.risk.dto.RiskCheckPageDTO;
import cn.pf.orch.meiye.domain.risk.po.RiskCheckPO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.Arrays;
import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RiskCheckAssembler {

    @Mapping(target = "files", expression = "java(stringToList(po.getFiles()))")
    RiskCheckDTO PO2DTO(RiskCheckPO po);

    @Mapping(target = "pageNo", source = "current")
    @Mapping(target = "pageSize", source = "size")
    @Mapping(target = "list", source = "records")
    @Mapping(target = "totalPages", source = "pages")
    PageResultDTO<RiskCheckPageDTO> toPage(IPage<RiskCheckPageDTO> poPage);

    default List<String> stringToList(String departmentIds) {
        if (departmentIds == null || departmentIds.isEmpty()) {
            return null;
        }
        return Arrays.asList(departmentIds.split(","));
    }
}
