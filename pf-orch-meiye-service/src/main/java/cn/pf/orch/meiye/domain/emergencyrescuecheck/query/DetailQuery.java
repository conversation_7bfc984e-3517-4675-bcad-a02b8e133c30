package cn.pf.orch.meiye.domain.emergencyrescuecheck.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class DetailQuery {

    @ApiModelProperty(value = "企业ID", example = "1001")
    @NotNull
    private Long companyId;

    @ApiModelProperty(value = "检查类型", example = "日常检查")
    @NotNull
    private String inspectionType;

    @ApiModelProperty(value = "项目名称", example = "安全生产检查")
    @NotNull
    private String project;

    @ApiModelProperty("问题Id")
//    @NotNull
    private Long problemId;
}
