package cn.pf.orch.meiye.domain.hazard.po;

import cn.genn.core.model.enums.DeletedEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * HazardMeasurePO对象
 *
 * <AUTHOR>
 * @desc 隐患措施表
 */
@Data
@Accessors(chain = true)
@TableName(value = "hazard_measure", autoResultMap = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HazardMeasurePO {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 隐患id
     */
    @TableField("hazard_id")
    private Long hazardId;

    /**
     * 隐患排查id
     */
    @TableField("hazard_identify_id")
    private Long hazardIdentifyId;

    /**
     * 措施内容
     */
    @TableField("content")
    private String content;

    /**
     * 公司id
     */
    @TableField("company_id")
    private Long companyId;

    /**
     * 删除状态
     */
    @TableField("deleted")
    private DeletedEnum deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;

    /**
     * 创建人
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人id
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    /**
     * 更新人
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

}

