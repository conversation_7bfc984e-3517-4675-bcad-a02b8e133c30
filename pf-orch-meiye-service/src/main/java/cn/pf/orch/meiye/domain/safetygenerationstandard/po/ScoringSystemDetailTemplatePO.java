package cn.pf.orch.meiye.domain.safetygenerationstandard.po;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.io.Serializable;

/**
 * 煤矿管理项目详情表(ScoringSystemDetailTemplate)表实体类
 *
 * <AUTHOR>
 * @since 2025-04-21 10:56:09
 */
@Data
@TableName("scoring_system_detail_template")
public class ScoringSystemDetailTemplatePO extends Model<ScoringSystemDetailTemplatePO> {
    //主键ID
    private Long id;
    //项目内容
    private String itemContent;
    //基本要求
    private String basicRequirements;
    //标准分值
    private BigDecimal standardScore;
    //评分方法
    private String scoringMethod;
    //管理项id
    private Long scoringSystemId;
    //创建人
    private String createUserId;
    //创建时间
    private LocalDateTime createTime;
    //更新人
    private String updateUserId;
    //更新时间
    private LocalDateTime updateTime;
}

