package cn.pf.orch.meiye.domain.safetygenerationstandard.po;

import cn.pf.orch.meiye.enums.MineTypeeEnum;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 煤矿管理模板表(ScoringSystemTemplate)表实体类
 *
 * <AUTHOR>
 * @since 2025-04-21 10:56:24
 */
@Data
@TableName("scoring_system_template")
public class ScoringSystemTemplatePO extends Model<ScoringSystemTemplatePO> {
    //主键ID
    private Long id;
    //适用煤矿类型（露天矿/井工矿）
    private MineTypeeEnum mineType;
    //管理部分（安全/生产/机电等）
    private String commonType;
    //标准分值(0-100)
    private BigDecimal standardScore;
    //权重(0-1)
    private BigDecimal weight;
    //创建人
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;
    //创建时间
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    //更新人
    private String updateUserId;
    //更新时间
    private LocalDateTime updateTime;

}

