package cn.pf.orch.meiye.domain.safetygenerationstandard.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@AllArgsConstructor
public class ScoredSystemDetailCommand {


    @ApiModelProperty(value = "安全生产标准化列表Id")
    @NotNull
    private Long scoredRecordId;

    @ApiModelProperty(value = "管理项评分Id")
    @NotNull
    private Long scoringSystemCorrelationId;

    @ApiModelProperty(value = "管理项详情列id")
    @NotNull
    private Long scoredDetailCorrelationId;

    @ApiModelProperty(value = "分数")
    @NotNull
    @DecimalMin("0.0")
    @DecimalMax("100.0")
    @Digits(integer = 6, fraction = 1, message = "请填写正确的分值")
    private BigDecimal scored;
}
