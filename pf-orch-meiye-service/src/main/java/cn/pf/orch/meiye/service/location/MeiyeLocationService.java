package cn.pf.orch.meiye.service.location;

import cn.genn.core.model.page.PageSortQuery;
import cn.pf.orch.meiye.domain.location.command.AddLocationCommand;
import cn.pf.orch.meiye.domain.location.command.DeleteLocationCommand;
import cn.pf.orch.meiye.domain.location.command.UpdateLocationCommand;
import cn.pf.orch.meiye.domain.location.dto.LocationTree;
import cn.pf.orch.meiye.domain.location.po.MeiyeLocation;
import cn.pf.orch.meiye.domain.location.query.QueryLocation;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 地点信息表(MeiyeLocation)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-11 08:48:50
 */
public interface MeiyeLocationService extends IService<MeiyeLocation> {


    void addLocation(AddLocationCommand command);

    void deleteLocation(DeleteLocationCommand command);

    List<LocationTree> queryLocation(QueryLocation queryLocation);

    void updateLocation(UpdateLocationCommand command);

    List<LocationTree> getLocationTree(List<Long> companyIds, PageSortQuery pageSortQuery);

    MeiyeLocation selectLocationById(Long id);

}

