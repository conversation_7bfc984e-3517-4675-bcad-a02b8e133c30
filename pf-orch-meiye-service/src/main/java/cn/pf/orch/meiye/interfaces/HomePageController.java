package cn.pf.orch.meiye.interfaces;

import cn.genn.core.model.page.PageResultDTO;
import cn.pf.orch.meiye.domain.system.dto.HomeMessagesDTO;
import cn.pf.orch.meiye.domain.system.dto.HomeTodoDTO;
import cn.pf.orch.meiye.domain.system.query.HomePageQuery;
import cn.pf.orch.meiye.service.HomeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/home")
@Api(tags = "首页")
public class HomePageController {

    @Resource
    private HomeService homeService;

    @PostMapping("/messages")
    @ApiOperation(value = "近期消息")
    public PageResultDTO<HomeMessagesDTO> messagesPage(@RequestBody HomePageQuery query) {
        return homeService.messagesPage(query);
    }

    @PostMapping("/todo")
    @ApiOperation(value = "我的待办")
    public PageResultDTO<HomeTodoDTO> todoPage(@RequestBody HomePageQuery query) {
        return homeService.todoPage(query);
    }
}
