package cn.pf.orch.meiye.domain.system.command;

import cn.pf.orch.meiye.enums.StatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class MyChangeStatusCommand {

    @ApiModelProperty(value = "主键")
    @NotEmpty(message = "idList不能为空")
    private List<Long> idList;

    @ApiModelProperty(value = "状态（1：启用；2：停用）")
    @NotNull(message = "状态类型不能为空")
    private StatusEnum status;
}
