package cn.pf.orch.meiye.interfaces;

import cn.genn.core.model.page.PageSortQuery;
import cn.pf.orch.meiye.config.CurrentUserHolder;
import cn.pf.orch.meiye.domain.location.command.AddLocationCommand;
import cn.pf.orch.meiye.domain.location.command.DeleteLocationCommand;
import cn.pf.orch.meiye.domain.location.command.UpdateLocationCommand;
import cn.pf.orch.meiye.domain.location.dto.LocationTree;
import cn.pf.orch.meiye.domain.location.query.QueryLocation;
import cn.pf.orch.meiye.service.location.MeiyeLocationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Api(tags = "地址模块")
@RestController
@RequestMapping("/location")
public class LocationController {

    @Resource
    private MeiyeLocationService service;

    @ApiOperation(value = "查询当前用户地址列表")
    @PostMapping("queryAllLocationTree")
    public List<LocationTree> queryAllLocationTree(@RequestBody PageSortQuery pageSortQuery) {
        // 获取当前用户可查询的所有煤矿
        List<Long> rangeCompanyIds = CurrentUserHolder.getRangeCompanyIds();
        log.info("login user contains range companyIds: {}", rangeCompanyIds);
        return service.getLocationTree(rangeCompanyIds,pageSortQuery);
    }

    @ApiOperation(value = "添加地址")
    @PostMapping("addLocation")
    public void addLocation(@RequestBody @Validated AddLocationCommand command) {
        log.info("add location: {}", command);
        service.addLocation(command);
    }

    @ApiOperation(value = "删除地址")
    @PostMapping("deleteLocation")
    public void deleteLocation(@RequestBody @Validated DeleteLocationCommand command) {
        log.info("delete location: {}", command);
        service.deleteLocation(command);
    }

    @ApiOperation(value = "条件查询")
    @PostMapping("queryLocation")
    public List<LocationTree> queryLocation(@RequestBody QueryLocation queryLocation) {
        long startTime = System.currentTimeMillis();
        log.info("query location start: {}", startTime);
        List<LocationTree> locationTrees = service.queryLocation(queryLocation);
        log.info("query location stop: {}", (System.currentTimeMillis() - startTime) / 1000);
        return locationTrees;
    }

    @ApiOperation(value = "更新地址信息")
    @PostMapping("updateLocation")
    public void updateLocation(@RequestBody @Validated @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)  UpdateLocationCommand command) {
        service.updateLocation(command);
    }

}
