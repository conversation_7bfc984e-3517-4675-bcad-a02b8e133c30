package cn.pf.orch.meiye.domain.system.po;

import cn.genn.core.model.enums.DeletedEnum;
import cn.pf.orch.meiye.enums.AuthTypeEnum;
import cn.pf.orch.meiye.enums.RoleTypeEnum;
import cn.pf.orch.meiye.enums.StatusEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * MyRolePO对象
 *
 * <AUTHOR>
 * @desc 角色表
 */
@Data
@Accessors(chain = true)
@TableName(value = "my_role", autoResultMap = true)
public class MyRolePO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 权限类型（集团，区域公司，煤矿）
     */
    @TableField("auth_type")
    private AuthTypeEnum authType;

    /**
     * 公司id
     */
    @TableField("company_id")
    private Long companyId;

    /**
     * 类型(system（系统角色），customize（自定义）)
     */
    @TableField("type")
    private RoleTypeEnum type;

    /**
     * 状态（1：启用；2：停用）
     */
    @TableField("status")
    private StatusEnum status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 逻辑删除（0：未删除；1：删除）
     */
    @TableField("deleted")
    @TableLogic
    private DeletedEnum deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;

    /**
     * 创建用户名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新用户ID
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    /**
     * 更新用户名
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

}

