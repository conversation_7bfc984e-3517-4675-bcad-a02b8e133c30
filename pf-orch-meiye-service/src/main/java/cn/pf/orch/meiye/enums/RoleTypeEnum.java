package cn.pf.orch.meiye.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum RoleTypeEnum {

    DEFAULT("default","默认角色"),
    SYSTEM("system", "系统角色"),
    CUSTOMIZE("customize", "自定义角色"),
    ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;


    private static final Map<String, RoleTypeEnum> VALUES = new HashMap<>();
    static {
        for (final RoleTypeEnum item : RoleTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static RoleTypeEnum of(String code) {
        return VALUES.get(code);
    }
}
