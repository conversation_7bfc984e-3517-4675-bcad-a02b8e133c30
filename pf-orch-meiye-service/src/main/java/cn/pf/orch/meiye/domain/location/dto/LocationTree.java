package cn.pf.orch.meiye.domain.location.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class LocationTree {

    private Long id;
    @ApiModelProperty(value = "地点名称")
    private String name;
    @ApiModelProperty(value = "地点层级")
    private Integer level;
    @ApiModelProperty(value = "煤矿")
    private String companyName;
    @ApiModelProperty(value = "煤矿id")
    private Long companyId;
    @ApiModelProperty("父级节点")
    private Long parentId;
    private String createBy;
    private LocalDateTime createTime;
    private String updateBy;
    private LocalDateTime updateTime;
    @ApiModelProperty(value = "子节点")
    private List<LocationTree> children;
}
