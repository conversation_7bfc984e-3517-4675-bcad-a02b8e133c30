package cn.pf.orch.meiye.service.location.impl;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.core.model.page.PageSortQuery;
import cn.hutool.core.util.ObjectUtil;
import cn.pf.orch.meiye.assembler.LocationAssembler;
import cn.pf.orch.meiye.config.CurrentUserHolder;
import cn.pf.orch.meiye.domain.location.command.AddLocationCommand;
import cn.pf.orch.meiye.domain.location.command.DeleteLocationCommand;
import cn.pf.orch.meiye.domain.location.command.UpdateLocationCommand;
import cn.pf.orch.meiye.domain.location.dto.LocationTree;
import cn.pf.orch.meiye.domain.location.po.MeiyeLocation;
import cn.pf.orch.meiye.domain.location.query.QueryLocation;
import cn.pf.orch.meiye.enums.LocationLevelTypeEnum;
import cn.pf.orch.meiye.mapper.MeiyeLocationMapper;
import cn.pf.orch.meiye.service.location.MeiyeLocationService;
import cn.pf.orch.meiye.utils.PageSortImplQuery;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 地点信息表(MeiyeLocation)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-11 08:48:50
 */
@Slf4j
@Service
public class MeiyeLocationServiceImpl extends ServiceImpl<MeiyeLocationMapper, MeiyeLocation> implements MeiyeLocationService {

    @Resource
    private MeiyeLocationMapper meiyeLocationMapper;
    @Resource
    private LocationAssembler locationAssembler;


    @Transactional
    @Override
    public void addLocation(AddLocationCommand command) {
        // 限制层级为5级
        if(ObjectUtil.isNotNull(command) && LocationLevelTypeEnum.FIVE.getCode().equals(command.getParentLevel())) {
            throw new BusinessException("当前地址节点不可添加子节点");
        }
        // 校验是否顶层地点
        if (ObjectUtil.isNull(command.getParentId())) {
            // 校验是否重复
            LambdaQueryWrapper<MeiyeLocation> wrapper = Wrappers.lambdaQuery(MeiyeLocation.class)
                    .eq(MeiyeLocation::getName, command.getName())
                    .isNull(MeiyeLocation::getParentId);
            if(ObjectUtil.isNotNull(meiyeLocationMapper.selectOne(wrapper))) {
                throw new BusinessException("地点已存在，请勿重复添加！");
            }
            MeiyeLocation meiyeLocation = locationAssembler.dtoToPO(command);
            meiyeLocation.setLevel(LocationLevelTypeEnum.ONE.getCode());
            meiyeLocationMapper.insert(meiyeLocation);
            meiyeLocation.setFullPathId(meiyeLocation.getId().toString());
            meiyeLocation.setFullPathName(meiyeLocation.getName());
            meiyeLocationMapper.update(meiyeLocation, new UpdateWrapper<MeiyeLocation>().eq("id", meiyeLocation.getId()));
            return;
        }
        // 校验是否重复
        LambdaQueryWrapper<MeiyeLocation> wrapper = Wrappers.lambdaQuery(MeiyeLocation.class)
                .eq(MeiyeLocation::getName, command.getName())
                .eq(MeiyeLocation::getParentId, command.getParentId());
        if(ObjectUtil.isNotNull(meiyeLocationMapper.selectOne(wrapper))) {
            throw new BusinessException("地点已存在，请勿重复添加！");
        }
        // 非顶级地点，先验证父地点
        MeiyeLocation parentLocation = Optional.ofNullable(meiyeLocationMapper.selectById(command.getParentId()))
                .orElseThrow(() -> new BusinessException("父节点不存在"));
        MeiyeLocation meiyeLocation = locationAssembler.dtoToPO(command);
        meiyeLocation.setLevel(parentLocation.getLevel() + 1);
        meiyeLocationMapper.insert(meiyeLocation);
        meiyeLocation.setFullPathId(parentLocation.getFullPathId() + "/" + meiyeLocation.getId());
        meiyeLocation.setFullPathName(parentLocation.getFullPathName()+ "/" + meiyeLocation.getName());
        meiyeLocationMapper.update(meiyeLocation, new UpdateWrapper<MeiyeLocation>().eq("id", meiyeLocation.getId()));
    }


    @Override
    public void deleteLocation(DeleteLocationCommand command) {
        // TODO  仅当没有下级地址时才能删除 有风险维护了地点是否可以删除
        if (Objects.equals(command.getLevel() , LocationLevelTypeEnum.FIVE.getCode())) {
            meiyeLocationMapper.deleteById(command.getId());
        }
        MeiyeLocation meiyeLocation = meiyeLocationMapper.selectById(command.getId());
        List<MeiyeLocation> result = Lists.newArrayList();
        findChilderNode(meiyeLocation,result);
        if(ObjectUtil.isEmpty(result)) {
            meiyeLocationMapper.deleteById(command.getId());
        } else {
            throw new BusinessException("当前节点有下级节点，不可删除");
        }
    }

    @Override
    public List<LocationTree> queryLocation(QueryLocation queryLocation) {
        // 地址名称信息为空时
        List<Long> companyIds = ObjectUtil.isEmpty(queryLocation.getCompanyId())
                ? CurrentUserHolder.getRangeCompanyIds()
                : Lists.newArrayList(queryLocation.getCompanyId());
        log.info("queryLocation companyIds: {}", companyIds);
        if(ObjectUtil.isEmpty(queryLocation.getName())) {
            List<LocationTree> locationTree = getLocationTree(companyIds, new PageSortImplQuery(queryLocation.getPageNo(), queryLocation.getPageSize(), queryLocation.getSort()));
            return locationTree.stream()
                    .skip((long) (queryLocation.getPageNo() - 1) * queryLocation.getPageSize())
                    .limit(queryLocation.getPageSize())
                    .collect(Collectors.toList());
        }
        // 仅查询地点名称
        LambdaQueryWrapper<MeiyeLocation> wrapper = Wrappers.lambdaQuery(MeiyeLocation.class)
                .in(ObjectUtil.isNotEmpty(queryLocation.getCompanyId()), MeiyeLocation::getCompanyId, queryLocation.getCompanyId())
                .like(MeiyeLocation::getName, queryLocation.getName())
                .eq(MeiyeLocation::getDeleted, DeletedEnum.NOT_DELETED)
                .orderByDesc(MeiyeLocation::getCreateTime);
        List<MeiyeLocation> records = meiyeLocationMapper.selectList(wrapper);
        List<MeiyeLocation> collect = records.stream().skip((long) (queryLocation.getPageNo() - 1) * queryLocation.getPageSize()).limit(queryLocation.getPageSize()).collect(Collectors.toList());
        return locationAssembler.POToTreeList(collect);
    }

    @Transactional
    @Override
    public void updateLocation(UpdateLocationCommand command) {
        MeiyeLocation node = meiyeLocationMapper.selectById(command.getId());
        String oldLocationName = node.getName();
        node.setName(command.getNewLocationName());
        node.setFullPathName(node.getFullPathName().replace(oldLocationName, command.getNewLocationName()));
        log.info("Location:{}", node);
        meiyeLocationMapper.updateById(node);
        // 需要递归查询当前节点的所有子集，并修改子集中的内容
        ArrayList<MeiyeLocation> result = Lists.newArrayList();
        findChilderNode(node, result);
        // 更新集合中的名称
        if(ObjectUtil.isEmpty(result)) {
            return;
        }
        result.forEach(location -> {
            String fullPathName = location.getFullPathName();
            location.setFullPathName(fullPathName.replace(oldLocationName, command.getNewLocationName()));
        });
        this.updateBatchById(result);
    }

    /**
     * @param ids 煤矿id
     * @return
     */
    @Override
    public List<LocationTree> getLocationTree(List<Long> ids, PageSortQuery pageSortQuery) {
        // 权限相关，仅可查看用户所属煤矿的数据
        LambdaQueryWrapper<MeiyeLocation> wrapper = Wrappers.lambdaQuery(MeiyeLocation.class)
                .in(ObjectUtil.isNotEmpty(ids), MeiyeLocation::getCompanyId, ids)
                .eq(MeiyeLocation::getDeleted, DeletedEnum.NOT_DELETED)
                .orderByDesc(MeiyeLocation::getCreateTime);
//        Page<MeiyeLocation> allLocationsPage = meiyeLocationMapper.selectPage(new Page<>(pageSortQuery.getPageNo(), pageSortQuery.getPageSize()), wrapper);
        List<MeiyeLocation> allLocations = meiyeLocationMapper.selectList(wrapper);
        Map<Long, LocationTree> treeMap = new HashMap<>();
        // 先创建所有节点的映射
        allLocations.forEach(location -> {
            LocationTree node = new LocationTree();
            node.setId(location.getId());
            node.setParentId(location.getParentId());
            node.setCompanyId(location.getCompanyId());
            node.setCompanyName(location.getCompanyName());
            node.setName(location.getName());
            node.setLevel(location.getLevel());
            node.setChildren(new ArrayList<>());
            node.setCreateBy(location.getCreateUserName());
            node.setUpdateBy(location.getUpdateUserName());
            treeMap.put(location.getId(), node);
        });

        // 构建树形结构
        List<LocationTree> roots = new ArrayList<>();
        allLocations.forEach(location -> {
            LocationTree node = treeMap.get(location.getId());
            if (location.getParentId() == null) {
                roots.add(node);
            } else {
                LocationTree parent = treeMap.get(location.getParentId());
                if (parent != null) {
                    parent.getChildren().add(node);
                }
            }
        });
        return roots;
    }

    @Override
    public MeiyeLocation selectLocationById(Long id) {
        return Optional.ofNullable(meiyeLocationMapper.selectById(id)).orElse(new MeiyeLocation());
    }


    /**
     * 递归查询当前节点的所有子节点
     * @param parentNode = 当前节点
     * @return
     */
    private void findChilderNode(MeiyeLocation parentNode, List<MeiyeLocation> result) {
        if (Objects.equals(parentNode.getLevel(), LocationLevelTypeEnum.FIVE.getCode())) {
            return;
        }
        LambdaQueryWrapper<MeiyeLocation> wrapper =
                Wrappers.lambdaQuery(MeiyeLocation.class).eq(MeiyeLocation::getParentId, parentNode.getId());
        List<MeiyeLocation> subList = meiyeLocationMapper.selectList(wrapper);
        for (MeiyeLocation child : subList) {
            result.add(child);
            findChilderNode(child, result);
        }
    }
}

