package cn.pf.orch.meiye.assembler;

import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.core.model.page.PageResultDTO;
import cn.hutool.core.collection.CollUtil;
import cn.pf.orch.meiye.domain.system.dto.MyUserDTO;
import cn.pf.orch.meiye.domain.system.po.MyUserPO;
import cn.pf.orch.meiye.domain.system.query.MyUserPageQuery;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lark.oapi.service.contact.v3.model.User;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.Arrays;
import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UserAssembler  extends QueryAssembler<MyUserPageQuery, MyUserPO, MyUserDTO> {

    @Mapping(source = "openId",target = "id")
    @Mapping(source = "avatar.avatarOrigin",target = "avatarOrigin")
    @Mapping(target = "deleted",expression = "java(cn.genn.core.model.enums.DeletedEnum.NOT_DELETED)")
    @Mapping(target = "gender", expression = "java(cn.pf.orch.meiye.enums.GenderEnum.of(user.getGender()))")
    @Mapping(target = "departmentId", expression = "java(getDepartmentId(user.getDepartmentIds()))")
    MyUserPO DTO2PO(User user);

    List<MyUserPO> DTO2PO(List<User> userList);


    @Mapping(target = "pageNo", source = "current")
    @Mapping(target = "pageSize", source = "size")
    @Mapping(target = "list", source = "records")
    @Mapping(target = "totalPages", source = "pages")
    PageResultDTO<MyUserDTO> toPage(IPage<MyUserDTO> poPage);

    default String getDepartmentId(String[] departmentIds) {
        if(CollUtil.isNotEmpty(Arrays.asList(departmentIds))){
            return departmentIds[0];
        }
        return null;
    }

}
