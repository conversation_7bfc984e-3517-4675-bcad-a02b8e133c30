package cn.pf.orch.meiye.mapper;

import cn.pf.orch.meiye.domain.hazard.dto.HazardCheckDTO;
import cn.pf.orch.meiye.domain.hazard.dto.HazardCheckPageDTO;
import cn.pf.orch.meiye.domain.hazard.po.HazardCheckPO;
import cn.pf.orch.meiye.domain.hazard.query.HazardCheckPageQuery;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface HazardCheckMapper extends BaseMapper<HazardCheckPO> {

    IPage<HazardCheckPageDTO> selectByPage(IPage<HazardCheckPO> page, @Param("query") HazardCheckPageQuery query);

    HazardCheckDTO selectDetailById(Long id);
}
