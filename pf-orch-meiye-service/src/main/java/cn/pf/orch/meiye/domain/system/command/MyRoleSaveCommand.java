package cn.pf.orch.meiye.domain.system.command;

import cn.pf.orch.meiye.enums.AuthTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
public class MyRoleSaveCommand {

    @ApiModelProperty(value = "名称")
    @NotBlank(message = "名称不能为空")
    @Size(max = 32, message = "名称最大长度不能超过32")
    private String name;

    @ApiModelProperty(value = "部门id")
    @NotNull(message = "公司不能为空")
    private Long companyId;

    @ApiModelProperty(value = "权限类型")
    @NotNull(message = "权限类型不能为空")
    private AuthTypeEnum authType;

}
