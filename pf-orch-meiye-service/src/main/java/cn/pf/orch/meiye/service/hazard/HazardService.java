package cn.pf.orch.meiye.service.hazard;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.pf.orch.feishu.FeishuAppClient;
import cn.pf.orch.feishu.model.ApprovalInstanceModel;
import cn.pf.orch.feishu.model.InstanceFormModel;
import cn.pf.orch.feishu.model.enums.InstanceFormTypeEnum;
import cn.pf.orch.meiye.assembler.ApprovalLogAssembler;
import cn.pf.orch.meiye.assembler.HazardInfoAssembler;
import cn.pf.orch.meiye.assembler.HazardMeasureAssembler;
import cn.pf.orch.meiye.config.CurrentUserHolder;
import cn.pf.orch.meiye.domain.feishu.SendHazardDTO;
import cn.pf.orch.meiye.domain.hazard.command.HazardMeasureSaveCommand;
import cn.pf.orch.meiye.domain.hazard.command.HazardMeasureUpdateCommand;
import cn.pf.orch.meiye.domain.hazard.command.MyHazardInfoSaveCommand;
import cn.pf.orch.meiye.domain.hazard.command.MyHazardInfoUpdateCommand;
import cn.pf.orch.meiye.domain.hazard.dto.HazardCountDTO;
import cn.pf.orch.meiye.domain.hazard.dto.HazardInfoDTO;
import cn.pf.orch.meiye.domain.hazard.dto.HazardInfoPageDTO;
import cn.pf.orch.meiye.domain.hazard.dto.HazardMeasureDTO;
import cn.pf.orch.meiye.domain.hazard.po.HazardInfoPO;
import cn.pf.orch.meiye.domain.hazard.po.HazardMeasurePO;
import cn.pf.orch.meiye.domain.hazard.query.HazardInfoQuery;
import cn.pf.orch.meiye.domain.risk.command.SendNotifyCommand;
import cn.pf.orch.meiye.domain.risk.po.MyAddressRelPO;
import cn.pf.orch.meiye.domain.risk.po.MyApprovalLogPO;
import cn.pf.orch.meiye.domain.risk.po.MyApprovalPO;
import cn.pf.orch.meiye.domain.system.po.MyCompanyPO;
import cn.pf.orch.meiye.domain.system.po.MyUserPO;
import cn.pf.orch.meiye.enums.*;
import cn.pf.orch.meiye.enums.feishu.FeishuInstanceStatusEnum;
import cn.pf.orch.meiye.exception.MessageCode;
import cn.pf.orch.meiye.mapper.*;
import cn.pf.orch.meiye.processor.HazardProcessor;
import cn.pf.orch.meiye.properties.ApprovalProperties;
import cn.pf.orch.meiye.properties.MeiyeSeverProperties;
import cn.pf.orch.meiye.service.CardSendActionService;
import cn.pf.orch.meiye.utils.FsNotifyUrlUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lark.oapi.service.approval.v4.model.ApprovalNodeInfo;
import com.lark.oapi.service.approval.v4.model.GetApprovalRespBody;
import com.lark.oapi.service.approval.v4.model.NodeApprover;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class HazardService {

    @Resource
    private MeiyeSeverProperties meiyeSeverProperties;
    @Resource
    private FeishuAppClient feishuAppClient;
    @Resource
    private HazardInfoMapper hazardInfoMapper;
    @Resource
    private MyApprovalMapper myApprovalMapper;
    @Resource
    private CardSendActionService cardSendActionService;
    @Resource
    private HazardInfoAssembler assembler;
    @Resource
    private HazardProcessor hazardProcessor;
    @Resource
    private MyAddressRelMapper myAddressRelMapper;
    @Resource
    private HazardMeasureMapper hazardMeasureMapper;
    @Resource
    private HazardMeasureAssembler hazardMeasureAssembler;
    @Resource
    private MyApprovalLogMapper myApprovalLogMapper;
    @Resource
    private ApprovalLogAssembler approvalLogAssembler;
    @Resource
    private MyUserMapper myUserMapper;
    @Resource
    private MyCompanyMapper myCompanyMapper;

    private final static String NOTIFY_TEMPLATE = "所属煤矿: {}\n隐患等级: {}\n隐患编号: {}\n隐患类别: {}\n是否可控: {}\n排查计划名称: {}\n排查类型: {}\n排查日期: {}\n隐患地点: {}\n详细地点: {}\n排查人: {}\n整改单位: {}\n整改责任人: {}\n整改期限: {}\n整改资金: {}元\n隐患描述: {}\n隐患措施: {}";

    // 定义日期格式
    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");

    public PageResultDTO<HazardInfoPageDTO> page(HazardInfoQuery query) {
        // 权限
        query.setCompanyIds(CurrentUserHolder.getRangeCompanyIds());
        PageResultDTO<HazardInfoPageDTO> pageResult = assembler.toPage(hazardInfoMapper.selectByPage(new Page<>(query.getPageNo(), query.getPageSize()), query));
        return pageResult;
    }

    public List<HazardCountDTO> count(HazardInfoQuery query) {
        // 权限
        query.setCompanyIds(CurrentUserHolder.getRangeCompanyIds());
        List<HazardCountDTO> hazardCountDTOS = hazardInfoMapper.selectLevelCount(query);
        Map<RiskLevelEnum, HazardCountDTO> map = hazardCountDTOS.stream().collect(Collectors.toMap(HazardCountDTO::getLevel, Function.identity()));
        List<HazardCountDTO> hazardCountDTOList = new ArrayList<>();
        for (RiskLevelEnum value : RiskLevelEnum.values()) {
            HazardCountDTO hazardCountDTO = map.get(value);
            if (hazardCountDTO == null) {
                hazardCountDTO = HazardCountDTO.builder().level(value).levelName(value.getDescription()).count(0).build();
            }
            hazardCountDTO.setLevelName(value.getDescription());
            hazardCountDTOList.add(hazardCountDTO);
        }
        return hazardCountDTOList;
    }

    public HazardInfoDTO get(Long id) {
        HazardInfoDTO hazardInfoDTO = hazardInfoMapper.selectDetailById(id);
        // 措施
        List<HazardMeasurePO> measureDTOS = hazardMeasureMapper.selectByHazardId(id);
        if (CollUtil.isNotEmpty(measureDTOS)) {
            hazardInfoDTO.setHazardMeasureList(hazardMeasureAssembler.PO2DTO(measureDTOS));
        }
        // 审核信息
        if (StrUtil.isNotBlank(hazardInfoDTO.getApprovalId())) {
            List<MyApprovalLogPO> approvalLogPOS = myApprovalLogMapper.selectByApprovalId(hazardInfoDTO.getApprovalId());
            hazardInfoDTO.setHazardApprovalList(approvalLogAssembler.PO2DTO(approvalLogPOS));
        }
        hazardInfoDTO.setCheckLevel(CheckLevelEnum.getByAuthType(CurrentUserHolder.getAuthType()));
        return hazardInfoDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    public Long save(MyHazardInfoSaveCommand command) {
        HazardInfoPO po = assembler.saveCommand2PO(command);
        //补充责任人
        command.setOwnerName(myUserMapper.selectById(command.getOwner()).getName());
        // 隐患编号
        po.setCode(this.generateCode());
        po.setStatus(RiskStatusEnum.WAIT_SUBMIT);
        hazardInfoMapper.insert(po);
        // 地址关联
        myAddressRelMapper.insert(MyAddressRelPO.builder()
                .addressId(command.getAddressId())
                .addressDetail(command.getAddressDetail())
                .bizId(po.getId())
                .bizType(ApprovalBizTypeEnum.HAZARD)
                .companyId(po.getCompanyId())
                .build());
        return po.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public Long change(MyHazardInfoUpdateCommand command) {
        hazardProcessor.changeCheck(command);
        if(StrUtil.isNotBlank(command.getOwner())){
            command.setOwnerName(myUserMapper.selectById(command.getOwner()).getName());
        }
        HazardInfoPO po = assembler.updateCommand2PO(command);
        hazardInfoMapper.updateById(po);
        // 地址关联
        if (ObjUtil.isNotNull(command.getAddressId()) && StrUtil.isNotEmpty(command.getAddressDetail())) {
            myAddressRelMapper.deleteByBiz(ApprovalBizTypeEnum.HAZARD, Arrays.asList(po.getId()));
            myAddressRelMapper.insert(MyAddressRelPO.builder()
                    .addressId(command.getAddressId())
                    .addressDetail(command.getAddressDetail())
                    .bizId(po.getId())
                    .bizType(ApprovalBizTypeEnum.HAZARD)
                    .companyId(po.getCompanyId())
                    .build());
        }
        return command.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean batchRemove(List<Long> idList) {
        hazardProcessor.deleteCheck(idList);
        hazardInfoMapper.deleteBatchIds(idList);
        myAddressRelMapper.deleteByBiz(ApprovalBizTypeEnum.HAZARD, idList);
        return true;
    }

    public Boolean saveHazardMeasure(HazardMeasureSaveCommand command) {
        HazardMeasurePO po = hazardMeasureAssembler.saveCommand2PO(command);
        HazardInfoPO hazardInfoPO = hazardInfoMapper.selectById(command.getHazardId());
        po.setCompanyId(hazardInfoPO.getCompanyId());
        po.setHazardIdentifyId(hazardInfoPO.getHazardIdentifyId());
        hazardMeasureMapper.insert(po);
        return true;
    }

    public Boolean updateHazardMeasure(HazardMeasureUpdateCommand command) {
        HazardMeasurePO po = hazardMeasureAssembler.updateCommand2PO(command);
        hazardMeasureMapper.updateById(po);
        return true;
    }

    public Boolean deleteHazardMeasure(List<Long> idList) {
        hazardMeasureMapper.deleteBatchIds(idList);
        return true;
    }

    public Boolean sendNotify(SendNotifyCommand command) {
        HazardInfoDTO hazardInfoDTO = this.get(command.getId());
        if (ObjUtil.isEmpty(hazardInfoDTO)) {
            throw new BusinessException(MessageCode.HAZARD_NOT_EXIST);
        }
        if (!hazardInfoDTO.getStatus().equals(RiskStatusEnum.NO_CLOSE)) {
            throw new BusinessException(MessageCode.STATUS_NOT_MATCH_STOP_NOTIFY);
        }
        String pcUrl = meiyeSeverProperties.getCardSend().getJumpUrl().getHazardPcUrl() + "?id=" + command.getId();
        String appUrl = meiyeSeverProperties.getCardSend().getJumpUrl().getHazardAppUrl() + "?id=" + command.getId();
        SendHazardDTO dto = SendHazardDTO.builder()
                .companyName(hazardInfoDTO.getCompanyName())
                .code(hazardInfoDTO.getCode())
                .address(hazardInfoDTO.getFullAddressName()+"-"+ hazardInfoDTO.getAddressDetail())
                .type(hazardInfoDTO.getType().getDescription())
                .remark(hazardInfoDTO.getRemark())
                .pcUrl(FsNotifyUrlUtils.getPCUrl(pcUrl))
                .appUrl(appUrl)
                .departmentName(hazardInfoDTO.getDepartmentName())
                .rectifyMoney(String.valueOf(hazardInfoDTO.getRectifyMoney()))
                .rectifyOwnerName(hazardInfoDTO.getRectifyOwnerName())
                .rectifyTime(hazardInfoDTO.getRectifyTime().format(formatter))
                .openIds(command.getOpenIds())
                .build();
        cardSendActionService.sendHazardCode(dto);
        return true;
    }


    /**
     * 发送审批
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean sendApproval(Long id) {
        HazardInfoDTO dto = this.get(id);
        if (ObjUtil.isEmpty(dto)) {
            throw new BusinessException(MessageCode.HAZARD_NOT_EXIST);
        }
        if (!dto.getStatus().equals(RiskStatusEnum.WAIT_SUBMIT) && !dto.getStatus().equals(RiskStatusEnum.REJECT)) {
            throw new BusinessException(MessageCode.STATUS_NOT_MATCH_STOP_APPROVAL);
        }
        // 校验审批人员
        List<MyUserPO> myUserPOS = myUserMapper.selectApprovalSignAndCompanyId(ApprovalSignEnum.COLLIERY, dto.getCompanyId());
        if (CollUtil.isEmpty(myUserPOS)) {
            throw new BusinessException("缺乏煤矿审批人员,请联系系统管理员配置");
        }
        MyCompanyPO pCompany = myCompanyMapper.selectById(dto.getCompanyId());
        List<MyUserPO> myUserRanges = myUserMapper.selectApprovalSignAndCompanyId(ApprovalSignEnum.RANGE, pCompany.getPid());
        if (CollUtil.isEmpty(myUserRanges)) {
            throw new BusinessException("缺乏区域公司审批人员,请联系系统管理员配置");
        }
        String identifyType = dto.getIdentifyType().getDescription();
        identifyType = ObjUtil.isNotEmpty(dto.getIdentifyDetail()) ? identifyType + "-" + dto.getIdentifyDetail().getDescription() : identifyType;
        String identifyTime = dto.getIdentifyTime().format(formatter);
        String rectifyTime = dto.getRectifyTime().format(formatter);
        String measures = null;
        if (CollUtil.isNotEmpty(dto.getHazardMeasureList())) {
            measures = "\n";
            for (int i = 0; i < dto.getHazardMeasureList().size(); i++) {
                HazardMeasureDTO hazardMeasureDTO = dto.getHazardMeasureList().get(i);
                measures = measures + " " + (i+1) + "." + hazardMeasureDTO.getContent() + "\n";
            }
        }
        // 审批详情内容
        StringBuilder content = new StringBuilder(StrUtil.format(NOTIFY_TEMPLATE, dto.getCompanyName(), dto.getLevel().getDescription(), dto.getCode(),
                        dto.getType().getDescription(), dto.getControlStatus().getDescription(), dto.getIdentifyName(), identifyType, identifyTime,
                        dto.getFullAddressName(), dto.getAddressDetail(), dto.getOwnerName(),dto.getDepartmentName(),dto.getRectifyOwnerName(),rectifyTime,dto.getRectifyMoney(), dto.getRemark(), measures)
                .replaceAll("null", "-"));
        Optional<ApprovalProperties> approvalOptional = meiyeSeverProperties.getApproval().stream().filter(item -> item.getCode().equals(ApprovalBizTypeEnum.HAZARD.getCode())).findFirst();
        String hazardCode = approvalOptional.map(ApprovalProperties::getApprovalCode).orElse(null);
        GetApprovalRespBody approval = feishuAppClient.getApprovalService().getApproval(hazardCode);
        // 审批节点id
        String collieryNodeId = this.getNodeId(approval, "煤矿审批");
        String regionNodeId = this.getNodeId(approval, "区域公司审批");

        InstanceFormModel instanceFormModel = InstanceFormModel.builder()
                .type(InstanceFormTypeEnum.TEXTAREA)
                .required(true)
                .value(content).build();
        List<NodeApprover> nodeApproverOpenIdList = new ArrayList<>();
        nodeApproverOpenIdList.add(NodeApprover.newBuilder()
                .key(collieryNodeId)
                .value(myUserPOS.stream().map(MyUserPO::getId).distinct().toArray(String[]::new)).build());
        nodeApproverOpenIdList.add(NodeApprover.newBuilder()
                .key(regionNodeId)
                .value(myUserRanges.stream().map(MyUserPO::getId).distinct().toArray(String[]::new)).build());
        ApprovalInstanceModel model = ApprovalInstanceModel.builder()
                .openId(CurrentUserHolder.getOpenId())
                .approvalCode(hazardCode)
                .formList(Collections.singletonList(instanceFormModel))
                .nodeApproverOpenIdList(nodeApproverOpenIdList).build();
        String instanceCode = feishuAppClient.getApprovalService().createApprovalInstance(model);
        hazardInfoMapper.updateById(HazardInfoPO.builder().id(id).approvalId(instanceCode).status(RiskStatusEnum.WAIT_REVIEW).build());
        myApprovalMapper.insert(MyApprovalPO.builder()
                .bizId(id)
                .bizType(ApprovalBizTypeEnum.HAZARD)
                .companyId(dto.getCompanyId())
                .id(instanceCode)
                .content(content.toString())
                .promoter(CurrentUserHolder.getOpenId())
                .operateTime(LocalDateTime.now())
                .status(FeishuInstanceStatusEnum.PENDING)
                .build());
        myApprovalLogMapper.insert(MyApprovalLogPO.builder()
                .approvalId(instanceCode)
                .status("提交审核")
                .openId(CurrentUserHolder.getOpenId())
                .userName(CurrentUserHolder.getName())
                .createTime(LocalDateTime.now())
                .build());
        return true;
    }

    public Boolean close(Long id) {
        hazardProcessor.closeCheck(id);
        hazardInfoMapper.updateById(HazardInfoPO.builder().id(id).status(RiskStatusEnum.CLOSE).build());
        return true;
    }

    private String getNodeId(GetApprovalRespBody approval, String nodeName) {
        if (ObjUtil.isNotNull(approval)) {
            ApprovalNodeInfo[] nodeList = approval.getNodeList();
            for (ApprovalNodeInfo approvalNodeInfo : nodeList) {
                if (nodeName.equals(approvalNodeInfo.getName())) {
                    return approvalNodeInfo.getNodeId();
                }
            }
        }
        log.error("未获取到审批节点,approval:{},nodeName:{}", JsonUtils.toJson(approval), nodeName);
        throw new BusinessException("未获取到审批节点");
    }

    /**
     * 生成隐患编号
     */
    private String generateCode() {
        // 取最大的运营商编号
        HazardInfoPO hazardInfoPO = hazardInfoMapper.getMaxCode();
        if (ObjUtil.isNull(hazardInfoPO)) {
            return "Y" + "000001";
        }
        String maxCode = hazardInfoPO.getCode();
        if (!maxCode.matches(CodeTypeEnum.MATCH)) {
            log.error("隐患编号不正确:{}", maxCode);
            throw new BusinessException("隐患编号不正确");
        }
        int incrementedNum = Integer.parseInt(maxCode.substring(1, 7)) + 1;
        String formattedNumericPart = String.format("%06d", incrementedNum);

        // 返回组合后的下一个编号
        return CodeTypeEnum.HAZARD.getCode() + formattedNumericPart;

    }
}
