package cn.pf.orch.meiye.job;

import cn.hutool.core.collection.CollUtil;
import cn.pf.orch.meiye.domain.system.po.MyCompanyPO;
import cn.pf.orch.meiye.mapper.MyCompanyMapper;
import cn.pf.orch.meiye.service.system.SyncFSContactService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 增量同步刷新飞书用户
 */
@Slf4j
@Component
public class RefreshFsUserTask {
    @Resource
    private SyncFSContactService syncFSContactService;
    @Resource
    private MyCompanyMapper myCompanyMapper;

    // @Scheduled(cron = "0 0 21 * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void executeTask() {
        log.info("定时任务:增量同步刷新飞书用户 开始执行");
        long start = System.currentTimeMillis();
        List<MyCompanyPO> companyPOS = myCompanyMapper.selectList(null);
        if(CollUtil.isNotEmpty(companyPOS)){
            companyPOS.forEach(companyPO -> {
                List<String> list = Arrays.asList(companyPO.getDepartmentIds().split(","));
                syncFSContactService.syncDepartmentAndUser(list, companyPO);
            });
        }
        log.info("定时任务:增量同步刷新飞书用户,执行耗时:{}s", (System.currentTimeMillis() - start) / 1000);
    }
}
