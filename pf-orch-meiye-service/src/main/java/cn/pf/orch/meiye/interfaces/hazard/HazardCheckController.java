package cn.pf.orch.meiye.interfaces.hazard;

import cn.genn.core.model.page.PageResultDTO;
import cn.pf.orch.meiye.domain.hazard.command.HazardCheckCommand;
import cn.pf.orch.meiye.domain.hazard.dto.HazardCheckDTO;
import cn.pf.orch.meiye.domain.hazard.dto.HazardCheckPageDTO;
import cn.pf.orch.meiye.domain.hazard.query.HazardCheckPageQuery;
import cn.pf.orch.meiye.service.hazard.HazardCheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@Api(tags = "事故隐患-检查台账")
@RestController
@RequestMapping("/hazard/check")
public class HazardCheckController {

    @Resource
    private HazardCheckService hazardCheckService;

    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询列表")
    public PageResultDTO<HazardCheckPageDTO> page(@ApiParam(value = "查询类") @RequestBody HazardCheckPageQuery query) {
        return hazardCheckService.page(query);
    }

    @PostMapping("/get")
    @ApiOperation(value = "根据id查询")
    public HazardCheckDTO get(@ApiParam(value = "查询类") @RequestParam Long id) {
        return hazardCheckService.get(id);
    }


    @PostMapping("/check")
    @ApiOperation(value = "隐患自查/抽查")
    public void check(@RequestBody @Validated HazardCheckCommand command) {
        hazardCheckService.check(command);
    }
}
