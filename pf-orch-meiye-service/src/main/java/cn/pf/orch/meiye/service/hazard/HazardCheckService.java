package cn.pf.orch.meiye.service.hazard;

import cn.genn.core.model.page.PageResultDTO;
import cn.hutool.core.util.StrUtil;
import cn.pf.orch.meiye.assembler.HazardCheckAssembler;
import cn.pf.orch.meiye.config.CurrentUserHolder;
import cn.pf.orch.meiye.domain.hazard.command.HazardCheckCommand;
import cn.pf.orch.meiye.domain.hazard.dto.HazardCheckDTO;
import cn.pf.orch.meiye.domain.hazard.dto.HazardCheckPageDTO;
import cn.pf.orch.meiye.domain.hazard.po.HazardCheckPO;
import cn.pf.orch.meiye.domain.hazard.po.HazardMeasurePO;
import cn.pf.orch.meiye.domain.hazard.query.HazardCheckPageQuery;
import cn.pf.orch.meiye.mapper.HazardCheckMapper;
import cn.pf.orch.meiye.processor.HazardCheckProcessor;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;

@Slf4j
@Service
public class HazardCheckService {

    @Resource
    private HazardCheckMapper hazardCheckMapper;
    @Resource
    private HazardCheckProcessor processor;
    @Resource
    private HazardCheckAssembler assembler;

    public PageResultDTO<HazardCheckPageDTO> page(HazardCheckPageQuery query) {
        query.setCompanyIds(CurrentUserHolder.getRangeCompanyIds());
        PageResultDTO<HazardCheckPageDTO> pageResult = assembler.toPage(hazardCheckMapper.selectByPage(new Page<>(query.getPageNo(), query.getPageSize()), query));
        return pageResult;
    }

    public HazardCheckDTO get(Long id) {
        HazardCheckDTO hazardCheckDTO = hazardCheckMapper.selectDetailById(id);
        if(StrUtil.isNotBlank(hazardCheckDTO.getFileStr())){
            hazardCheckDTO.setFiles(Arrays.asList(hazardCheckDTO.getFileStr().split(",")));
        }

        return hazardCheckDTO;
    }


    public void check(HazardCheckCommand command) {
        HazardMeasurePO hazardMeasurePO = processor.check(command);
        HazardCheckPO po = HazardCheckPO.builder()
                .companyId(hazardMeasurePO.getCompanyId())
                .hazardMeasureId(hazardMeasurePO.getId())
                .hazardId(hazardMeasurePO.getHazardId())
                .level(command.getCheckLevel())
                .result(command.getCheckResult())
                .detail(command.getDetail())
                .files(String.join(",", command.getFiles()))
                .build();
        hazardCheckMapper.insert(po);
    }
}
