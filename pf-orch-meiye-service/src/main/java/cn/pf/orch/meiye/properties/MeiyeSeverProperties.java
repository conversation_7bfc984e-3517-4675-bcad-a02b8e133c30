package cn.pf.orch.meiye.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.ArrayList;
import java.util.List;


@Data
@RefreshScope
@ConfigurationProperties(prefix = "genn.meiye")
public class MeiyeSeverProperties {

    /**
     * 鉴权
     */
    @NestedConfigurationProperty
    private SsoAuthProperties permission = new SsoAuthProperties();

    /**
     * 登录
     */
    @NestedConfigurationProperty
    private LoginProperties login = new LoginProperties();

    /**
     * 卡片通知
     */
    @NestedConfigurationProperty
    private CardSendProperties cardSend = new CardSendProperties();

    /**
     * 审批
     */
    @NestedConfigurationProperty
    private List<ApprovalProperties> approval = new ArrayList<>();


}
