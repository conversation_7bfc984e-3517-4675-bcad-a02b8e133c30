package cn.pf.orch.meiye.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum ControlStatusEnum {


    CONTROL(0, "可控"),
    NO_CONTROL(1, "失控");


    @EnumValue
    @JsonValue
    private final int code;

    private final String description;

    private static final Map<Integer, ControlStatusEnum> VALUES = new HashMap<>();
    static {
        for (final ControlStatusEnum item : ControlStatusEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static ControlStatusEnum of(int code) {
        return VALUES.get(code);
    }
}
