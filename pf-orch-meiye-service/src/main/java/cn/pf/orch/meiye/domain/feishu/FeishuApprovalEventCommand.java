package cn.pf.orch.meiye.domain.feishu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class FeishuApprovalEventCommand {

    private String type;

    @JsonProperty("app_id")
    private String appId;

    /**
     * 审批定义code
     */
    @JsonProperty("approval_code")
    private String approvalCode;

    /**
     * 审批实例code
     */
    @JsonProperty("instance_code")
    private String instanceCode;

    /**
     * 状态修改时间
     */
    @JsonProperty("instance_operate_time")
    private String instanceOperateTime;

    @JsonProperty("operate_time")
    private String operateOldTime;

    /**
     * 状态
     */
    private String status;


    //-------审批任务专有字段------

    @JsonProperty("open_id")
    private String openId;

    @JsonProperty("task_id")
    private String taskId;

    /**
     * 审批节点唯一key
     */
    @JsonProperty("def_key")
    private String defKey;

    //-------处理过的字段-------

    /**
     * 操作时间
     */
    private LocalDateTime operateTime;

}
