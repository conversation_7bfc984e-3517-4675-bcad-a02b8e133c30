package cn.pf.orch.meiye.domain.emergencyrescuecheck.po;

import cn.pf.orch.meiye.enums.DelEnum;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 应急救援检查问题描述表(MeiyeCompanyProblemRecode)表实体类
 *
 * <AUTHOR>
 * @since 2025-04-17 15:54:47
 */
@Data
public class MeiyeCompanyProblemRecode extends Model<MeiyeCompanyProblemRecode> {

    private Long id;
    //煤矿id
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private Long companyId;
    //检查模板id
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private Long emergencyRescueCheckTempId;
    //问题描述
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String description;
    //附件链接
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String attachment;

    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private DelEnum del;

    //创建人id
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;
    //创建人
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;
    //创建时间
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    //更新人id
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;
    //更新人
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;
    //更新时间
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}

