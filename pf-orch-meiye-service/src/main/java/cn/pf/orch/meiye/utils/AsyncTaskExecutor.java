package cn.pf.orch.meiye.utils;

import cn.genn.core.utils.thread.ContextAwareThreadPoolExecutor;
import cn.genn.core.utils.thread.decorator.GennRequestContextDecorator;
import cn.genn.core.utils.thread.decorator.MDCContextAwareDecorator;
import cn.hutool.core.thread.NamedThreadFactory;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 自定义线程池,将不同任务归类
 * <AUTHOR>
 */
public class AsyncTaskExecutor {

    public final static ExecutorService resourceExecutor;

    static {
        resourceExecutor = new ContextAwareThreadPoolExecutor(
            5,
            5,
            60 * 1000L,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(Integer.MAX_VALUE),
            new NamedThreadFactory("update-resource-task", false),
            new GennRequestContextDecorator(new MDCContextAwareDecorator()));
    }


}
