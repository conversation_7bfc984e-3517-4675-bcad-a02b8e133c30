package cn.pf.orch.meiye.service.risk;

import cn.genn.core.model.page.PageResultDTO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.pf.orch.meiye.assembler.RiskIdentifyAssembler;
import cn.pf.orch.meiye.config.CurrentUserHolder;
import cn.pf.orch.meiye.domain.risk.command.MyRiskIdentifySaveCommand;
import cn.pf.orch.meiye.domain.risk.command.MyRiskIdentifyUpdateCommand;
import cn.pf.orch.meiye.domain.risk.dto.RiskIdentifyDTO;
import cn.pf.orch.meiye.domain.risk.po.RiskIdentifyPO;
import cn.pf.orch.meiye.domain.risk.po.RiskInfoPO;
import cn.pf.orch.meiye.domain.risk.query.RiskIdentifyQuery;
import cn.pf.orch.meiye.domain.system.po.MyCompanyPO;
import cn.pf.orch.meiye.enums.IdentifyStatusEnum;
import cn.pf.orch.meiye.mapper.MyCompanyMapper;
import cn.pf.orch.meiye.mapper.MyUserMapper;
import cn.pf.orch.meiye.mapper.RiskIdentifyMapper;
import cn.pf.orch.meiye.mapper.RiskInfoMapper;
import cn.pf.orch.meiye.processor.RiskIdentifyProcessor;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RiskIdentifyService {

    @Resource
    private RiskIdentifyMapper riskIdentifyMapper;
    @Resource
    private RiskIdentifyProcessor riskIdentifyProcessor;
    @Resource
    private RiskIdentifyAssembler assembler;
    @Resource
    private MyCompanyMapper myCompanyMapper;
    @Resource
    private RiskInfoMapper riskInfoMapper;
    @Resource
    private MyUserMapper myUserMapper;

    public PageResultDTO<RiskIdentifyDTO> page(RiskIdentifyQuery query) {
        //权限
        query.setCompanyIds(CurrentUserHolder.getRangeCompanyIds());
        PageResultDTO<RiskIdentifyDTO> pageResult = assembler.toPage(riskIdentifyMapper.selectByPage(new Page<>(query.getPageNo(), query.getPageSize()), query));
        if(CollUtil.isNotEmpty(pageResult.getList())){
            List<Long> ids = pageResult.getList().stream().map(RiskIdentifyDTO::getId).collect(Collectors.toList());
            List<RiskInfoPO> riskInfoPOS = riskInfoMapper.selectByIdentifyIds(ids);
            Map<Long, List<RiskInfoPO>> riskInfoMap = riskInfoPOS.stream().collect(Collectors.groupingBy(RiskInfoPO::getRiskIdentifyId));
            for (RiskIdentifyDTO riskIdentifyDTO : pageResult.getList()) {
                List<RiskInfoPO> riskInfoPOList = riskInfoMap.get(riskIdentifyDTO.getId());
                riskIdentifyDTO.setRiskCount((long) (CollUtil.isNotEmpty(riskInfoPOList)?riskInfoPOList.size():0));
            }
        }
        return pageResult;
    }

    public RiskIdentifyDTO get(Long id) {
        RiskIdentifyPO po = riskIdentifyMapper.selectById(id);
        RiskIdentifyDTO dto = assembler.PO2DTO(po);
        MyCompanyPO companyPO = myCompanyMapper.selectById(po.getCompanyId());
        dto.setCompanyName(companyPO.getName());
        return dto;
    }

    public Boolean save(MyRiskIdentifySaveCommand command) {
        riskIdentifyProcessor.saveCheck(command);
        //补充责任人
        command.setIdentifyOwnerName(myUserMapper.selectById(command.getIdentifyOwner()).getName());
        RiskIdentifyPO po = assembler.command2PO(command);
        po.setStatus(IdentifyStatusEnum.getStatus(po.getStartTime(), po.getEndTime()));
        riskIdentifyMapper.insert(po);
        return true;
    }

    public Boolean change(MyRiskIdentifyUpdateCommand command) {
        riskIdentifyProcessor.changeCheck(command);
        if(StrUtil.isNotBlank(command.getIdentifyOwner())){
            command.setIdentifyOwnerName(myUserMapper.selectById(command.getIdentifyOwner()).getName());
        }
        RiskIdentifyPO po = assembler.updateCommand2PO(command);
        if(ObjUtil.isNotNull(command.getStartTime()) && ObjUtil.isNotNull(command.getEndTime())){
            po.setStatus(IdentifyStatusEnum.getStatus(po.getStartTime(), po.getEndTime()));
        }
        riskIdentifyMapper.updateById(po);
        return true;
    }

    public Boolean batchRemove(List<Long> ids) {
        riskIdentifyProcessor.deleteCheck(ids);
        riskIdentifyMapper.deleteBatchIds(ids);
        return true;
    }

    public List<RiskIdentifyDTO> list() {
        List<Long> companyIds = CurrentUserHolder.getRangeCompanyIds();
        return assembler.PO2DTO(riskIdentifyMapper.selectByCompanyIds(companyIds));
    }

}
