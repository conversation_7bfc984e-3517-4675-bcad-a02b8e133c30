package cn.pf.orch.meiye.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum IdentifyTypeEnum {

    YEAR("year","年度辨识"),
    SPECIAL("special", "专项辨识"),
    ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;


    private static final Map<String, IdentifyTypeEnum> VALUES = new HashMap<>();
    static {
        for (final IdentifyTypeEnum item : IdentifyTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static IdentifyTypeEnum of(String code) {
        return VALUES.get(code);
    }
}
