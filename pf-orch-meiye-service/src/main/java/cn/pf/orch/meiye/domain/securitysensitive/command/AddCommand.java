package cn.pf.orch.meiye.domain.securitysensitive.command;

import cn.pf.orch.meiye.enums.SensitiveCategoryEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
public class AddCommand {

    @ApiModelProperty(value = "企业id")
    @NotNull
    private Long companyId;

    @ApiModelProperty(value = "企业名称")
    @NotEmpty
    private String companyName;

    @ApiModelProperty(value = "敏感信息类别")
    @NotNull
    private SensitiveCategoryEnum sensitiveCategory;

    @ApiModelProperty(value = "敏感信息描述")
    @NotEmpty
    private String sensitiveDescription;

    @ApiModelProperty(value = "管控措施")
    @NotEmpty
    private String controlMeasures;

    @ApiModelProperty(value = "责任人", example = "张三")
    @NotEmpty
    private String responsiblePerson;

    @ApiModelProperty(value = "责任人工号")
//    @NotEmpty
    private String responsiblePersonNumber;

    @ApiModelProperty(value = "责任人openId")
    @NotEmpty
    private String responsiblePersonOpenId;

    @ApiModelProperty(value = "责任部门", example = "信息安全部")
    @NotEmpty
    private String responsibleDepartment;

    @ApiModelProperty(value = "责任部门Id")
    @NotEmpty
    private String responsibleDepartmentId;

    @ApiModelProperty(value = "管控结果")
    @NotEmpty
    private String controlResult;

    @ApiModelProperty(value = "当前登录人id")
    private String createUserId;

    @ApiModelProperty(value = "当前登录人名称")
    private String createUserName;

}
