package cn.pf.orch.meiye.aspect;

import cn.pf.orch.meiye.enums.OperateTypeEnum;

import javax.validation.constraints.NotNull;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface UserLog {
    /**
     * 操作类型
     */
    @NotNull
    OperateTypeEnum operateType();
    /**
     * 详情
     */
    @NotNull
    String detail();

}
