package cn.pf.orch.meiye.job;

import cn.pf.orch.meiye.domain.hazard.po.HazardIdentifyPO;
import cn.pf.orch.meiye.domain.risk.po.RiskIdentifyPO;
import cn.pf.orch.meiye.enums.IdentifyStatusEnum;
import cn.pf.orch.meiye.mapper.HazardIdentifyMapper;
import cn.pf.orch.meiye.mapper.RiskIdentifyMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class RefreshIdentifyTask {

    @Resource
    private RiskIdentifyMapper riskIdentifyMapper;
    @Resource
    private HazardIdentifyMapper hazardIdentifyMapper;

    /**
     * 风险辨识计划状态刷新
     */
    @Scheduled(cron = "0 0 4 * * ?")
    public void executeTask() {
        List<RiskIdentifyPO> list = riskIdentifyMapper.selectByNoEndStatus();
        for (RiskIdentifyPO po : list) {
            IdentifyStatusEnum status = IdentifyStatusEnum.getStatus(po.getStartTime(), po.getEndTime());
            if (!status.equals(po.getStatus())) {
                po.setStatus(status);
                riskIdentifyMapper.updateById(po);
            }
        }
    }

    /**
     * 事故隐患状态刷新
     */
    @Scheduled(cron = "0 0 4 * * ?")
    public void executeTask2() {
        List<HazardIdentifyPO> list = hazardIdentifyMapper.selectByNoEndStatus();
        for (HazardIdentifyPO po : list) {
            IdentifyStatusEnum status = IdentifyStatusEnum.getStatus(po.getStartTime(), po.getEndTime());
            if (!status.equals(po.getStatus())) {
                po.setStatus(status);
                hazardIdentifyMapper.updateById(po);
            }
        }
    }
}
