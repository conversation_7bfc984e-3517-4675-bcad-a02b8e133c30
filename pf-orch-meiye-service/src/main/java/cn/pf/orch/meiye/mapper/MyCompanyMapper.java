package cn.pf.orch.meiye.mapper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.pf.orch.meiye.config.CurrentUserHolder;
import cn.pf.orch.meiye.domain.system.po.MyCompanyPO;
import cn.pf.orch.meiye.domain.system.query.MyCompanyQuery;
import cn.pf.orch.meiye.enums.AuthTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public interface MyCompanyMapper extends BaseMapper<MyCompanyPO> {

    default List<MyCompanyPO> queryList(MyCompanyQuery query, List<Long> companyIds) {
        List<Long> pids = new ArrayList<>();
        if (StrUtil.isNotBlank(query.getName())) {
            List<MyCompanyPO> pidNameList = selectList(Wrappers.lambdaQuery(MyCompanyPO.class).like(MyCompanyPO::getName, query.getParentName()));
            if (CollUtil.isEmpty(pidNameList)) {
                return CollUtil.newArrayList();
            }
            pids = pidNameList.stream().map(MyCompanyPO::getId).collect(Collectors.toList());
        }
        LambdaQueryWrapper<MyCompanyPO> wrapper = Wrappers.lambdaQuery(MyCompanyPO.class)
                .eq(ObjUtil.isNotEmpty(query.getAuthType()), MyCompanyPO::getAuthType, query.getAuthType())
                .in(CollUtil.isNotEmpty(pids), MyCompanyPO::getPid, pids)
                .in(MyCompanyPO::getId, companyIds)
                .like(StrUtil.isNotBlank(query.getName()), MyCompanyPO::getName, query.getName());
        return selectList(wrapper);
    }

    default List<MyCompanyPO> getChildrenById(Long companyId) {
        LambdaQueryWrapper<MyCompanyPO> wrapper = Wrappers.lambdaQuery(MyCompanyPO.class)
                .eq(MyCompanyPO::getPid, companyId);
        return selectList(wrapper);
    }

    default List<MyCompanyPO> selectByName(String name) {
        LambdaQueryWrapper<MyCompanyPO> wrapper = Wrappers.lambdaQuery(MyCompanyPO.class)
                .eq(MyCompanyPO::getName, name);
        return selectList(wrapper);
    }
    default List<MyCompanyPO> selectByLikeName(String name) {
        LambdaQueryWrapper<MyCompanyPO> wrapper = Wrappers.lambdaQuery(MyCompanyPO.class)
                .like(MyCompanyPO::getName, name);
        return selectList(wrapper);
    }

    default List<MyCompanyPO> selectByPid(Long pid) {
        LambdaQueryWrapper<MyCompanyPO> wrapper = Wrappers.lambdaQuery(MyCompanyPO.class)
                .eq(MyCompanyPO::getPid, pid);
        return selectList(wrapper);
    }

    default List<MyCompanyPO> selectByAuthType(AuthTypeEnum authType){
        LambdaQueryWrapper<MyCompanyPO> wrapper = Wrappers.lambdaQuery(MyCompanyPO.class)
                .in(MyCompanyPO::getId, CurrentUserHolder.getRangeCompanyIds())
                .in(MyCompanyPO::getAuthType, AuthTypeEnum.arrange(authType));
        return selectList(wrapper);
    }

    default List<MyCompanyPO> selectByNewAuthType(AuthTypeEnum authType){
        LambdaQueryWrapper<MyCompanyPO> wrapper = Wrappers.lambdaQuery(MyCompanyPO.class)
                .eq(MyCompanyPO::getAuthType, authType);
        return selectList(wrapper);
    }


    List<MyCompanyPO> selectByDepartmentIds(@Param("departmentIds") List<String> departmentIds);


    MyCompanyPO selectByUserId(@Param("userId") String userId);
}
