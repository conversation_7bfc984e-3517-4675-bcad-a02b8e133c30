package cn.pf.orch.meiye.service.system;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.pf.orch.meiye.domain.system.dto.DictDTO;
import cn.pf.orch.meiye.domain.system.po.DictPO;
import cn.pf.orch.meiye.domain.system.po.DictTypePO;
import cn.pf.orch.meiye.mapper.MyDictMapper;
import cn.pf.orch.meiye.mapper.MyDictTypeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DictService {

    @Resource
    private MyDictMapper myDictMapper;
    @Resource
    private MyDictTypeMapper myDictTypeMapper;

    public Map<String, List<DictDTO>> queryDataDictList(List<String> dictTypes) {
        Map<String, List<DictDTO>> dicMap = new HashMap<>();
        List<DictTypePO> dictTypePOList = myDictTypeMapper.queryDictType(dictTypes);
        if (CollectionUtil.isEmpty(dictTypePOList)) {
            return dicMap;
        }

        List<Integer> dictTypeIds = CollUtil.isNotEmpty(dictTypes) ? dictTypePOList.stream().map(DictTypePO::getId).collect(Collectors.toList()) : null;
        List<DictPO> dictPOList = myDictMapper.queryDict(dictTypeIds);
        if (CollectionUtil.isEmpty(dictPOList)) {
            return dicMap;
        }

        Map<Integer, List<DictPO>> dictGroupMap = dictPOList.stream().collect(Collectors.groupingBy(DictPO::getDictTypeId));
        for (DictTypePO dictType : dictTypePOList) {
            List<DictPO> tempDictVos = dictGroupMap.get(dictType.getId());
            dicMap.put(dictType.getDictType(), BeanUtil.copyToList(tempDictVos, DictDTO.class));
        }
        return dicMap;
    }

    public DictDTO queryDataDictDetail(Integer id) {
        return BeanUtil.copyProperties(myDictMapper.selectById(id), DictDTO.class);
    }

}

