package cn.pf.orch.meiye.handler.strategy;

import cn.pf.orch.meiye.domain.feishu.FeishuApprovalEventCommand;
import cn.pf.orch.meiye.domain.hazard.po.HazardInfoPO;
import cn.pf.orch.meiye.domain.risk.po.MyApprovalPO;
import cn.pf.orch.meiye.enums.RiskStatusEnum;
import cn.pf.orch.meiye.enums.feishu.ApprovalEventTypeEnum;
import cn.pf.orch.meiye.enums.feishu.FeishuInstanceStatusEnum;
import cn.pf.orch.meiye.mapper.HazardInfoMapper;
import cn.pf.orch.meiye.mapper.MyApprovalMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class HazardInstanceStrategy extends ApprovalEventStrategy {

    @Resource
    private HazardInfoMapper hazardInfoMapper;
    @Resource
    private MyApprovalMapper myApprovalMapper;

    @Override
    public String getApprovalCode() {
        return ApprovalEventTypeEnum.HAZARD_INSTANCE.getCode();
    }

    @Override
    public boolean approved(FeishuApprovalEventCommand event) {
        return this.handlerMethod(event, RiskStatusEnum.NO_CLOSE, FeishuInstanceStatusEnum.APPROVED);
    }

    @Override
    public boolean rejected(FeishuApprovalEventCommand event) {
        return this.handlerMethod(event,RiskStatusEnum.REJECT,FeishuInstanceStatusEnum.REJECTED);
    }

    @Override
    public boolean canceled(FeishuApprovalEventCommand event) {
        return this.handlerMethod(event,RiskStatusEnum.REJECT,FeishuInstanceStatusEnum.CANCELED);
    }

    @Override
    public boolean deleted(FeishuApprovalEventCommand event) {
        return this.handlerMethod(event,RiskStatusEnum.REJECT,FeishuInstanceStatusEnum.DELETED);
    }

    private boolean handlerMethod(FeishuApprovalEventCommand event,RiskStatusEnum riskStatus,FeishuInstanceStatusEnum instanceStatus) {
        HazardInfoPO po = hazardInfoMapper.selectByApprovalId(event.getInstanceCode());
        if (po == null) {
            return true;
        }
        hazardInfoMapper.updateById(HazardInfoPO.builder().id(po.getId()).status(riskStatus).build());
        myApprovalMapper.updateById(MyApprovalPO.builder().id(event.getInstanceCode())
                .status(instanceStatus)
                .operateTime(event.getOperateTime()).build());
        return true;
    }

}
