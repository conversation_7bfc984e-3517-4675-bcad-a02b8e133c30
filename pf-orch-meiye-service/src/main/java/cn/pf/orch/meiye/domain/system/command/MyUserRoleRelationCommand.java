package cn.pf.orch.meiye.domain.system.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class MyUserRoleRelationCommand {

    @ApiModelProperty(value = "用户id")
    @NotBlank(message="用户id不能为空")
    private String userId;

    @ApiModelProperty(value = "角色id")
    private List<Long> roleIdList;

}
