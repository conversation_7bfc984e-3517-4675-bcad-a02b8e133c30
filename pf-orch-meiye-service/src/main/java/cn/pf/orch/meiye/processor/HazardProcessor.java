package cn.pf.orch.meiye.processor;

import cn.genn.core.exception.BusinessException;
import cn.hutool.core.util.ObjUtil;
import cn.pf.orch.meiye.domain.hazard.command.MyHazardInfoUpdateCommand;
import cn.pf.orch.meiye.domain.hazard.po.HazardInfoPO;
import cn.pf.orch.meiye.enums.RiskStatusEnum;
import cn.pf.orch.meiye.exception.MessageCode;
import cn.pf.orch.meiye.mapper.HazardInfoMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class HazardProcessor {

    @Resource
    private HazardInfoMapper hazardInfoMapper;

    public void changeCheck(MyHazardInfoUpdateCommand command) {
    }

    public void deleteCheck(List<Long> idList) {
        List<HazardInfoPO> hazardPOList = hazardInfoMapper.selectBatchIds(idList);
        for (HazardInfoPO hazardInfoPO : hazardPOList) {
            if(! hazardInfoPO.getStatus().equals(RiskStatusEnum.WAIT_SUBMIT) && ! hazardInfoPO.getStatus().equals(RiskStatusEnum.REJECT)){
                throw new BusinessException(MessageCode.HAZARD_NO_DELETED);
            }
        }
    }

    public void closeCheck(Long id){
        HazardInfoPO hazardInfoPO = hazardInfoMapper.selectById(id);
        if(ObjUtil.isEmpty(hazardInfoPO)){
            throw new BusinessException(MessageCode.HAZARD_NOT_EXIST);
        }
        if(! hazardInfoPO.getStatus().equals(RiskStatusEnum.NO_CLOSE) ){
            throw new BusinessException(MessageCode.STATUS_NOT_MATCH_STOP_CLOSE);
        }
    }
}
