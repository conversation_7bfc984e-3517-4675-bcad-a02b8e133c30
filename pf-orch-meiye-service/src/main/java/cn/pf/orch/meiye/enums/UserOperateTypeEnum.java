package cn.pf.orch.meiye.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum UserOperateTypeEnum {

    ADD(1, "创建"),
    UPDATE(2, "更新"),
    DELETE(3, "删除"),
    REVIEW_SUCCESS(4, "审核"),
    Login(5, "登录"),
    logout(6, "登出")
    ;

    @EnumValue
    @JsonValue
    private final int code;

    private final String description;

    private static final Map<Integer, UserOperateTypeEnum> VALUES = new HashMap<>();
    static {
        for (final UserOperateTypeEnum item : UserOperateTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static UserOperateTypeEnum of(Integer code) {
        return VALUES.get(code);
    }
}
