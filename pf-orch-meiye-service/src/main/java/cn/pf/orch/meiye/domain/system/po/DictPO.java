package cn.pf.orch.meiye.domain.system.po;

import cn.genn.core.model.enums.DeletedEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * DictPO对象
 *
 * <AUTHOR>
 * @desc 数据字典表
 */
@Data
@Accessors(chain = true)
@TableName(value = "my_dict", autoResultMap = true)
public class DictPO {

    /**
     *
     */
    @TableId
    private Integer id;

    /**
     * 字典类型ID
     */
    @TableField("dict_type_id")
    private Integer dictTypeId;

    /**
     * 字典名称
     */
    @TableField("dict_name")
    private String dictName;

    /**
     * 字典值
     */
    @TableField("dict_value")
    private String dictValue;

    /**
     * 排序
     */
    @TableField("dict_sort")
    private Integer dictSort;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标识:0 未删除,1 已删除
     */
    @TableField("deleted")
    private DeletedEnum deleted;

}

