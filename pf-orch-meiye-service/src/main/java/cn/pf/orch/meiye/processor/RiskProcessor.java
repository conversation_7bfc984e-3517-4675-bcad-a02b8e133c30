package cn.pf.orch.meiye.processor;

import cn.genn.core.exception.BusinessException;
import cn.hutool.core.util.ObjUtil;
import cn.pf.orch.meiye.domain.risk.command.MyRiskInfoUpdateCommand;
import cn.pf.orch.meiye.domain.risk.po.RiskInfoPO;
import cn.pf.orch.meiye.enums.RiskStatusEnum;
import cn.pf.orch.meiye.exception.MessageCode;
import cn.pf.orch.meiye.mapper.RiskInfoMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class RiskProcessor {

    @Resource
    private RiskInfoMapper riskInfoMapper;

    public void changeCheck(MyRiskInfoUpdateCommand command) {
    }

    public void deleteCheck(List<Long> idList) {
        List<RiskInfoPO> riskPOList = riskInfoMapper.selectBatchIds(idList);
        for (RiskInfoPO riskInfoPO : riskPOList) {
            if(! riskInfoPO.getStatus().equals(RiskStatusEnum.WAIT_SUBMIT) && ! riskInfoPO.getStatus().equals(RiskStatusEnum.REJECT)){
                throw new BusinessException(MessageCode.RISK_NO_DELETED);
            }
        }
    }

    public void closeCheck(Long id){
        RiskInfoPO riskInfoPO = riskInfoMapper.selectById(id);
        if(ObjUtil.isEmpty(riskInfoPO)){
            throw new BusinessException(MessageCode.RISK_NOT_EXIST);
        }
        if(! riskInfoPO.getStatus().equals(RiskStatusEnum.NO_CLOSE) ){
            throw new BusinessException(MessageCode.STATUS_NOT_MATCH_STOP_CLOSE);
        }
    }
}
