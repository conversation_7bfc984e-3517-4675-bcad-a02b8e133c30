package cn.pf.orch.meiye.service.safetygenerationstandard.impl;

import cn.genn.core.exception.BusinessException;
import cn.pf.orch.meiye.domain.safetygenerationstandard.command.ScoredSystemDetailCommand;
import cn.pf.orch.meiye.domain.safetygenerationstandard.command.ScoredUpdateCommand;
import cn.pf.orch.meiye.domain.safetygenerationstandard.dto.ScoredStatusNumDTO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.dto.ScoringSystemDetailCorrelationDTO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.po.ScoringDetailCorrelationPO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.po.ScoringSystemDetailTemplatePO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.query.ScoredDetailTemplateQuery;
import cn.pf.orch.meiye.domain.safetygenerationstandard.query.ScoredStatusNumQuery;
import cn.pf.orch.meiye.domain.safetygenerationstandard.query.ScoredSystemDetailQuery;
import cn.pf.orch.meiye.enums.DelEnum;
import cn.pf.orch.meiye.enums.ScoringStatusEnum;
import cn.pf.orch.meiye.mapper.ScoringDetailCorrelationMapper;
import cn.pf.orch.meiye.service.safetygenerationstandard.ScoringDetailCorrelationService;
import cn.pf.orch.meiye.service.safetygenerationstandard.ScoringRecordsService;
import cn.pf.orch.meiye.service.safetygenerationstandard.ScoringSystemCorrelationService;
import cn.pf.orch.meiye.service.safetygenerationstandard.ScoringSystemDetailTemplateService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lark.oapi.core.utils.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 管理项明细评分记录表(ScoringDetailCorrelation)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-22 16:49:34
 */
@Slf4j
@Service
public class ScoringDetailCorrelationServiceImpl extends ServiceImpl<ScoringDetailCorrelationMapper, ScoringDetailCorrelationPO> implements ScoringDetailCorrelationService {

    @Resource
    private ScoringSystemDetailTemplateService scoringSystemDetailTemplateService;
    @Resource
    private ScoringDetailCorrelationMapper scoringDetailCorrelationMapper;

    private ScoringSystemCorrelationService scoringSystemCorrelationService;
    private ScoringRecordsService scoringRecordsService;
    @Autowired
    public void setService(@Lazy ScoringSystemCorrelationService scoringSystemCorrelationService, @Lazy ScoringRecordsService scoringRecordsService) {
        this.scoringSystemCorrelationService = scoringSystemCorrelationService;
        this.scoringRecordsService = scoringRecordsService;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    @Override
    public void initCompanyScoredDetail(Long scoredRecordId, Long scoredSystemTemplateId, Long scoredSystemCorrelationId) {
        // 查询管理项下的项目内容
        List<ScoringSystemDetailTemplatePO> scoringSystemDetailTemplatePOS = scoringSystemDetailTemplateService.selectDetailTemplateByCondition(new ScoredDetailTemplateQuery(scoredSystemTemplateId));
        Optional.ofNullable(scoringSystemDetailTemplatePOS).orElseThrow(() -> new BusinessException("管理项未关联具体项目！"));
        List<ScoringDetailCorrelationPO> insertList = Lists.newArrayList();
        for (ScoringSystemDetailTemplatePO templatePO : scoringSystemDetailTemplatePOS) {
            ScoringDetailCorrelationPO correlationPO = new ScoringDetailCorrelationPO();
            correlationPO.setScoringRecordId(scoredRecordId);
            correlationPO.setScoringSystemCorrelationId(scoredSystemCorrelationId);
            correlationPO.setScoringSystemDetailId(templatePO.getId());
            correlationPO.setActualScore(BigDecimal.ZERO);
            correlationPO.setStatus(ScoringStatusEnum.UN_SCORED);
            insertList.add(correlationPO);
        }
        this.saveBatch(insertList);
    }


    @Override
    public List<ScoringSystemDetailCorrelationDTO> queryScoringSystemDetailCorrelationList(ScoredSystemDetailQuery query) {
        LambdaQueryWrapper<ScoringDetailCorrelationPO> wrapper = Wrappers.lambdaQuery(ScoringDetailCorrelationPO.class)
                .eq(ScoringDetailCorrelationPO::getScoringRecordId, query.getScoredRecordId())
                .eq(ScoringDetailCorrelationPO::getScoringSystemCorrelationId, query.getScoringSystemCorrelationId())
                .eq(ObjectUtils.isNotNull(query.getScoringStatus()), ScoringDetailCorrelationPO::getStatus, query.getScoringStatus());
        List<ScoringDetailCorrelationPO> scoringDetailCorrelationPOS = scoringDetailCorrelationMapper.selectList(wrapper);
        log.info("scored system detail correlation list size:{}", scoringDetailCorrelationPOS.size());
        if (CollectionUtils.isEmpty(scoringDetailCorrelationPOS)) {
            log.info("scored system detail correlation is empty");
            return Collections.emptyList();
        }
        // 赋值模板信息; dto=未进行过滤的集合
        List<ScoringSystemDetailCorrelationDTO> dtos = Lists.newArrayList();
        scoringDetailCorrelationPOS.forEach(po -> {
            ScoringSystemDetailCorrelationDTO detailCorrelationDTO = new ScoringSystemDetailCorrelationDTO();
            Long detailTemplateId = po.getScoringSystemDetailId();
            Long id = po.getId();
            Optional.ofNullable(scoringSystemDetailTemplateService.selectById(detailTemplateId)).ifPresent( templatePOInfo -> {
                detailCorrelationDTO.setScoredDetailCorrelationId(id);
                detailCorrelationDTO.setItemContent(templatePOInfo.getItemContent());
                detailCorrelationDTO.setBasicRequirements(templatePOInfo.getBasicRequirements());
                detailCorrelationDTO.setStandardScore(templatePOInfo.getStandardScore());
                detailCorrelationDTO.setScoringMethod(templatePOInfo.getScoringMethod());
                detailCorrelationDTO.setActualScore(po.getActualScore());
                detailCorrelationDTO.setStatus(po.getStatus());
                dtos.add(detailCorrelationDTO);
            });
        });
        List<ScoringSystemDetailCorrelationDTO> result = dtos;
        // 条件筛选
        if(ObjectUtils.isNotNull(query.getItemContent())) {
            result =  dtos.stream().filter(info -> info.getItemContent().contains(query.getItemContent())).collect(Collectors.toList());
        }
        return result;
    }

    @Transactional
    @Override
    public void scoring(List<ScoredSystemDetailCommand> scoredList) {
        log.info("scoring detail info:{}", scoredList);
        scoredList = scoredList.stream()
                .filter(info -> ObjectUtils.isNotNull(info.getScored()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(scoredList)) {
            return;
        }
        Long scoredRecordId = scoredList.get(0).getScoredRecordId();
        Long scoringSystemCorrelationId = scoredList.get(0).getScoringSystemCorrelationId();
        /**
         * 级联更新上层管理项的评分及进度，以及煤矿的总分和总进度
         */
        List<ScoringDetailCorrelationPO> updateList = Lists.newArrayList();
        scoredList.forEach(scored -> {
            ScoringDetailCorrelationPO po = scoringDetailCorrelationMapper.selectById(scored.getScoredDetailCorrelationId());
            po.setActualScore(scored.getScored());
            po.setStatus(ScoringStatusEnum.SCORED);
            updateList.add(po);
        });
        this.updateBatchById(updateList);

        // 级联更新管理体系评分
        scoringSystemCorrelationService.scoredSystem(new ScoredUpdateCommand(scoredRecordId,scoringSystemCorrelationId));

        // 级联更新生产标准化列表评分
        scoringRecordsService.scoredSystem(new ScoredUpdateCommand(scoredRecordId,null));
    }


    @Override
    public ScoredStatusNumDTO queryScoredStatusNum(ScoredStatusNumQuery query) {
        LambdaQueryWrapper<ScoringDetailCorrelationPO> wrapper = Wrappers.lambdaQuery(ScoringDetailCorrelationPO.class)
                .eq(ObjectUtils.isNotNull(query.getScoredRecordId()), ScoringDetailCorrelationPO::getScoringRecordId, query.getScoredRecordId())
                .eq(ObjectUtils.isNotNull(query.getScoringSystemCorrelationId()), ScoringDetailCorrelationPO::getScoringSystemCorrelationId, query.getScoringSystemCorrelationId());
        List<ScoringDetailCorrelationPO> pos = scoringDetailCorrelationMapper.selectList(wrapper);
        if(CollectionUtils.isEmpty(pos)) {
            return new ScoredStatusNumDTO(0L, 0L);
        }
        long scoredNumber = pos.stream().filter(po -> ScoringStatusEnum.SCORED.equals(po.getStatus())).count();
        long total = pos.size();
        return new ScoredStatusNumDTO(scoredNumber, total);
    }

    @Transactional
    @Override
    public void deleteByScoringRecordId(Long scoringRecordId) {
        Optional.ofNullable(scoringRecordId).orElseThrow(() -> new RuntimeException("scoringRecordId is null, forbid delete"));
        LambdaQueryWrapper<ScoringDetailCorrelationPO> wrapper = Wrappers.lambdaQuery(ScoringDetailCorrelationPO.class)
                .eq(ScoringDetailCorrelationPO::getScoringRecordId, scoringRecordId);
        List<ScoringDetailCorrelationPO> detailCorrelationPOS = scoringDetailCorrelationMapper.selectList(wrapper);
        Optional.ofNullable(detailCorrelationPOS).orElseThrow(() -> new RuntimeException("ScoringDetailCorrelationPO is null, forbid delete"));
        detailCorrelationPOS.forEach(scoringSystemCorrelationPO -> {
            scoringSystemCorrelationPO.setDel(DelEnum.NO_DELETE);
        });
        this.updateBatchById(detailCorrelationPOS);
    }

}