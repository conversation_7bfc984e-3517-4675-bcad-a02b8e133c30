package cn.pf.orch.meiye.domain.system.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class CompanyLicenseAddCommand {

    @ApiModelProperty(value = "许可证类型")
    @NotBlank(message = "许可证类型不能为空")
    private String type;

    @ApiModelProperty(value = "许可证名称")
    @NotBlank(message = "许可证名称不能为空")
    private String name;

    @ApiModelProperty(value = "文件id")
    private List<String> licenses;

}
