package cn.pf.orch.meiye.assembler;

import cn.pf.orch.meiye.domain.safetygenerationstandard.command.AddRecordCommand;
import cn.pf.orch.meiye.domain.safetygenerationstandard.command.UpdateRecordCommand;
import cn.pf.orch.meiye.domain.safetygenerationstandard.dto.ScoringRecordsDTO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.dto.ScoringSystemDetailCorrelationDTO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.po.ScoringDetailCorrelationPO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.po.ScoringRecordsPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SafetyGenerationStandardAssembler {

    ScoringRecordsDTO scoringRecordPOToDTO(ScoringRecordsPO po);
    List<ScoringRecordsDTO> scoringRecordPOToDTOList(List<ScoringRecordsPO> po);


    ScoringRecordsPO addCommandToPO(AddRecordCommand command);

    ScoringRecordsPO addCommandToPO(UpdateRecordCommand command);

    /**
     * 管理項详情评分dto转换
     * @param pos
     * @return
     */
    ScoringSystemDetailCorrelationDTO scoredDetailCorrelatePOToDTO (ScoringDetailCorrelationPO pos);
    List<ScoringSystemDetailCorrelationDTO> scoredDetailCorrelatePOToDTOs (List<ScoringDetailCorrelationPO> pos);
}
