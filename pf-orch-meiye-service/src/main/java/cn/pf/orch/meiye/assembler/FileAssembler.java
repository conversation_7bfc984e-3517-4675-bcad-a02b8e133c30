package cn.pf.orch.meiye.assembler;

import cn.pf.orch.meiye.domain.system.dto.FileInfoDTO;
import cn.pf.orch.meiye.domain.system.po.FileDetailPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface FileAssembler {


    List<FileInfoDTO> PO2DTO(List<FileDetailPO> pos);

    @Mapping(source = "id", target = "fileKey")
    FileInfoDTO PO2DTO(FileDetailPO pos);
}
