package cn.pf.orch.meiye.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum RiskLevelEnum {

    ONE(1, "重大风险"),
    TWO(2,"较大风险"),
    THREE(3,"一般风险"),
    FOUR(4,"低风险"),
    ;

    @EnumValue
    @JsonValue
    private final int type;

    private final String description;

    private static final Map<Integer, IdentifyStatusEnum> VALUES = new HashMap<>();
    static {
        for (final IdentifyStatusEnum item : IdentifyStatusEnum.values()) {
            VALUES.put(item.getType(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static IdentifyStatusEnum of(int code) {
        return VALUES.get(code);
    }
}
