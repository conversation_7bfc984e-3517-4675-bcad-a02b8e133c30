package cn.pf.orch.meiye.service.emergencyrescuecheck.impl;

import cn.pf.orch.meiye.assembler.EmergencyRescueCheckAssembler;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.dto.CheckTemplateDTO;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.dto.EmergencyRescueCheckInfoDTO;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.dto.InspectionDropDownDTO;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.po.MeiyeInspectionTemplate;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.query.EmergencyRescueCheckInfoQuery;
import cn.pf.orch.meiye.mapper.MeiyeInspectionTemplateMapper;
import cn.pf.orch.meiye.service.emergencyrescuecheck.MeiyeInspectionTemplateService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lark.oapi.core.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 检查项目模板表(MeiyeInspectionTemplate)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-15 11:29:44
 */
@Service
public class MeiyeInspectionTemplateServiceImpl extends ServiceImpl<MeiyeInspectionTemplateMapper, MeiyeInspectionTemplate> implements MeiyeInspectionTemplateService {

    @Resource
    private MeiyeInspectionTemplateMapper templateMapper;
    @Resource
    private EmergencyRescueCheckAssembler assembler;

    @Override
    public Map<String, List<CheckTemplateDTO>> checkTemplates() {
        List<MeiyeInspectionTemplate> meiyeInspectionTemplates = templateMapper.selectList(new QueryWrapper<MeiyeInspectionTemplate>());
        List<CheckTemplateDTO> checkTemplates = assembler.checkTemplateTODTO(meiyeInspectionTemplates);
        return checkTemplates.stream().collect(Collectors.groupingBy(CheckTemplateDTO::getInspectionType));
    }

    @Override
    public Page<EmergencyRescueCheckInfoDTO> queryEmergencyCheckInfo(EmergencyRescueCheckInfoQuery query) {
        return templateMapper.queryEmergencyCheckInfo(new Page<>(query.getPageNo(),query.getPageSize()), query);
    }

    @Override
    public List<InspectionDropDownDTO> dropDown() {
        List<Map<String, String>> maps = templateMapper.dropDown();
        List<InspectionDropDownDTO> downDTOS = Lists.newArrayList();

        maps.forEach(map -> {
            InspectionDropDownDTO dto = new InspectionDropDownDTO();
            String inspectionType = map.get("inspectionType");
            String projectsList = map.get("projectsList");
            dto.setInspectionType(inspectionType);
            if(Objects.nonNull(projectsList)){
                dto.setChildren(Arrays.asList(projectsList.split("、")));
            }
            downDTOS.add(dto);
        });
        return downDTOS;
    }

}

