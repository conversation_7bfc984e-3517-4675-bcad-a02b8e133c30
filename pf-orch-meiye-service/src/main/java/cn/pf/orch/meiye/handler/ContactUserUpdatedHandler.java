package cn.pf.orch.meiye.handler;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.collection.CollUtil;
import cn.pf.orch.feishu.FeishuAppClient;
import cn.pf.orch.feishu.model.callback.EventCallbackCommand;
import cn.pf.orch.meiye.domain.feishu.FeishuUserUpdateCommand;
import cn.pf.orch.meiye.domain.system.po.MyCompanyPO;
import cn.pf.orch.meiye.domain.system.po.MyUserPO;
import cn.pf.orch.meiye.domain.system.po.MyUserRoleRelPO;
import cn.pf.orch.meiye.enums.RoleTypeEnum;
import cn.pf.orch.meiye.enums.feishu.EventCallbackEnum;
import cn.pf.orch.meiye.mapper.MyCompanyMapper;
import cn.pf.orch.meiye.mapper.MyRoleMapper;
import cn.pf.orch.meiye.mapper.MyUserMapper;
import cn.pf.orch.meiye.mapper.MyUserRoleRelMapper;
import com.lark.oapi.service.contact.v3.model.Department;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ContactUserUpdatedHandler implements CallbackHandler<FeishuUserUpdateCommand> {

    @Resource
    private MyUserMapper myUserMapper;
    @Resource
    private MyCompanyMapper myCompanyMapper;
    @Resource
    private FeishuAppClient feishuAppClient;
    @Resource
    private MyRoleMapper myRoleMapper;
    @Resource
    private MyUserRoleRelMapper myUserRoleRelMapper;

    @Override
    public void handle(EventCallbackCommand<FeishuUserUpdateCommand> command) {
        String json = JsonUtils.toJson(command.getEvent());
        FeishuUserUpdateCommand event = JsonUtils.parse(json, FeishuUserUpdateCommand.class);
        FeishuUserUpdateCommand.UserDTO newUser = event.getObject();
        FeishuUserUpdateCommand.UserDTO oldUser = event.getOldObject();
        if(!newUser.getMobile().equals(oldUser.getMobile())){
            myUserMapper.updateById(MyUserPO.builder().id(newUser.getOpenId()).mobile(newUser.getMobile()).build());
        }
        if(!newUser.getDepartmentIds().equals(oldUser.getDepartmentIds())){
            List<String> list = newUser.getDepartmentIds();
            if(CollUtil.isEmpty(list)){
                myUserMapper.deleteByIds(Collections.singletonList(newUser.getOpenId()));
            }else {
                String departmentId = list.get(0);
                //获取所有上级部门,与当前组织对应的部门比较,取出相同的部门id
                List<Department> parentDepartment = feishuAppClient.getContactService().getParentDepartment(departmentId);
                if(CollUtil.isNotEmpty(parentDepartment)){
                    List<String> departmentIds = parentDepartment.stream().map(Department::getOpenDepartmentId).collect(Collectors.toList());
                    List<MyCompanyPO> companyPOS = myCompanyMapper.selectByDepartmentIds(departmentIds);
                    if(CollUtil.isNotEmpty(companyPOS)){
                        MyCompanyPO companyPO = companyPOS.get(0);
                        Long roleId = myRoleMapper.getSystemRoleId(companyPO.getAuthType());
                        myUserMapper.updateById(MyUserPO.builder().id(newUser.getOpenId()).departmentId(departmentId).authType(companyPO.getAuthType()).companyId(companyPO.getId()).build());
                        myUserRoleRelMapper.deleteByUserIds(Collections.singletonList(newUser.getOpenId()));
                        myUserRoleRelMapper.insert(MyUserRoleRelPO.builder().userId(newUser.getOpenId()).roleId(roleId).roleType(RoleTypeEnum.SYSTEM).build());
                        myUserMapper.updateApprovalByIds(Collections.singletonList(newUser.getOpenId()), null);
                    }
                }
            }
        }

    }

    @Override
    public EventCallbackEnum event() {
        return EventCallbackEnum.USER_UPDATE_V3;
    }
}
