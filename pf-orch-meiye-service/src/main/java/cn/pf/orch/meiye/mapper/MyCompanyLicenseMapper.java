package cn.pf.orch.meiye.mapper;

import cn.pf.orch.meiye.domain.system.po.MyCompanyLicensePO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MyCompanyLicenseMapper extends BaseMapper<MyCompanyLicensePO> {

    default List<MyCompanyLicensePO>  selectByCompanyId(Long companyId){
        return selectList(Wrappers.lambdaQuery(MyCompanyLicensePO.class).eq(MyCompanyLicensePO::getCompanyId, companyId));
    }

    default  void deleteCompanyId(Long companyId){
        delete(Wrappers.lambdaQuery(MyCompanyLicensePO.class).eq(MyCompanyLicensePO::getCompanyId, companyId));
    }

    int batchInsert(List<MyCompanyLicensePO> list);
}
