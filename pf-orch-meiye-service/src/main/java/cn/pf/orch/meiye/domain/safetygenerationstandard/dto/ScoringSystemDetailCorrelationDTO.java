package cn.pf.orch.meiye.domain.safetygenerationstandard.dto;

import cn.pf.orch.meiye.enums.ScoringStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "具体管理项下各内容评分表")
public class ScoringSystemDetailCorrelationDTO {


    @ApiModelProperty(value = "管理项详情列id")
    private Long scoredDetailCorrelationId;

    @ApiModelProperty(value = "项目内容")
    private String itemContent;

    @ApiModelProperty(value = "基本要求")
    private String basicRequirements;

    @ApiModelProperty(value = "标注分值")
    private BigDecimal standardScore;

    @ApiModelProperty(value = "评分方法")
    private String scoringMethod;

    @ApiModelProperty(value = "实际得分")
    private BigDecimal actualScore;

    //状态（0-未评分；1-已评分）
    private ScoringStatusEnum status;


}
