package cn.pf.orch.meiye.assembler;

import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.core.model.page.PageResultDTO;
import cn.pf.orch.meiye.domain.system.dto.MyRoleDTO;
import cn.pf.orch.meiye.domain.system.po.MyRolePO;
import cn.pf.orch.meiye.domain.system.query.MyRolePageQuery;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RoleAssembler extends QueryAssembler<MyRolePageQuery, MyRolePO, MyRoleDTO> {


    @Mapping(target = "pageNo", source = "current")
    @Mapping(target = "pageSize", source = "size")
    @Mapping(target = "list", source = "records")
    @Mapping(target = "totalPages", source = "pages")
    PageResultDTO<MyRoleDTO> toPage(IPage<MyRoleDTO> poPage);
}
