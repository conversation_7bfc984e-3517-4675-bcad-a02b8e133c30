package cn.pf.orch.meiye.domain.safetygenerationstandard.command;

import cn.pf.orch.meiye.enums.ScoringLevelEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
@ApiModel("新增煤矿评分记录")
public class AddRecordCommand {

    @ApiModelProperty(value = "煤矿id")
    @NotNull
    private Long companyId;

    @ApiModelProperty(value = "煤矿名称")
    @NotNull
    private String companyName;

    @ApiModelProperty(value = "评分级别")
    @NotNull
    private ScoringLevelEnum scoreLevel;

    @ApiModelProperty(value = "评分时间")
    @NotNull
    private LocalDate scoreDate;
}
