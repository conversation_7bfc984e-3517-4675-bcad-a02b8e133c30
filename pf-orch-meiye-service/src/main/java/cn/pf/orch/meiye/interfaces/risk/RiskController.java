package cn.pf.orch.meiye.interfaces.risk;

import cn.genn.core.model.page.PageResultDTO;
import cn.pf.orch.meiye.domain.risk.command.*;
import cn.pf.orch.meiye.domain.risk.dto.RiskCountDTO;
import cn.pf.orch.meiye.domain.risk.dto.RiskInfoDTO;
import cn.pf.orch.meiye.domain.risk.dto.RiskInfoPageDTO;
import cn.pf.orch.meiye.domain.risk.query.RiskInfoQuery;
import cn.pf.orch.meiye.service.risk.RiskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api(tags = "安全风险-风险")
@RestController
@RequestMapping("/risk")
public class RiskController {

    @Resource
    private RiskService riskService;

    @PostMapping("/page")
    @ApiOperation(value = "分页查询列表")
    public PageResultDTO<RiskInfoPageDTO> page(@ApiParam(value = "查询类") @RequestBody RiskInfoQuery query) {
        return riskService.page(query);
    }

    @PostMapping("/count")
    @ApiOperation(value = "计算风险数量")
    public List<RiskCountDTO> count(@ApiParam(value = "查询类") @RequestBody RiskInfoQuery query){
        return riskService.count(query);
    }

    @PostMapping("/get")
    @ApiOperation(value = "根据id查询")
    public RiskInfoDTO get(@ApiParam(value = "查询类") @RequestParam Long id) {
        return riskService.get(id);
    }

    @PostMapping("/save")
    @ApiOperation(value = "添加")
    public Long save(@RequestBody @Validated MyRiskInfoSaveCommand command) {
        return riskService.save(command);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public Long change(@RequestBody @Validated MyRiskInfoUpdateCommand command) {
        return riskService.change(command);
    }

    @PostMapping("/batch/delete")
    @ApiOperation(value = "批量删除")
    public Boolean batchRemove(@RequestBody List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return true;
        }
        return riskService.batchRemove(idList);
    }

    @PostMapping("/saveRiskMeasure")
    @ApiOperation(value = "新增风险措施")
    public Boolean saveRiskMeasure(@RequestBody @Validated RiskMeasureSaveCommand command) {
        return riskService.saveRiskMeasure(command);
    }

    @PostMapping("/updateRiskMeasure")
    @ApiOperation(value = "更新风险措施")
    public Boolean updateRiskMeasure(@RequestBody @Validated RiskMeasureUpdateCommand command) {
        return riskService.updateRiskMeasure(command);
    }

    @PostMapping("/batch/deleteRiskMeasure")
    @ApiOperation(value = "删除风险措施")
    public Boolean deleteRiskMeasure(@RequestBody List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return true;
        }
        return riskService.deleteRiskMeasure(idList);
    }

    @PostMapping("/sendNotify")
    @ApiOperation(value = "发起飞书通知")
    public Boolean sendNotify(@RequestBody @Validated SendNotifyCommand command) {
        return riskService.sendNotify(command);
    }

    @PostMapping("/sendApproval")
    @ApiOperation(value = "提交审核")
    public Boolean sendApproval(@RequestParam("id") Long id) {
        return riskService.sendApproval(id);
    }

    @PostMapping("/close")
    @ApiOperation(value = "关闭风险")
    public Boolean close(@RequestParam("id") Long id) {
        return riskService.close(id);
    }



}
