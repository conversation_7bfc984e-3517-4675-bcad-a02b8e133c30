package cn.pf.orch.meiye.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DiscovererUnitsEnum {


    GROUP(1,"集团"),
    REGION(2,"区域公司"),
    COLLIERY(3,"煤矿"),
            ;

    @EnumValue
    @JsonValue
    private final Integer code;

    private final String description;

}
