package cn.pf.orch.meiye.domain.system.dto;

import cn.pf.orch.meiye.enums.ApprovalBizTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HomeMessagesDTO {

    private String approvalId;

    private Long bizId;

    private ApprovalBizTypeEnum bizType;

    private String code;

    @ApiModelProperty(value = "审批状态")
    private String status;

    private String remark;

    private String comment;

    @ApiModelProperty(value = "审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime operateTime;

    @ApiModelProperty(value = "审核人")
    private String approvalOwner;

    @ApiModelProperty(value = "审核人名称")
    private String approvalUserName;
}
