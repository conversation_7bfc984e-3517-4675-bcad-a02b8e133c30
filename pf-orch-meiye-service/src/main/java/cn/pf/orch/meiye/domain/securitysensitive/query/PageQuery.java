package cn.pf.orch.meiye.domain.securitysensitive.query;

import cn.genn.core.model.page.PageSortQuery;
import cn.pf.orch.meiye.enums.SensitiveCategoryEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class PageQuery extends PageSortQuery {

    @ApiModelProperty(value = "企业名称", example = "ABC科技有限公司")
    private Long companyId;

    @ApiModelProperty(value = "敏感信息类别")
    private SensitiveCategoryEnum sensitiveCategory;

    @ApiModelProperty(value = "敏感信息描述")
    private String sensitiveDescription;

    @ApiModelProperty(value = "责任人", example = "张三")
    private String responsiblePerson;

    @ApiModelProperty(value = "责任部门", example = "信息安全部")
    private String responsibleDepartment;

    @ApiModelProperty(value = "责任部门Id")
    private String responsibleDepartmentId;

    @ApiModelProperty(value = "开始日期", example = "2023-01-01")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期", example = "2023-12-31")
    private LocalDate endDate;
    // 权限控制
    private List<Long> rangeCompanyIds;



}
