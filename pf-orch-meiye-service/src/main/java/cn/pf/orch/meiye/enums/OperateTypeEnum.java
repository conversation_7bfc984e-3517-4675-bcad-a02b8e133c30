package cn.pf.orch.meiye.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public enum OperateTypeEnum {

    QUERY(1,"查询"),
    ADD(2, "创建"),
    UPDATE(3, "更新"),
    DELETE(4, "删除"),
    LOGIN(5, "登录"),

    //可根据特定业务增加类型,例如审批等
    ;

    @EnumValue
    @JsonValue
    private final int code;

    private final String description;

    OperateTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Integer, OperateTypeEnum> VALUES = new HashMap<>();

    static {
        for (final OperateTypeEnum item : OperateTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static OperateTypeEnum of(int code) {
        return VALUES.get(code);
    }


    public static boolean isAdd(OperateTypeEnum opType) {
        return ADD.equals(opType);
    }
    public static boolean isUpdate(OperateTypeEnum opType) {
        return UPDATE.equals(opType);
    }

}

