package cn.pf.orch.meiye.service.system;

import cn.pf.orch.meiye.assembler.ResourceAssembler;
import cn.pf.orch.meiye.config.CurrentUserHolder;
import cn.pf.orch.meiye.domain.system.dto.MyResourceTreeDTO;
import cn.pf.orch.meiye.domain.system.po.MyResourcePO;
import cn.pf.orch.meiye.enums.AuthTypeEnum;
import cn.pf.orch.meiye.mapper.MyResourceMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class ResourceService {

    @Resource
    private MyResourceMapper myResourceMapper;
    @Resource
    private ResourceAssembler resourceAssembler;

    public List<MyResourceTreeDTO> queryTreeRange() {
        AuthTypeEnum authType = CurrentUserHolder.getAuthType();
        List<MyResourcePO> resourcePOList = myResourceMapper.selectByAuthType(authType);
        return resourceAssembler.buildTree(resourcePOList);
    }


}
