package cn.pf.orch.meiye.domain.safetygenerationstandard.dto;

import cn.pf.orch.meiye.enums.ScoringLevelEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.checkerframework.checker.units.qual.A;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@ApiModel(description = "安全生产标准化")
public class ScoringRecordsDTO {


    private Long id;

    @ApiModelProperty(value = "煤矿ID")
    private Long companyId;

    @ApiModelProperty(value = "煤矿名称")
    private String companyName;

    @ApiModelProperty(value = "评分等级") // 根据实际枚举值调整
    private ScoringLevelEnum scoreLevel;

    @ApiModelProperty(value = "评分日期")
    private LocalDate scoreDate;

    @ApiModelProperty(value = "分数")
    private BigDecimal totalScore;

    @ApiModelProperty(value = "进度",hidden = true)
    private BigDecimal progress;

    @ApiModelProperty(value = "已评分数量")
    private Long scoredNumber;

    @ApiModelProperty(value = "项目总数")
    private Long totalSize;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

}
