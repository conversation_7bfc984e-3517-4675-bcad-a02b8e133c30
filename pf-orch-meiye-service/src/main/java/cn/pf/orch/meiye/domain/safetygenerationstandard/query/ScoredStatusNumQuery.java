package cn.pf.orch.meiye.domain.safetygenerationstandard.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ScoredStatusNumQuery {

    @ApiModelProperty(value = "安全生产化记录id")
    private Long scoredRecordId;

    @ApiModelProperty(value = "管理部分得分表ID")
    private Long scoringSystemCorrelationId;

    public ScoredStatusNumQuery(Long scoredRecordId) {
        this.scoredRecordId = scoredRecordId;
    }
}
