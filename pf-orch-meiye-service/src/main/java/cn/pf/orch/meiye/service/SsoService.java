package cn.pf.orch.meiye.service;

import cn.genn.core.context.BaseRequestContext;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.pf.orch.feishu.FeishuAppClient;
import cn.pf.orch.feishu.model.AuthUserAccessTokenModel;
import cn.pf.orch.meiye.aspect.UserOperationContext;
import cn.pf.orch.meiye.assembler.ResourceAssembler;
import cn.pf.orch.meiye.assembler.UserInfoAssembler;
import cn.pf.orch.meiye.common.CacheConstants;
import cn.pf.orch.meiye.common.Constants;
import cn.pf.orch.meiye.config.UserInfoDTO;
import cn.pf.orch.meiye.domain.system.command.LogoutToolCommand;
import cn.pf.orch.meiye.domain.system.dto.AuthConfigDTO;
import cn.pf.orch.meiye.domain.system.dto.MyUserRoleRelDTO;
import cn.pf.orch.meiye.domain.system.dto.UserTokenDTO;
import cn.pf.orch.meiye.domain.system.po.MyCompanyPO;
import cn.pf.orch.meiye.domain.system.po.MyResourcePO;
import cn.pf.orch.meiye.domain.system.po.MyUserPO;
import cn.pf.orch.meiye.domain.system.po.MyUserRoleRelPO;
import cn.pf.orch.meiye.domain.system.query.SsoUserLoginQuery;
import cn.pf.orch.meiye.domain.system.query.SsoUserTokenQuery;
import cn.pf.orch.meiye.enums.AuthTypeEnum;
import cn.pf.orch.meiye.enums.ClientTypeEnum;
import cn.pf.orch.meiye.enums.RoleTypeEnum;
import cn.pf.orch.meiye.exception.AuthException;
import cn.pf.orch.meiye.exception.MessageCode;
import cn.pf.orch.meiye.mapper.*;
import cn.pf.orch.meiye.properties.MeiyeSeverProperties;
import cn.pf.orch.meiye.utils.AsyncTaskExecutor;
import com.lark.oapi.service.authen.v1.model.GetUserInfoRespBody;
import com.lark.oapi.service.contact.v3.model.BatchGetIdUserRespBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SsoService {


    @Resource
    private FeishuAppClient feishuAppClient;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private MeiyeSeverProperties meiyeSeverProperties;
    @Resource
    private UserInfoAssembler userInfoAssembler;
    @Resource
    private MyUserMapper myUserMapper;
    @Resource
    private MyUserRoleRelMapper myUserRoleRelMapper;
    @Resource
    private MyRoleMapper myRoleMapper;
    @Resource
    private MyCompanyMapper myCompanyMapper;
    @Resource
    private ResourceAssembler assembler;
    @Resource
    private MyResourceMapper myResourceMapper;
    ;

    /**
     * 免登过程-获取token
     */
    public UserTokenDTO getUserToken(SsoUserLoginQuery query) {
        AuthUserAccessTokenModel authUserAccessTokenModel = feishuAppClient.getAuthService().authUserAccessToken(query.getCode(), query.getRedirectUri());
        String uuid = IdUtil.fastSimpleUUID();
        String token = Base64.getUrlEncoder().withoutPadding().encodeToString(uuid.getBytes());
        long timeOut = meiyeSeverProperties.getLogin().getTimeOut();
        log.info("getUserToken token: {}", timeOut);
        generateFsUserInfo(token, authUserAccessTokenModel.getUserAccessToken(), query.getClientType(), timeOut);
        return UserTokenDTO.builder()
                .token(token)
                .timeOut(meiyeSeverProperties.getLogin().getTimeOut())
                .build();
    }

    public UserInfoDTO getUserInfo(String token) {
        UserInfoDTO userInfoDTO = this.getCacheUserInfo(token);
        String userAccessToken = userInfoDTO.getUserAccessToken();
        AuthUserAccessTokenModel userAccessTokenModel = feishuAppClient.getAuthService().getUserAccessToken(userAccessToken);
        if (!userInfoDTO.getUserAccessToken().equals(userAccessTokenModel.getUserAccessToken())) {
            Long expire = stringRedisTemplate.getExpire(CacheConstants.getAuthToken(token));
            long timeOut = meiyeSeverProperties.getLogin().getTimeOut();
            return this.generateFsUserInfo(token, userAccessTokenModel.getUserAccessToken(), userInfoDTO.getClientType(), Optional.ofNullable(expire).orElse(timeOut));
        } else {
            log.info("token:{}无须刷新", token);
        }
        return userInfoDTO;
    }

    public UserInfoDTO getCacheUserInfo(String token) {
        String userInfo = stringRedisTemplate.opsForValue().get(CacheConstants.getAuthToken(token));
        if (StrUtil.isBlank(userInfo)) {
            throw new AuthException(MessageCode.AUTH_FAIL);
        }
        return JsonUtils.parse(userInfo, UserInfoDTO.class);
    }

    private UserInfoDTO generateFsUserInfo(String token, String userToken, ClientTypeEnum clientType, long timeOut) {
        GetUserInfoRespBody respBody = feishuAppClient.getAuthService().getUserInfo(userToken);
        // 登录校验
        checkUserLogin(respBody);
        UserInfoDTO userInfoDTO = userInfoAssembler.resp2DTO(respBody);
        userInfoDTO.setToken(token);
        userInfoDTO.setClientType(clientType);
        userInfoDTO.setLastRefreshTime(LocalDateTime.now());
        userInfoDTO.setUserAccessToken(userToken);
        // 添加上下文,用于完善用户日志和补充数据字段
        if (BaseRequestContext.get() != null) {
            UserOperationContext.put("username", respBody.getName());
            UserOperationContext.put("fsOpenId", respBody.getOpenId());
            BaseRequestContext.putAttachment(Constants.OPEN_ID, respBody.getOpenId());
            BaseRequestContext.putAttachment(Constants.USER_NAME, respBody.getName());
        }
        // 超管额外处理
        List<String> adminTelephones = meiyeSeverProperties.getLogin().getAdminTelephones();
        if (!adminTelephones.contains(respBody.getMobile())) {
            // 角色
            List<MyUserRoleRelDTO> roleDTOs = myRoleMapper.selectByUserIds(Collections.singletonList(respBody.getOpenId()));
            userInfoDTO.setRole(roleDTOs);
            // 公司
            MyCompanyPO po = myCompanyMapper.selectByUserId(respBody.getOpenId());
            userInfoDTO.setAuthType(po.getAuthType());
            userInfoDTO.setCompanyId(po.getId());
            userInfoDTO.setCompanyName(po.getName());
        } else {
            MyUserRoleRelDTO roleDTO = MyUserRoleRelDTO.builder()
                    .roleId(1L)
                    .roleName("admin")
                    .roleType(RoleTypeEnum.DEFAULT)
                    .userId(respBody.getOpenId())
                    .build();
            userInfoDTO.setRole(Collections.singletonList(roleDTO));
            // 获取集团
            List<MyCompanyPO> pos = myCompanyMapper.selectByNewAuthType(AuthTypeEnum.GROUP);
            userInfoDTO.setAuthType(AuthTypeEnum.GROUP);
            userInfoDTO.setCompanyId(pos.get(0).getId());
            userInfoDTO.setCompanyName(pos.get(0).getName());
            userInfoDTO.setCompanyDepartmentId(pos.get(0).getDepartmentIds().split(",")[0]);
        }
        String userInfoJson = JsonUtils.toJson(userInfoDTO);
        stringRedisTemplate.opsForValue().set(CacheConstants.getAuthToken(token), userInfoJson, timeOut, TimeUnit.SECONDS);
        stringRedisTemplate.opsForValue().set(CacheConstants.getFeishuOpenId(clientType.getCode(), userInfoDTO.getOpenId()), userInfoJson, timeOut, TimeUnit.SECONDS);
        return userInfoDTO;
    }

    public Boolean refreshToken(SsoUserTokenQuery query) {
        UserInfoDTO cacheUserInfo = this.getCacheUserInfo(query.getToken());
        long timeOut = meiyeSeverProperties.getLogin().getTimeOut();
        stringRedisTemplate.expire(CacheConstants.getAuthToken(query.getToken()), timeOut, TimeUnit.SECONDS);
        stringRedisTemplate.expire(CacheConstants.getFeishuOpenId(cacheUserInfo.getClientType().getCode(), cacheUserInfo.getOpenId()), timeOut, TimeUnit.SECONDS);
        return true;
    }


    public Boolean logout(String token) {
        stringRedisTemplate.delete(CacheConstants.getAuthToken(token));
        return true;
    }

    public Boolean logoutTool(LogoutToolCommand command) {
        if (CollUtil.isNotEmpty(command.getOpenIdList())) {
            logoutByOpenId(command.getOpenIdList());
        }
        // if (StrUtil.isNotBlank(command.getName())) {
        //     ContactSearchUserDTO dto = ContactSearchUserDTO.builder().query(command.getName()).build();
        //     ContactSearchUserResponse resp = feishuAppClient.getContactService().searchUser(dto);
        //     if (ObjUtil.isNotNull(resp) && !resp.getUsers().isEmpty()) {
        //         List<String> openIds = resp.getUsers().stream().map(UserDTO::getOpenId).distinct().collect(Collectors.toList());
        //         log.info("按名称登出用户,name:{},openIds:{}", command.getName(), JsonUtils.toJson(openIds));
        //         logoutByOpenId(openIds);
        //     }
        // }
        if (StrUtil.isNotBlank(command.getTelephone())) {
            BatchGetIdUserRespBody body = feishuAppClient.getContactService().getOpenId(command.getTelephone());
            if (ObjUtil.isNotNull(body) && body.getUserList().length > 0) {
                String openId = Arrays.asList(body.getUserList()).get(0).getUserId();
                this.logoutByOpenId(Collections.singletonList(openId));
            }
        }
        return true;
    }

    public void logoutByOpenId(List<String> openIds) {
        if (CollUtil.isEmpty(openIds)) {
            return;
        }
        CompletableFuture.runAsync(() -> {
                    List<String> keys = scanKeys(CacheConstants.getAuthToken("*"));
                    for (String key : keys) {
                        String token = key.substring(key.lastIndexOf(":") + 1);
                        String authToken = CacheConstants.getAuthToken(token);
                        if (StrUtil.isNotBlank(authToken)) {
                            String userInfo = stringRedisTemplate.opsForValue().get(authToken);
                            UserInfoDTO parse = JsonUtils.parse(userInfo, UserInfoDTO.class);
                            if (openIds.contains(parse.getOpenId())) {
                                stringRedisTemplate.delete(authToken);
                            }
                        }
                    }
                }, AsyncTaskExecutor.resourceExecutor)
                .exceptionally((ex -> {
                    log.error("登出用户异常: ", ex);
                    return null;
                }));
    }

    private List<String> scanKeys(String pattern) {
        List<String> keys = new ArrayList<>();
        ScanOptions options = ScanOptions.scanOptions().match(pattern).build();
        stringRedisTemplate.execute((RedisCallback<Void>) connection -> {
            try (Cursor<byte[]> cursor = connection.scan(options)) {
                while (cursor.hasNext()) {
                    keys.add(new String(cursor.next()));
                }
            }
            return null;
        });
        return keys;
    }

    public UserInfoDTO getUserToken(String openId) {
        for (ClientTypeEnum value : ClientTypeEnum.values()) {
            String key = CacheConstants.getFeishuOpenId(value.getCode(), openId);
            String userInfo = stringRedisTemplate.opsForValue().get(key);
            if (StrUtil.isNotBlank(userInfo)) {
                UserInfoDTO userInfoDTO = JsonUtils.parse(userInfo, UserInfoDTO.class);
                String authToken = CacheConstants.getAuthToken(userInfoDTO.getToken());
                String userInfoStr = stringRedisTemplate.opsForValue().get(authToken);
                if (ObjUtil.isNotNull(userInfoStr)) {
                    return userInfoDTO;
                }
            }
        }
        return null;
    }

    private void checkUserLogin(GetUserInfoRespBody fsUser) {
        List<String> adminTelephones = meiyeSeverProperties.getLogin().getAdminTelephones();
        if (adminTelephones.contains(fsUser.getMobile())) {
            return;
        }
        MyUserPO myUserPO = myUserMapper.selectById(fsUser.getOpenId());
        if (ObjUtil.isEmpty(myUserPO)) {
            log.error("用户未同步:{}", JsonUtils.toJson(fsUser));
            throw new AuthException(MessageCode.LOGIN_NO_AUTH_ERROR);
        }
        List<MyUserRoleRelPO> relPO = myUserRoleRelMapper.selectByUserId(fsUser.getOpenId());
        if (CollUtil.isEmpty(relPO)) {
            log.error("用户无角色:{}", JsonUtils.toJson(fsUser));
            throw new AuthException(MessageCode.LOGIN_NO_AUTH_ERROR);
        }
    }


    public AuthConfigDTO getAuthConfig(String openId) {
        // todo:小程序权限相关字段
        return null;
    }

    public void getResource(UserInfoDTO userInfo) {
        List<MyResourcePO> resourcePOS = new ArrayList<>();
        // 超管有所有权限
        List<String> adminTelephones = meiyeSeverProperties.getLogin().getAdminTelephones();
        if (adminTelephones.contains(userInfo.getMobile())) {
            resourcePOS = myResourceMapper.selectList(null);
        } else {
            resourcePOS = myResourceMapper.selectByOpenId(userInfo.getOpenId());
        }
        if (CollUtil.isNotEmpty(resourcePOS)) {
            userInfo.setResourceTree(assembler.buildTree(resourcePOS));
            userInfo.setPermissions(resourcePOS.stream().map(MyResourcePO::getPath).collect(Collectors.toSet()));
        }
    }
}
