package cn.pf.orch.meiye.domain.system.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class MyUserSaveCommand {

    @ApiModelProperty(value = "煤矿id")
    @NotNull(message="煤矿不能为空")
    private Long companyId;

    @ApiModelProperty(value = "用户名称")
    @NotBlank(message="用户名称不能为空")
    private String name;

    @ApiModelProperty(value = "手机号")
    @NotBlank(message="手机号不能为空")
    private String mobile;
}
