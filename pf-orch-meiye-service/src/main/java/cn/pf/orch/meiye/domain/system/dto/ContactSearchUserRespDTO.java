package cn.pf.orch.meiye.domain.system.dto;

import lombok.Data;

import java.util.List;


@Data
public class ContactSearchUserRespDTO {

    private Boolean hasMore;

    private String pageToken;

    private List<User> users;

    @Data
    public static class User{
        private String unionId;
        private String userId;
        private String openId;
        private String name;
        private String avatarKey;
        private String avatarUrl;
    }

    // @Data
    // public static class AvatarInfo{
    //     private String avatar72;
    //     private String avatar240;
    //     private String avatar640;
    //     private String avatarOrigin;
    // }
}
