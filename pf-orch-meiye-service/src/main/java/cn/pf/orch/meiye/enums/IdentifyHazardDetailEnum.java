package cn.pf.orch.meiye.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum IdentifyHazardDetailEnum {

    GROUP("group","集团公司检查"),
    REGION("region","区域分公司检查"),
    COLLIERY("colliery","本公司分级检查"),
    OTHER("other","其他"),
    ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;


    private static final Map<String, IdentifyHazardDetailEnum> VALUES = new HashMap<>();
    static {
        for (final IdentifyHazardDetailEnum item : IdentifyHazardDetailEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static IdentifyHazardDetailEnum of(String code) {
        return VALUES.get(code);
    }
}
