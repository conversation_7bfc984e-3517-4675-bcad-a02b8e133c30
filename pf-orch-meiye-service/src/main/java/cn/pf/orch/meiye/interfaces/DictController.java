package cn.pf.orch.meiye.interfaces;

import cn.pf.orch.meiye.domain.system.dto.DictDTO;
import cn.pf.orch.meiye.domain.system.query.DictDetailQuery;
import cn.pf.orch.meiye.domain.system.query.DictListQuery;
import cn.pf.orch.meiye.service.system.DictService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Api(tags = "数据字典")
@RestController
@RequestMapping("/dict")
public class DictController {

    @Resource
    private DictService dictService;

    @PostMapping(value = "/queryDataDictList")
    @ApiOperation(value = "通过数据字典类型获取字典")
    public Map<String, List<DictDTO>> queryDataDictList(@RequestBody @Validated DictListQuery dictListQuery) {
        return dictService.queryDataDictList(dictListQuery.getDictTypes());
    }

    @PostMapping(value = "/queryDataDictDetail")
    @ApiOperation(value = "通过数据字典id获取字典信息")
    public DictDTO queryDataDictDetail(@RequestBody @Validated DictDetailQuery dictDetailQuery) {
        return dictService.queryDataDictDetail(dictDetailQuery.getId());
    }
}

