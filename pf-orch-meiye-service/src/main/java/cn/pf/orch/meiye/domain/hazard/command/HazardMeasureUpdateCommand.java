package cn.pf.orch.meiye.domain.hazard.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class HazardMeasureUpdateCommand {

    @ApiModelProperty(value = "措施")
    @NotNull(message = "措施不能为空")
    private Long id;

    @ApiModelProperty(value = "措施内容")
    @NotBlank(message = "措施内容不能为空")
    private String content;
}
