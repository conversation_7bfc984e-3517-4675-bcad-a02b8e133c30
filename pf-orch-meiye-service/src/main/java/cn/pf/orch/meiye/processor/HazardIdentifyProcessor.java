package cn.pf.orch.meiye.processor;

import cn.genn.core.exception.BusinessException;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.pf.orch.meiye.domain.hazard.command.MyHazardIdentifySaveCommand;
import cn.pf.orch.meiye.domain.hazard.command.MyHazardIdentifyUpdateCommand;
import cn.pf.orch.meiye.domain.hazard.po.HazardIdentifyPO;
import cn.pf.orch.meiye.domain.hazard.po.HazardInfoPO;
import cn.pf.orch.meiye.exception.MessageCode;
import cn.pf.orch.meiye.mapper.HazardIdentifyMapper;
import cn.pf.orch.meiye.mapper.HazardInfoMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class HazardIdentifyProcessor {

    @Resource
    private HazardIdentifyMapper mapper;
    @Resource
    private HazardInfoMapper hazardInfoMapper;

    public void saveCheck(MyHazardIdentifySaveCommand command){
        HazardIdentifyPO po = mapper.selectByName(command.getName());
        if(ObjUtil.isNotEmpty(po)){
            throw new BusinessException(MessageCode.HAZARD_IDENTIFY_NAME_EXIST);
        }
    }

    public void changeCheck(MyHazardIdentifyUpdateCommand command){
        HazardIdentifyPO po = mapper.selectByName(command.getName());
        if(ObjUtil.isNotEmpty(po) && !po.getId().equals(command.getId())){
            throw new BusinessException(MessageCode.HAZARD_IDENTIFY_NAME_EXIST);
        }
    }

    public void deleteCheck(List<Long> ids){
        List<HazardInfoPO> pos = hazardInfoMapper.selectByIdentifyIds(ids);
        if(CollUtil.isNotEmpty(pos)){
            throw new BusinessException(MessageCode.HAZARD_EXIST_ERROR);
        }
    }
}
