package cn.pf.orch.meiye.service.safetyviolation.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.pf.orch.meiye.assembler.SafetyViolationAssembler;
import cn.pf.orch.meiye.domain.location.po.MeiyeLocation;
import cn.pf.orch.meiye.domain.safetyviolation.command.AddCommand;
import cn.pf.orch.meiye.domain.safetyviolation.command.UpdateCommand;
import cn.pf.orch.meiye.domain.safetyviolation.dto.MeiyeSafetyViolationDTO;
import cn.pf.orch.meiye.domain.safetyviolation.po.MeiyeSafetyViolation;
import cn.pf.orch.meiye.domain.safetyviolation.query.PageQuery;
import cn.pf.orch.meiye.enums.DelEnum;
import cn.pf.orch.meiye.mapper.MeiyeSafetyViolationMapper;
import cn.pf.orch.meiye.service.location.MeiyeLocationService;
import cn.pf.orch.meiye.service.safetyviolation.MeiyeSafetyViolationService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * 三违记录表(MeiyeSafetyViolation)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-14 10:27:37
 */
@Service
public class MeiyeSafetyViolationServiceImpl extends ServiceImpl<MeiyeSafetyViolationMapper, MeiyeSafetyViolation> implements MeiyeSafetyViolationService {

    @Resource
    private MeiyeSafetyViolationMapper mapper;
    @Resource
    private SafetyViolationAssembler assembler;
    @Resource
    private MeiyeLocationService locationService;

    @Override
    public void add(AddCommand command) {
        MeiyeSafetyViolation meiyeSafetyViolation = assembler.addCommandToPO(command);
        mapper.insert(meiyeSafetyViolation);
    }

    @Override
    public void update(UpdateCommand command) {
        MeiyeSafetyViolation meiyeSafetyViolation = assembler.updateCommandToPO(command);
        mapper.updateById(meiyeSafetyViolation);
    }

    @Override
    public void delete(Long id) {
        LambdaUpdateWrapper<MeiyeSafetyViolation> wrapper = Wrappers.lambdaUpdate(MeiyeSafetyViolation.class)
                .eq(MeiyeSafetyViolation::getId, id)
                .set(MeiyeSafetyViolation::getDel, DelEnum.DELETE);
        mapper.update(wrapper);
    }

    @Override
    public Page<MeiyeSafetyViolationDTO> page(PageQuery query) {
        LambdaQueryWrapper<MeiyeSafetyViolation> wrapper = Wrappers.lambdaQuery(MeiyeSafetyViolation.class)
                .eq(ObjectUtil.isNotEmpty(query.getCompanyId()), MeiyeSafetyViolation::getCompanyId, query.getCompanyId())
                .like(ObjectUtil.isNotEmpty(query.getViolationDetails()), MeiyeSafetyViolation::getViolationDetails, query.getViolationDetails())
                .eq(ObjectUtil.isNotEmpty(query.getViolationType()),MeiyeSafetyViolation::getViolationType, query.getViolationType())
                .like(ObjectUtil.isNotEmpty(query.getPersonName()),MeiyeSafetyViolation::getPersonName, query.getPersonName())
                .like(ObjectUtil.isNotEmpty(query.getDiscoverer()),MeiyeSafetyViolation::getDiscoverer, query.getDiscoverer())
                .eq(ObjectUtil.isNotEmpty(query.getViolationLocationId()),MeiyeSafetyViolation::getViolationLocationId, query.getViolationLocationId())
                .ge(ObjectUtil.isNotEmpty(query.getViolationDateStart()),MeiyeSafetyViolation::getViolationDate, query.getViolationDateStart())
                .le(ObjectUtil.isNotEmpty(query.getViolationDateEnd()),MeiyeSafetyViolation::getViolationDate, query.getViolationDateEnd())
                .eq(MeiyeSafetyViolation::getDel, DelEnum.NO_DELETE.getCode())
                .in(ObjectUtil.isNotEmpty(query.getRangeCompanyIds()), MeiyeSafetyViolation::getCompanyId, query.getRangeCompanyIds())
                .orderByDesc(MeiyeSafetyViolation::getCreateTime);
        Page<MeiyeSafetyViolation> poPage = mapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), wrapper);
        List<MeiyeSafetyViolationDTO> records = Lists.newArrayList();
        Optional.ofNullable(poPage.getRecords()).ifPresent(list -> {
            list.forEach( po -> {
                MeiyeSafetyViolationDTO dto = assembler.poToDto(po);
                MeiyeLocation location = locationService.selectLocationById(dto.getViolationLocationId());
                dto.setViolationLocationName(location.getName());
                dto.setLocationFullPathName(location.getFullPathName());
                records.add(dto);
            });
        });
        Page<MeiyeSafetyViolationDTO> page = new Page<>(poPage.getCurrent(), poPage.getSize(), poPage.getTotal());
        page.setRecords(records);
        return page;
    }

    @Override
    public MeiyeSafetyViolationDTO detail(Long id) {
        MeiyeSafetyViolation meiyeSafetyViolation = mapper.selectById(id);
        MeiyeSafetyViolationDTO dto = assembler.poToDto(meiyeSafetyViolation);
        return Optional.ofNullable(dto)
                .map(dto1 -> {
                    MeiyeLocation location = locationService.selectLocationById(dto.getViolationLocationId());
                    dto.setViolationLocationName(location.getName());
                    dto.setLocationFullPathName(location.getFullPathName());
                    return dto1;
                }) .orElseThrow(() -> new RuntimeException("未查询到该记录的详细信息"));
    }
}

