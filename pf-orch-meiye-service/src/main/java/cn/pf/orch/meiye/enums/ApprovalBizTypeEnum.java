package cn.pf.orch.meiye.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum ApprovalBizTypeEnum {

    RISK("risk","安全风险"),
    HAZARD("hazard","事故隐患"),
    ;
    @EnumValue
    @JsonValue
    private final String code;

    private final String description;


    private static final Map<String, ApprovalBizTypeEnum> VALUES = new HashMap<>();

    static {
        for (final ApprovalBizTypeEnum item : ApprovalBizTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static ApprovalBizTypeEnum of(String code) {
        return VALUES.get(code);
    }
}
