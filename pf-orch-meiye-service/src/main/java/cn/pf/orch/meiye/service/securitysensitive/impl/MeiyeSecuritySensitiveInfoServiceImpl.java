package cn.pf.orch.meiye.service.securitysensitive.impl;

import cn.genn.core.exception.BusinessException;
import cn.hutool.core.util.ObjectUtil;
import cn.pf.orch.meiye.assembler.MeiyeSecuritySensitiveInfoAssmbler;
import cn.pf.orch.meiye.domain.feishu.SendSecurityDTO;
import cn.pf.orch.meiye.domain.risk.command.SendNotifyCommand;
import cn.pf.orch.meiye.domain.securitysensitive.command.AddCommand;
import cn.pf.orch.meiye.domain.securitysensitive.command.UpdateCommand;
import cn.pf.orch.meiye.domain.securitysensitive.dto.MeiyeSecuritySensitiveInfoDTO;
import cn.pf.orch.meiye.domain.securitysensitive.po.MeiyeSecuritySensitiveInfo;
import cn.pf.orch.meiye.domain.securitysensitive.query.PageQuery;
import cn.pf.orch.meiye.enums.DelEnum;
import cn.pf.orch.meiye.exception.MessageCode;
import cn.pf.orch.meiye.mapper.MeiyeSecuritySensitiveInfoMapper;
import cn.pf.orch.meiye.service.CardSendActionService;
import cn.pf.orch.meiye.service.securitysensitive.MeiyeSecuritySensitiveInfoService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 安全敏感信息表(MeiyeSecuritySensitiveInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-13 15:42:23
 */
@Service
public class MeiyeSecuritySensitiveInfoServiceImpl extends ServiceImpl<MeiyeSecuritySensitiveInfoMapper, MeiyeSecuritySensitiveInfo> implements MeiyeSecuritySensitiveInfoService {

    @Resource
    private MeiyeSecuritySensitiveInfoAssmbler assmbler;

    @Resource
    private MeiyeSecuritySensitiveInfoMapper mapper;

    @Resource
    private CardSendActionService cardSendActionService;


    public MeiyeSecuritySensitiveInfoDTO getById(Integer id) {
        MeiyeSecuritySensitiveInfo meiyeSecuritySensitiveInfo = mapper.selectById(id);
        return assmbler.poToDto(meiyeSecuritySensitiveInfo);
    }

    @Override
    public void add(AddCommand command) {
        MeiyeSecuritySensitiveInfo meiyeSecuritySensitiveInfo = assmbler.addCommandToPo(command);
        mapper.insert(meiyeSecuritySensitiveInfo);
    }

    @Override
    public void delete(Integer id) {
        LambdaUpdateWrapper<MeiyeSecuritySensitiveInfo> wrapper = Wrappers.lambdaUpdate(MeiyeSecuritySensitiveInfo.class)
                .eq(MeiyeSecuritySensitiveInfo::getId, id)
                .set(MeiyeSecuritySensitiveInfo::getDel, DelEnum.DELETE);
        mapper.update(wrapper);
    }

    @Override
    public void update(UpdateCommand command) {
        MeiyeSecuritySensitiveInfo entity = assmbler.updateCommandToPo(command);
        mapper.updateById(entity);
    }

    @Override
    public Page<MeiyeSecuritySensitiveInfoDTO> pageQuery(PageQuery query) {
        Page<MeiyeSecuritySensitiveInfo> page = new Page<>(query.getPageNo(), query.getPageSize());
        LambdaQueryWrapper<MeiyeSecuritySensitiveInfo> wrapper = Wrappers.lambdaQuery(MeiyeSecuritySensitiveInfo.class)
                .eq(ObjectUtil.isNotEmpty(query.getCompanyId()), MeiyeSecuritySensitiveInfo::getCompanyId, query.getCompanyId())
                .eq(ObjectUtil.isNotEmpty(query.getSensitiveCategory()),MeiyeSecuritySensitiveInfo::getSensitiveCategory, query.getSensitiveCategory())
                .like(ObjectUtil.isNotEmpty(query.getSensitiveDescription()),MeiyeSecuritySensitiveInfo::getSensitiveDescription, query.getSensitiveDescription())
                .like(ObjectUtil.isNotEmpty(query.getResponsiblePerson()),MeiyeSecuritySensitiveInfo::getResponsiblePerson, query.getResponsiblePerson())
                .like(ObjectUtil.isNotEmpty(query.getResponsibleDepartment()),MeiyeSecuritySensitiveInfo::getResponsibleDepartment, query.getResponsibleDepartment())
                .ge(ObjectUtil.isNotEmpty(query.getStartDate()),MeiyeSecuritySensitiveInfo::getCreateTime, query.getStartDate())
                .le(ObjectUtil.isNotEmpty(query.getEndDate()),MeiyeSecuritySensitiveInfo::getCreateTime, query.getEndDate())
                .eq(MeiyeSecuritySensitiveInfo::getDel, DelEnum.NO_DELETE.getCode())
                .in(ObjectUtil.isNotEmpty(query.getRangeCompanyIds()),MeiyeSecuritySensitiveInfo::getCompanyId, query.getRangeCompanyIds())
                .orderByDesc(MeiyeSecuritySensitiveInfo::getCreateTime);
        Page<MeiyeSecuritySensitiveInfo> POPage = mapper.selectPage(page, wrapper);
        List<MeiyeSecuritySensitiveInfoDTO> meiyeSecuritySensitiveInfoDTOS =
                Optional.ofNullable(POPage.getRecords()).map(records -> assmbler.poToDtos(records)).orElse(Collections.emptyList());
        Page<MeiyeSecuritySensitiveInfoDTO> pageDTO = new Page<>(POPage.getCurrent(), POPage.getSize(), POPage.getTotal());
        pageDTO.setRecords(meiyeSecuritySensitiveInfoDTOS);
        return pageDTO;
    }


    @Override
    public MeiyeSecuritySensitiveInfoDTO queryDetail(Integer id) {
        return Optional.ofNullable(mapper.selectById(id))
                .map(info -> assmbler.poToDto(info))
                .orElse(null);

    }


    @Override
    public Boolean sendNotify(SendNotifyCommand command) {
        MeiyeSecuritySensitiveInfo meiyeSecuritySensitiveInfo = this.getById(command.getId());
        Optional.ofNullable(meiyeSecuritySensitiveInfo).orElseThrow(() -> new BusinessException(MessageCode.SECURITY_NO_EXIST) );
        // 校验是否被删除
        if (meiyeSecuritySensitiveInfo.getDel().equals(DelEnum.DELETE)) {
            throw new BusinessException(MessageCode.SECURITY_DEL);
        }
        // 构建卡片对象
        SendSecurityDTO sendSecurityDTO = SendSecurityDTO.builder()
                .companyName(meiyeSecuritySensitiveInfo.getCompanyName())
                .sensitiveCategory(meiyeSecuritySensitiveInfo.getSensitiveCategory().getDescription())
                .sensitiveDescription(meiyeSecuritySensitiveInfo.getSensitiveDescription())
                .controlMeasures(meiyeSecuritySensitiveInfo.getControlMeasures())
                .responsibleDepartment(meiyeSecuritySensitiveInfo.getResponsibleDepartment())
                .responsiblePerson(meiyeSecuritySensitiveInfo.getResponsiblePerson())
                .responsiblePersonNumber(meiyeSecuritySensitiveInfo.getResponsiblePersonNumber())
                .controlResult(meiyeSecuritySensitiveInfo.getControlResult())
                .openIds(command.getOpenIds())
                .build();
        cardSendActionService.sendSecurityCode(sendSecurityDTO);
        return Boolean.TRUE;
    }
}

