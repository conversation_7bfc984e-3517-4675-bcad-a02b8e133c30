package cn.pf.orch.meiye.domain.system.command;

import cn.pf.orch.meiye.enums.ApprovalSignEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class MyUserApprovalSignCommand {

    @ApiModelProperty(value = "用户id")
    @NotBlank(message="用户id不能为空")
    private String userId;

    @ApiModelProperty(value = "审批标签")
    private ApprovalSignEnum approvalSign;
}
