package cn.pf.orch.meiye.mapper;

import cn.pf.orch.meiye.domain.risk.dto.RiskCheckDTO;
import cn.pf.orch.meiye.domain.risk.dto.RiskCheckPageDTO;
import cn.pf.orch.meiye.domain.risk.po.RiskCheckPO;
import cn.pf.orch.meiye.domain.risk.query.RiskCheckPageQuery;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface RiskCheckMapper extends BaseMapper<RiskCheckPO> {

    IPage<RiskCheckPageDTO> selectByPage(IPage<RiskCheckPO> page, @Param("query") RiskCheckPageQuery query);

    RiskCheckDTO selectDetailById(Long id);
}
