package cn.pf.orch.meiye.assembler;

import cn.genn.core.model.page.PageResultDTO;
import cn.pf.orch.meiye.domain.hazard.command.MyHazardInfoSaveCommand;
import cn.pf.orch.meiye.domain.hazard.command.MyHazardInfoUpdateCommand;
import cn.pf.orch.meiye.domain.hazard.dto.HazardInfoPageDTO;
import cn.pf.orch.meiye.domain.hazard.po.HazardInfoPO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface HazardInfoAssembler {

    HazardInfoPO saveCommand2PO(MyHazardInfoSaveCommand command);

    HazardInfoPO updateCommand2PO(MyHazardInfoUpdateCommand command);

    @Mapping(target = "pageNo", source = "current")
    @Mapping(target = "pageSize", source = "size")
    @Mapping(target = "list", source = "records")
    @Mapping(target = "totalPages", source = "pages")
    PageResultDTO<HazardInfoPageDTO> toPage(IPage<HazardInfoPageDTO> poPage);
}
