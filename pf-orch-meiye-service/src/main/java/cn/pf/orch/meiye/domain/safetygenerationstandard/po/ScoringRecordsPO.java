package cn.pf.orch.meiye.domain.safetygenerationstandard.po;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import cn.pf.orch.meiye.enums.DelEnum;
import cn.pf.orch.meiye.enums.MineTypeeEnum;
import cn.pf.orch.meiye.enums.ScoringLevelEnum;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
/**
 * 安全生产煤矿评分记录表(ScoringRecords)表实体类
 *
 * <AUTHOR>
 * @since 2025-04-22 10:29:37
 */
@Data
@TableName("scoring_records")
public class ScoringRecordsPO extends Model<ScoringRecordsPO> {
    //主键ID
    private Long id;
    //煤矿id
    private Long companyId;

    private String companyName;
    //煤矿类型（井工矿/露天矿）
    private MineTypeeEnum mineType = MineTypeeEnum.UNDERGROUND_MINE;
    //评分级别
    private ScoringLevelEnum scoreLevel;
    //评分日期
    private LocalDate scoreDate;
    //总分(0-100)
    private BigDecimal totalScore;
    //总评分进度(0-100%)
    private BigDecimal progress;
    //创建人id
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;
    //创建人
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;
    //创建时间
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    //更新人id
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;
    //更新人
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;
    //更新时间
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    private DelEnum del;

}

