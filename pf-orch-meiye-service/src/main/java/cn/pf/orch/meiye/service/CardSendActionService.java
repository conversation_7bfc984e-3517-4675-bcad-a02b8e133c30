package cn.pf.orch.meiye.service;

import cn.pf.orch.feishu.FeishuAppClient;
import cn.pf.orch.meiye.domain.feishu.SendHazardDTO;
import cn.pf.orch.meiye.domain.feishu.SendRiskDTO;
import cn.pf.orch.meiye.domain.feishu.SendSecurityDTO;
import cn.pf.orch.meiye.properties.MeiyeSeverProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class CardSendActionService {

    @Resource
    private FeishuAppClient feishuAppClient;
    @Resource
    private MeiyeSeverProperties meiyeSeverProperties;

    /**
     * 安全风险通知
     */
    public void sendSignInCode(SendRiskDTO dto) {
        Map<String, Object> templateVariable = new HashMap<>();
        templateVariable.put("companyName", dto.getCompanyName());
        templateVariable.put("code", dto.getCode());
        templateVariable.put("address", dto.getAddress());
        templateVariable.put("type", dto.getType());
        templateVariable.put("remark", dto.getRemark());
        templateVariable.put("pcUrl", dto.getPcUrl());
        templateVariable.put("appUrl", dto.getAppUrl());
        String templateId = meiyeSeverProperties.getCardSend().getTemplate().getRiskId();
        for (String openId : dto.getOpenIds()) {
            feishuAppClient.getRobotService().sendCard(openId, templateId, templateVariable);
        }
    }


    /**
     * 事故隐患通知
     */
    public void sendHazardCode(SendHazardDTO dto) {
        Map<String, Object> templateVariable = new HashMap<>();
        templateVariable.put("companyName", dto.getCompanyName());
        templateVariable.put("code", dto.getCode());
        templateVariable.put("address", dto.getAddress());
        templateVariable.put("type", dto.getType());
        templateVariable.put("remark", dto.getRemark());
        templateVariable.put("pcUrl", dto.getPcUrl());
        templateVariable.put("appUrl", dto.getAppUrl());
        templateVariable.put("departmentName", dto.getDepartmentName());
        templateVariable.put("rectifyMoney", dto.getRectifyMoney());
        templateVariable.put("rectifyOwnerName", dto.getRectifyOwnerName());
        templateVariable.put("rectifyTime", dto.getRectifyTime());
        String templateId = meiyeSeverProperties.getCardSend().getTemplate().getHazardId();
        for (String openId : dto.getOpenIds()) {
            feishuAppClient.getRobotService().sendCard(openId, templateId, templateVariable);
        }
    }


    /**
     * 安全敏感信息通知
     */
    public void sendSecurityCode(SendSecurityDTO dto) {
        Map<String, Object> templateVariable = new HashMap<>();
        templateVariable.put("companyName", dto.getCompanyName());
        templateVariable.put("sensitiveCategory", dto.getSensitiveCategory());
        templateVariable.put("sensitiveDescription", dto.getSensitiveDescription());
        templateVariable.put("controlMeasures", dto.getControlMeasures());
        templateVariable.put("responsibleDepartment", dto.getResponsibleDepartment());
        templateVariable.put("responsiblePerson", dto.getResponsiblePerson());
        templateVariable.put("responsiblePersonNumber", dto.getResponsiblePersonNumber());
        templateVariable.put("controlResult", dto.getControlResult());
        String templateId = meiyeSeverProperties.getCardSend().getTemplate().getSensitiveId();
        for (String openId : dto.getOpenIds()) {
            feishuAppClient.getRobotService().sendCard(openId, templateId, templateVariable);
        }

    }
}
