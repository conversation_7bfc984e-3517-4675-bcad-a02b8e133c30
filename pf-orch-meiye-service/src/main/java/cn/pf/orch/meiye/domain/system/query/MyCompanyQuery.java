package cn.pf.orch.meiye.domain.system.query;

import cn.pf.orch.meiye.enums.AuthTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MyCompanyQuery {

    @ApiModelProperty(value = "权限类型（集团，区域公司，煤矿）")
    private AuthTypeEnum authType;

    @ApiModelProperty(value = "公司名称")
    private String name;

    @ApiModelProperty(value = "上级部门")
    private String parentName;
}
