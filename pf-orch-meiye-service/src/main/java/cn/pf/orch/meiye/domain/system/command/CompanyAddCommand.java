package cn.pf.orch.meiye.domain.system.command;

import cn.pf.orch.meiye.enums.AuthTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class CompanyAddCommand {

    @ApiModelProperty(value = "权限类型（集团，区域公司，煤矿）")
    @NotNull(message = "公司层级不能为空")
    private AuthTypeEnum authType;

    @ApiModelProperty(value = "公司名称")
    @NotBlank(message = "公司层级不能为空")
    private String name;

    @ApiModelProperty(value = "上级公司")
    @NotNull(message = "公司层级不能为空")
    private Long pid;

    @ApiModelProperty(value = "飞书部门id映射")
    @NotEmpty(message = "关联飞书组织不能为空")
    private List<String> departmentIds;

    @ApiModelProperty(value = "许可证")
    private List<CompanyLicenseAddCommand> licenseList;

}
