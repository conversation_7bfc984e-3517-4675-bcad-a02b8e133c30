package cn.pf.orch.meiye.interfaces.risk;

import cn.genn.core.model.page.PageResultDTO;
import cn.pf.orch.meiye.domain.risk.command.MyRiskIdentifySaveCommand;
import cn.pf.orch.meiye.domain.risk.command.MyRiskIdentifyUpdateCommand;
import cn.pf.orch.meiye.domain.risk.dto.RiskIdentifyDTO;
import cn.pf.orch.meiye.domain.risk.query.RiskIdentifyQuery;
import cn.pf.orch.meiye.service.risk.RiskIdentifyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api(tags = "安全风险-风险辨别计划")
@RestController
@RequestMapping("/risk/identify")
public class RiskIdentifyController {

    @Resource
    private RiskIdentifyService riskIdentifyService;

    @PostMapping("/page")
    @ApiOperation(value = "分页查询列表")
    public PageResultDTO<RiskIdentifyDTO> page(@ApiParam(value = "查询类") @RequestBody RiskIdentifyQuery query) {
        return riskIdentifyService.page(query);
    }

    @PostMapping("/get")
    @ApiOperation(value = "根据id查询")
    public RiskIdentifyDTO get(@ApiParam(value = "查询类") @RequestParam Long id) {
        return riskIdentifyService.get(id);
    }

    @PostMapping("/save")
    @ApiOperation(value = "添加")
    public Boolean save(@RequestBody @Validated MyRiskIdentifySaveCommand command) {
        return riskIdentifyService.save(command);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public Boolean change(@RequestBody @Validated MyRiskIdentifyUpdateCommand command) {
        return riskIdentifyService.change(command);
    }

    @PostMapping("/batch/delete")
    @ApiOperation(value = "批量删除")
    public Boolean batchRemove(@RequestBody List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return true;
        }
        return riskIdentifyService.batchRemove(idList);
    }

    @PostMapping("/list")
    @ApiOperation(value = "获取权限范围内未到期辨识计划")
    public List<RiskIdentifyDTO> list() {
        return riskIdentifyService.list();
    }
}
