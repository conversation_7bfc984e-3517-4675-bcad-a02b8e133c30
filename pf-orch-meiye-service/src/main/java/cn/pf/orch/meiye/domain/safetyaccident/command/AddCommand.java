package cn.pf.orch.meiye.domain.safetyaccident.command;

import cn.pf.orch.meiye.enums.AccidentCategoryEnum;
import cn.pf.orch.meiye.enums.AccidentNature;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ApiModel(value ="AddCommand", description = "新增事故台账")
public class AddCommand {

    @ApiModelProperty(value = "煤矿ID", example = "1001", required = true)
    @NotNull
    private Long companyId;

    @ApiModelProperty(value = "煤矿名称", example = "山西大同煤矿", required = true)
    @NotEmpty
    private String companyName;

    @ApiModelProperty(value = "事故性质", required = true)
    @NotNull
    private AccidentNature accidentNature;

    @ApiModelProperty(value = "事故类别", required = true)
    @NotNull
    private AccidentCategoryEnum accidentCategory;

    @ApiModelProperty(value = "事故名称", example = "井下瓦斯爆炸", required = true)
    @NotEmpty
    private String accidentName;

    @ApiModelProperty(value = "发生时间", example = "2023-05-15 14:30:00", required = true)
    @NotNull
    private LocalDateTime occurrenceTime;

    @ApiModelProperty(value = "上报时间", example = "2023-05-15 15:00:00", required = true)
    @NotNull
    private LocalDateTime reportTime;

//    @ApiModelProperty(value = "时间描述", example = "事故发生在早班交接期间")
//    @NotEmpty
//    private String timeDescription;

    @ApiModelProperty(value = "事故地点ID", example = "location-001", required = true)
    @NotNull
    private Long locationId;

    @ApiModelProperty(value = "事故责任人", example = "张三")
    @NotEmpty
    private String responsiblePerson;

    @ApiModelProperty(value = "事故责任人工号", example = "EMP1001")
//    @NotEmpty
    private String responsiblePersonNumber;

    @ApiModelProperty(value = "责任人openid", example = "wx1234567890abcdef")
    @NotEmpty
    private String responsiblePersonOpenId;

    @ApiModelProperty(value = "死亡人数", example = "2")
    @NotNull
    private Integer deathToll;

    @ApiModelProperty(value = "重伤人数", example = "3")
    @NotNull
    private Integer seriousInjury;

    @ApiModelProperty(value = "轻伤人数", example = "5")
    @NotNull
    private Integer minorInjury;

    @ApiModelProperty(value = "经济损失(元)", example = "500000.00")
    @NotNull
    private BigDecimal economicLoss;

    @ApiModelProperty(value = "当前登录人id")
    private String createUserId;

    @ApiModelProperty(value = "当前登录人名称")
    private String createUserName;
}
