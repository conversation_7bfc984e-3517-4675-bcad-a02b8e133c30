package cn.pf.orch.meiye.handler;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.extra.spring.SpringUtil;
import cn.pf.orch.feishu.model.callback.EventCallbackCommand;
import cn.pf.orch.meiye.domain.feishu.FeishuApprovalEventCommand;
import cn.pf.orch.meiye.enums.feishu.EventCallbackEnum;
import cn.pf.orch.meiye.handler.strategy.ApprovalEventStrategy;
import cn.pf.orch.meiye.properties.ApprovalProperties;
import cn.pf.orch.meiye.properties.MeiyeSeverProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Component
public class ApprovalInstanceHandler implements CallbackHandler<FeishuApprovalEventCommand> {
    @Resource
    private MeiyeSeverProperties meiyeSeverProperties;

    @Override
    public void handle(EventCallbackCommand<FeishuApprovalEventCommand> command) {
        String json = JsonUtils.toJson(command.getEvent());
        FeishuApprovalEventCommand event = JsonUtils.parse(json, FeishuApprovalEventCommand.class);
        Optional<ApprovalProperties> approvalOptional = meiyeSeverProperties.getApproval().stream().filter(item -> item.getApprovalCode().equals(event.getApprovalCode())).findFirst();
        String approvalCode = approvalOptional.map(ApprovalProperties::getCode).orElse(null);
        try {
            Map<String, ApprovalEventStrategy> instanceMap = SpringUtil.getBeansOfType(ApprovalEventStrategy.class);
            ApprovalEventStrategy instanceStrategy = null;
            for (ApprovalEventStrategy value : instanceMap.values()) {
                if (value.getApprovalCode().equals(approvalCode+"-instance")) {
                    instanceStrategy = value;
                    break;
                }
            }
            if (instanceStrategy == null) {
                log.error("未找到对应的审批实例实现,instanceCode:{}", event.getInstanceCode());
                return;
            }
            Boolean response = instanceStrategy.execute(event);
            if (!response) {
                log.error("[审批实例事件] 执行失败!,event:{}", JsonUtils.toJson(event));
            }
        } catch (Exception e) {
            log.error("[审批实例事件] 执行失败!!,event:{}", JsonUtils.toJson(event), e);
        }
    }

    @Override
    public EventCallbackEnum event() {
        return EventCallbackEnum.APPROVAL_INSTANCE;
    }
}
