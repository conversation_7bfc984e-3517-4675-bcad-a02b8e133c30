package cn.pf.orch.meiye.assembler;

import cn.genn.core.model.page.PageResultDTO;
import cn.pf.orch.meiye.domain.risk.command.MyRiskInfoSaveCommand;
import cn.pf.orch.meiye.domain.risk.command.MyRiskInfoUpdateCommand;
import cn.pf.orch.meiye.domain.risk.dto.RiskInfoPageDTO;
import cn.pf.orch.meiye.domain.risk.po.RiskInfoPO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RiskInfoAssembler {

    RiskInfoPO saveCommand2PO(MyRiskInfoSaveCommand command);

    RiskInfoPO updateCommand2PO(MyRiskInfoUpdateCommand command);

    @Mapping(target = "pageNo", source = "current")
    @Mapping(target = "pageSize", source = "size")
    @Mapping(target = "list", source = "records")
    @Mapping(target = "totalPages", source = "pages")
    PageResultDTO<RiskInfoPageDTO> toPage(IPage<RiskInfoPageDTO> poPage);
}
