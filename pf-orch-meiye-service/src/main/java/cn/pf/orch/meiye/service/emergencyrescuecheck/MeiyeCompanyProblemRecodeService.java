package cn.pf.orch.meiye.service.emergencyrescuecheck;

import cn.pf.orch.meiye.domain.emergencyrescuecheck.command.AddProblemCommand;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.command.UpdateProblemCommand;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.po.MeiyeCompanyProblemRecode;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.constraints.NotNull;

/**
 * 应急救援检查问题描述表(MeiyeCompanyProblemRecode)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-17 15:54:47
 */
public interface MeiyeCompanyProblemRecodeService extends IService<MeiyeCompanyProblemRecode> {

    void addProblem(AddProblemCommand addProblem);

    void updateById(UpdateProblemCommand updateProblem);

    void delete(Long problemId);
}

