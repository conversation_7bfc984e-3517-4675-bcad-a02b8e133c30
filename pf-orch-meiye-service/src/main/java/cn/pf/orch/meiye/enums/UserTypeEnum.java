package cn.pf.orch.meiye.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;


@Getter
@AllArgsConstructor
public enum UserTypeEnum {

    IN(1,"内部用户"),
    OUT(2,"外部用户"),
    ;

    @EnumValue
    @JsonValue
    private final int code;

    private final String description;


    private static final Map<Integer, UserTypeEnum> VALUES = new HashMap<>();

    static {
        for (final UserTypeEnum item : UserTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static UserTypeEnum of(Integer code) {
        return VALUES.get(code);
    }
}
