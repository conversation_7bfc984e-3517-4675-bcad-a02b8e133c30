package cn.pf.orch.meiye.mapper;

import cn.pf.orch.meiye.domain.hazard.dto.HazardIdentifyDTO;
import cn.pf.orch.meiye.domain.hazard.po.HazardIdentifyPO;
import cn.pf.orch.meiye.domain.hazard.query.HazardIdentifyQuery;
import cn.pf.orch.meiye.enums.IdentifyStatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface HazardIdentifyMapper extends BaseMapper<HazardIdentifyPO> {

    IPage<HazardIdentifyDTO> selectByPage(IPage<HazardIdentifyPO> page, @Param("query") HazardIdentifyQuery query);


    default HazardIdentifyPO selectByName(String name) {
        LambdaQueryWrapper<HazardIdentifyPO> wrapper = Wrappers.lambdaQuery(HazardIdentifyPO.class)
                .eq(HazardIdentifyPO::getName, name)
                .last("limit 1");
        return selectOne(wrapper);
    }

    default List<HazardIdentifyPO> selectByNoEndStatus(){
        LambdaQueryWrapper<HazardIdentifyPO> wrapper = Wrappers.lambdaQuery(HazardIdentifyPO.class)
                .notIn(HazardIdentifyPO::getStatus, IdentifyStatusEnum.FIVE);
        return selectList(wrapper);
    }

    default List<HazardIdentifyPO> selectByCompanyIds(List<Long> companyIds){
        LambdaQueryWrapper<HazardIdentifyPO> wrapper = Wrappers.lambdaQuery(HazardIdentifyPO.class)
                .gt(HazardIdentifyPO::getEndTime, LocalDateTime.now())
                .in(HazardIdentifyPO::getCompanyId, companyIds);
        return selectList(wrapper);
    }
}
