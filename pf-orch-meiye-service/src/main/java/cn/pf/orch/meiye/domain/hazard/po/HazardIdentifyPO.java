package cn.pf.orch.meiye.domain.hazard.po;

import cn.genn.core.model.enums.DeletedEnum;
import cn.pf.orch.meiye.enums.IdentifyHazardDetailEnum;
import cn.pf.orch.meiye.enums.IdentifyStatusEnum;
import cn.pf.orch.meiye.enums.IdentifyTypeEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * HazardIdentifyPO对象
 *
 * <AUTHOR>
 * @desc 隐患排查计划
 */
@Data
@Accessors(chain = true)
@TableName(value = "hazard_identify", autoResultMap = true)
public class HazardIdentifyPO {

    /**
     * 主键id
     */
    @TableId
    private Long id;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 公司id
     */
    @TableField("company_id")
    private Long companyId;

    /**
     * 排查类型
     */
    @TableField("identify_type")
    private IdentifyTypeEnum identifyType;

    /**
     * 类别详情
     */
    @TableField("identify_detail")
    private IdentifyHazardDetailEnum identifyDetail;

    /**
     * 状态（未开始，进行中，已结束）
     */
    @TableField("status")
    private IdentifyStatusEnum status;

    /**
     * 责任人
     */
    @TableField("identify_owner")
    private String identifyOwner;

    /**
     * 人员名称
     */
    @TableField("identify_owner_name")
    private String identifyOwnerName;

    /**
     * 开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 删除状态（0未删除，1已删除）
     */
    @TableField("deleted")
    private DeletedEnum deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;

    /**
     * 创建人
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人id
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    /**
     * 更新人
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

}

