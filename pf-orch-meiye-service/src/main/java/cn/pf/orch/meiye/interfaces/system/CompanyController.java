package cn.pf.orch.meiye.interfaces.system;


import cn.pf.orch.meiye.domain.system.command.CompanyAddCommand;
import cn.pf.orch.meiye.domain.system.command.CompanyUpdateCommand;
import cn.pf.orch.meiye.domain.system.dto.MyCompanyDTO;
import cn.pf.orch.meiye.domain.system.dto.MyCompanyTreeDTO;
import cn.pf.orch.meiye.domain.system.query.MyCompanyQuery;
import cn.pf.orch.meiye.enums.AuthTypeEnum;
import cn.pf.orch.meiye.service.system.CompanyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;


@Slf4j
@Api(tags = "系统管理-组织机构")
@RestController
@RequestMapping("/company")
public class CompanyController {

    @Resource
    private CompanyService companyService;

    @PostMapping("/queryList")
    @ApiOperation(value = "查询公司树")
    public List<MyCompanyTreeDTO> queryList(@Validated @RequestBody MyCompanyQuery query) {
        return companyService.queryList(query);
    }

    @PostMapping("/get")
    @ApiOperation(value = "根据id查询")
    public MyCompanyDTO get(@ApiParam(name = "id", required = true) @RequestParam Long id) {
        return companyService.get(id);
    }

    @PostMapping("/add")
    @ApiOperation(value = "添加公司")
    public void add(@Valid @RequestBody CompanyAddCommand command) {
        companyService.add(command);
    }

    @PostMapping("/update")
    @ApiOperation(value = "编辑公司")
    public void update(@Valid @RequestBody CompanyUpdateCommand command) {
        companyService.update(command);
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除公司")
    public void delete(@ApiParam(name = "id") @RequestParam Long id) {
        companyService.delete(id);
    }

    @PostMapping("/queryTreeRange")
    @ApiOperation(value = "用户权限范围内的组织树")
    public List<MyCompanyTreeDTO> queryTreeRange() {
        return companyService.queryTreeRange();
    }

    @PostMapping("/queryRange")
    @ApiOperation(value = "用户权限范围内的组织")
    public List<MyCompanyDTO> queryRange(@ApiParam @RequestParam(required = false) AuthTypeEnum authType) {
        return companyService.queryRange(authType);
    }


}
