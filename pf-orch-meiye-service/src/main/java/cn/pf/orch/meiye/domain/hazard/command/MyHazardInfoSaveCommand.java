package cn.pf.orch.meiye.domain.hazard.command;

import cn.pf.orch.meiye.enums.ControlStatusEnum;
import cn.pf.orch.meiye.enums.RiskLevelEnum;
import cn.pf.orch.meiye.enums.RiskTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class MyHazardInfoSaveCommand {

    @ApiModelProperty(value = "隐患排查id")
    @NotNull(message = "隐患排查不能为空")
    private Long hazardIdentifyId;

    @ApiModelProperty(value = "煤矿id")
    @NotNull(message = "煤矿不能为空")
    private Long companyId;

    @ApiModelProperty(value = "隐患等级")
    @NotNull(message = "隐患等级不能为空")
    private RiskLevelEnum level;

    @ApiModelProperty(value = "隐患类别")
    @NotNull(message = "隐患类别不能为空")
    private RiskTypeEnum type;

    @ApiModelProperty(value = "可控状态（0可控，1失控）")
    @NotNull(message = "可控状态不能为空")
    private ControlStatusEnum controlStatus;

    @ApiModelProperty(value = "隐患责任人")
    @NotBlank(message = "责任人不能为空")
    private String owner;

    @ApiModelProperty(value = "隐患责任人名称")
    @NotBlank(message = "责任人不能为空")
    private String ownerName;

    @ApiModelProperty(value = "描述")
    @NotBlank(message = "描述不能为空")
    private String remark;

    @ApiModelProperty("地址id")
    @NotNull(message = "地址不能为空")
    private Long addressId;

    @ApiModelProperty("地址详情")
    private String addressDetail;

    @ApiModelProperty("整改单位id")
    private String departmentId;

    @ApiModelProperty("整改单位名称")
    private String departmentName;

    @ApiModelProperty("整改责任人id")
    private String rectifyOwner;

    @ApiModelProperty("整改责任人名称")
    private String rectifyOwnerName;

    @ApiModelProperty("整改期限")
    private LocalDateTime rectifyTime;

    @ApiModelProperty("整改资金(元)")
    @DecimalMin(value = "0.00", message = "整改资金不能小于0")
    @DecimalMax(value = "100000000", message = "整改资金不能大于1亿")
    private BigDecimal rectifyMoney;

}
