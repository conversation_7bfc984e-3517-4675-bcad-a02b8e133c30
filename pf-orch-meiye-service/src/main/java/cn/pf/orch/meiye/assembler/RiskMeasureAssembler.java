package cn.pf.orch.meiye.assembler;

import cn.pf.orch.meiye.domain.risk.command.RiskMeasureSaveCommand;
import cn.pf.orch.meiye.domain.risk.command.RiskMeasureUpdateCommand;
import cn.pf.orch.meiye.domain.risk.dto.RiskMeasureDTO;
import cn.pf.orch.meiye.domain.risk.po.RiskMeasurePO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RiskMeasureAssembler {

    RiskMeasurePO saveCommand2PO(RiskMeasureSaveCommand command);

    RiskMeasurePO updateCommand2PO(RiskMeasureUpdateCommand command);

    RiskMeasureDTO PO2DTO(RiskMeasurePO po);

    List<RiskMeasureDTO> PO2DTO(List<RiskMeasurePO> po);
}
