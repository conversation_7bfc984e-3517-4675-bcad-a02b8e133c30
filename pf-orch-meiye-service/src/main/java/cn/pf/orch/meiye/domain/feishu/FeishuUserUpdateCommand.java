package cn.pf.orch.meiye.domain.feishu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class FeishuUserUpdateCommand {

    @JsonProperty("object")
    private UserDTO object;

    @JsonProperty("old_object")
    private UserDTO oldObject;


    @Data
    public static  class UserDTO{

        @JsonProperty("open_id")
        private String openId;

        @JsonProperty("mobile")
        private String mobile;

        @JsonProperty("department_ids")
        private List<String> departmentIds;
    }
}
