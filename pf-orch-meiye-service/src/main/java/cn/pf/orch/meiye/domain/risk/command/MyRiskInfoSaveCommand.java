package cn.pf.orch.meiye.domain.risk.command;

import cn.pf.orch.meiye.enums.ControlStatusEnum;
import cn.pf.orch.meiye.enums.RiskLevelEnum;
import cn.pf.orch.meiye.enums.RiskTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class MyRiskInfoSaveCommand {

    @ApiModelProperty(value = "风险辨识id")
//    @NotNull(message = "风险辨识不能为空")
    private Long riskIdentifyId;

    @ApiModelProperty(value = "煤矿id")
    @NotNull(message = "煤矿不能为空")
    private Long companyId;

    @ApiModelProperty(value = "风险等级")
    @NotNull(message = "风险等级不能为空")
    private RiskLevelEnum level;

    @ApiModelProperty(value = "风险类别")
    @NotNull(message = "风险类别不能为空")
    private RiskTypeEnum type;

    @ApiModelProperty(value = "可控状态（0可控，1失控）")
    @NotNull(message = "可控状态不能为空")
    private ControlStatusEnum controlStatus;

    @ApiModelProperty(value = "风险责任人")
    @NotBlank(message = "责任人不能为空")
    private String owner;

    @ApiModelProperty(value = "风险责任人名称")
    @NotBlank(message = "责任人不能为空")
    private String ownerName;

    @ApiModelProperty(value = "描述")
    @NotBlank(message = "描述不能为空")
    private String remark;

    @ApiModelProperty("地址id")
    @NotNull(message = "地址不能为空")
    private Long addressId;

    @ApiModelProperty("地址详情")
    private String addressDetail;

}
