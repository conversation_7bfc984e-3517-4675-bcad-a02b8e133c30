package cn.pf.orch.meiye.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
public class RedisUtils {

    private static StringRedisTemplate redisTemplate;

    @Autowired
    public void setRedisTemplate(StringRedisTemplate redisTemplate) {
        RedisUtils.redisTemplate = redisTemplate;
    }


    ///////////string////////////

    public static void set(String key, String value) {
        redisTemplate.opsForValue().set(key, value);
    }

    public static void set(String key, String value, Long time, TimeUnit timeUnit) {
        redisTemplate.opsForValue().set(key, value, time, timeUnit);
    }

    public static String get(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    public static void getAndSet(String key, String value) {
        redisTemplate.opsForValue().getAndSet(key, value);
    }

    public static Long incr(String key, Long value) {
        return redisTemplate.opsForValue().increment(key, value);
    }

    public static List<String> mGet(Collection keys) {
        return redisTemplate.opsForValue().multiGet(keys);
    }

    //////////////key//////////////////

    /**
     * 设置某个时间点过期
     *
     * @param key
     * @param time 过期的时间点 毫秒
     * @return
     */
    public static boolean expireAt(String key, long time) {
        return redisTemplate.expireAt(key, new Date(time));
    }

    public static void delete(String key) {
        redisTemplate.delete(key);
    }

    public static void deleteKeysByPrefix(String prefix) {
        // 使用模糊查询获取所有匹配的key
        Set<String> keys = redisTemplate.keys(prefix + "*");
        if (keys != null && !keys.isEmpty()) {
            // 批量删除这些key
            redisTemplate.delete(keys);
        }
    }

    /**
     * key 是否存在
     */
    public static Boolean hasKey(String key) {
        return redisTemplate.hasKey(key);
    }

    /**
     * 模糊查询key
     *
     * @param pattern 模糊key
     * @return
     */
    public static Set<String> keys(String pattern) {
        return redisTemplate.keys(pattern);
    }

    ///////////////////List///////////////////////////
    public static Long rpush(String key, String value) {
        return redisTemplate.opsForList().rightPush(key, value);
    }

    public static List<String> lrange(String key) {
        return redisTemplate.opsForList().range(key, 0, -1);
    }

    //////////////////hash///////////////////
    public static void hSet(String key, String hKey, Object hValue) {
        redisTemplate.opsForHash().put(key, hKey, hValue);
    }


    /**
     * 向一张hash表中放入数据,如果不存在将创建
     *
     * @param key   键
     * @param item  项
     * @param value 值
     * @param time  时间(秒) 注意:如果已存在的hash表有时间,这里将会替换原有的时间
     * @return true 成功 false失败
     */
    public static boolean hSetData(String key, String item, Object value, long time) {
        try {
            redisTemplate.opsForHash().put(key, item, value);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 指定缓存失效时间
     *
     * @param key  键
     * @param time 时间(秒)
     * @return
     */
    public static boolean expire(String key, long time) {
        try {
            if (time > 0) {
                redisTemplate.expire(key, time, TimeUnit.SECONDS);
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static Object hGet(String key, String hKey) {
        return redisTemplate.opsForHash().get(key, hKey);
    }

    /**
     * hash  是否存在
     */
    public static Boolean hExists(String key, String hKey) {
        return redisTemplate.opsForHash().hasKey(key, hKey);
    }

    /**
     * Hash自增
     */
    public static Long hIncr(String key, Object hk, Long value) {
        return redisTemplate.opsForHash().increment(key, hk, value);
    }

    public static Set<Object> hKeys(String key) {
        return redisTemplate.opsForHash().keys(key);
    }

    public static List<Object> hVals(String key) {
        return redisTemplate.opsForHash().values(key);
    }

    public static Map<Object, Object> entries(String key) {
        return redisTemplate.opsForHash().entries(key);
    }


    /**
     * Hash删除
     */
    public static Long deleteHash(String key1, String key2) {
        return redisTemplate.opsForHash().delete(key1, key2);
    }

    //////////set////////////

    /**
     * 去重插入
     */
    public static Long sAdd(String key, String v) {
        return redisTemplate.opsForSet().add(key, v);
    }

    private static final String SADD_LUA_TEXT = "local res = redis.call('SADD', '%s', '%s')\n" +
            "if res == 1 then -- Value was added to the set\n" +
            "    redis.call('PEXPIRE', '%s', %d) -- Refresh expiration time\n" +
            "end\n" +
            "return res";

    public static Long sAdd(String key, String v, Long time, TimeUnit timeUnit) {

        String luaScript = String.format(SADD_LUA_TEXT, key, v, key, timeUnit.toMillis(time));
        DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>(luaScript, Long.class);
        return redisTemplate.execute(redisScript, Collections.singletonList(key));
    }

    public static Set<String> sMembers(String key) {
        return redisTemplate.opsForSet().members(key);
    }

    /**
     * 删除value值
     */
    public static void sRemove(String key, String[] values) {
        redisTemplate.opsForSet().remove(key, values);
    }


    /**
     * 切换DB  spring 2.X 的写法
     */
    public static void selectDB(int index) {
        LettuceConnectionFactory jedisConnectionFactory = (LettuceConnectionFactory) redisTemplate.getConnectionFactory();
        jedisConnectionFactory.setDatabase(index);
        redisTemplate.setConnectionFactory(jedisConnectionFactory);
        jedisConnectionFactory.resetConnection();
    }

}
