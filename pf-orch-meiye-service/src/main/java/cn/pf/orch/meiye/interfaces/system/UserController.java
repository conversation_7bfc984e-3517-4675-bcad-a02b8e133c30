package cn.pf.orch.meiye.interfaces.system;

import cn.genn.core.model.page.PageResultDTO;
import cn.pf.orch.meiye.domain.hazard.query.CompanyRangeUserQuery;
import cn.pf.orch.meiye.domain.system.command.MyUserApprovalSignCommand;
import cn.pf.orch.meiye.domain.system.command.MyUserRoleRelationCommand;
import cn.pf.orch.meiye.domain.system.command.MyUserSaveCommand;
import cn.pf.orch.meiye.domain.system.command.MyUserUpdateCommand;
import cn.pf.orch.meiye.domain.system.dto.MyUserDTO;
import cn.pf.orch.meiye.domain.system.query.MyUserPageQuery;
import cn.pf.orch.meiye.service.system.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api(tags = "系统管理-用户管理")
@RestController
@RequestMapping("/user")
public class UserController {

    @Resource
    private UserService userService;

    @PostMapping("/page")
    @ApiOperation(value = "分页查询列表")
    public PageResultDTO<MyUserDTO> page(@ApiParam(value = "查询类") @RequestBody @Validated MyUserPageQuery query) {
        return userService.page(query);
    }

    @PostMapping("/related/role")
    @ApiOperation(value = "关联角色")
    public Boolean relatedRole(@ApiParam(value = "用户关联角色") @RequestBody @Validated MyUserRoleRelationCommand command) {
        return userService.relatedRole(command);
    }

    @PostMapping("/related/approval")
    @ApiOperation(value = "关联审批权限")
    public Boolean relatedApproval(@ApiParam(value = "用户关联审批权限") @RequestBody @Validated MyUserApprovalSignCommand command) {
        return userService.relatedApproval(command);
    }

    @PostMapping("/save")
    @ApiOperation(value = "添加用户")
    public Boolean save(@ApiParam(value = "角色") @RequestBody @Validated MyUserSaveCommand command) {
        return userService.save(command);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改用户")
    public Boolean change(@ApiParam(value = "角色") @RequestBody @Validated MyUserUpdateCommand command) {
        return userService.change(command);
    }

    @PostMapping("/batch/delete")
    @ApiOperation(value = "批量删除用户")
    public Boolean batchRemove(@ApiParam(value = "批量删除角色") @RequestBody List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return true;
        }
        return userService.batchRemove(idList);
    }

    @PostMapping("/selectByCompanyId")
    @ApiOperation(value = "公司id查询范围用户")
    public List<MyUserDTO> selectByCompanyId(@RequestBody @Validated CompanyRangeUserQuery query) {
        long start = System.currentTimeMillis();
        log.info("selectByCompanyId start: {}", start);
        List<MyUserDTO> myUserDTOS = userService.selectByCompanyId(query);
        log.info("selectByCompanyId stop: {}", (System.currentTimeMillis() - start)/1000);
        return myUserDTOS;
    }

}
