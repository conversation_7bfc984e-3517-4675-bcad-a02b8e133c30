package cn.pf.orch.meiye.interfaces;

import cn.pf.orch.feishu.FeishuAppClient;
import cn.pf.orch.feishu.model.ContactSearchUserDTO;
import cn.pf.orch.meiye.assembler.UserInfoAssembler;
import cn.pf.orch.meiye.domain.system.command.LogoutToolCommand;
import cn.pf.orch.meiye.domain.system.dto.ContactSearchUserRespDTO;
import cn.pf.orch.meiye.service.SsoService;
import com.lark.oapi.service.contact.v3.model.UserContactInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;

@Slf4j
@Api(tags = "后台工具")
@RestController
@RequestMapping("/tool")
public class ToolController {

    @Resource
    private SsoService ssoService;
    @Resource
    private FeishuAppClient feishuAppClient;
    @Resource
    private UserInfoAssembler userInfoAssembler;

    @PostMapping("/logoutTool")
    @ApiOperation("登出工具")
    public Boolean logoutTool(@RequestBody LogoutToolCommand command) {
        return ssoService.logoutTool(command);
    }

    /**
     * 手机号获取飞书用户信息(方便测试用)
     * 支持离职员工
     * @param telephone
     * @return
     */
    @GetMapping("/getUserInfo")
    @ApiOperation(value = "手机号获取飞书用户信息,支持查询离职员工")
    public Object getUserInfo(@RequestParam("telephone")String telephone){
        UserContactInfo[] userList = feishuAppClient.getContactService().getOpenId(telephone).getUserList();
        return feishuAppClient.getContactService().userBatch(Collections.singletonList(userList[0].getUserId()));
    }

    /**
     * 名称模糊查询飞书用户信息
     * 不支持搜索离职员工
     */
    @PostMapping("/searchUser")
    @ApiOperation(value = "名称模糊查询飞书用户信息,不支持查询离职员工")
    public ContactSearchUserRespDTO searchUser(@RequestBody ContactSearchUserDTO query){
        return userInfoAssembler.resp2DTO(feishuAppClient.getContactService().searchUser(query));
    }

    @GetMapping("/subscribeInstance")
    @ApiOperation(value = "审批定义订阅")
    public Boolean syncDepartmentAndUser(@RequestParam("approvalCode") String approvalCode){
        feishuAppClient.getApprovalService().subscribeInstance(approvalCode);
        return true;
    }
}
