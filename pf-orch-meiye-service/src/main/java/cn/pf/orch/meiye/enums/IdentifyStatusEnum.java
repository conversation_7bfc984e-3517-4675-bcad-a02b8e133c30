package cn.pf.orch.meiye.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum IdentifyStatusEnum {

    ONE(1, "未开始"),
    FOUR(4,"进行中"),
    FIVE(5,"已结束"),
    ;


    @EnumValue
    @JsonValue
    private final int type;

    private final String description;

    private static final Map<Integer, IdentifyStatusEnum> VALUES = new HashMap<>();
    static {
        for (final IdentifyStatusEnum item : IdentifyStatusEnum.values()) {
            VALUES.put(item.getType(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static IdentifyStatusEnum of(int code) {
        return VALUES.get(code);
    }

    public static IdentifyStatusEnum getStatus(LocalDateTime startTime, LocalDateTime endTime){
        LocalDateTime currentTime = LocalDateTime.now();
        if (currentTime.isBefore(startTime)) {
            return IdentifyStatusEnum.ONE;
        } else if (currentTime.isAfter(endTime)) {
            return IdentifyStatusEnum.FIVE;
        } else {
            return IdentifyStatusEnum.FOUR;
        }
    }
}
