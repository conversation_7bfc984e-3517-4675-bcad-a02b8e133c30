package cn.pf.orch.meiye.service.safetyaccident;

import cn.pf.orch.meiye.domain.safetyaccident.command.AddCommand;
import cn.pf.orch.meiye.domain.safetyaccident.command.UpdateCommand;
import cn.pf.orch.meiye.domain.safetyaccident.dto.MeiyeSafetyAccidentDTO;
import cn.pf.orch.meiye.domain.safetyaccident.po.MeiyeSafetyAccidentRecords;
import cn.pf.orch.meiye.domain.safetyaccident.query.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.constraints.NotNull;

/**
 * 安全事故记录表(MeiyeSafetyAccidentRecords)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-13 19:28:18
 */
public interface MeiyeSafetyAccidentRecordsService extends IService<MeiyeSafetyAccidentRecords> {

    void add(AddCommand record);

    void update(UpdateCommand record);

    void delete(Integer id);

    Page<MeiyeSafetyAccidentDTO> query(PageQuery query);

    MeiyeSafetyAccidentDTO detail(@NotNull Integer id);
}

