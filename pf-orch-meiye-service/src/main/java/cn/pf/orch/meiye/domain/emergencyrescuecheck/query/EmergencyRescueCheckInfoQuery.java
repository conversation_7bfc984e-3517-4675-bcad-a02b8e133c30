package cn.pf.orch.meiye.domain.emergencyrescuecheck.query;

import cn.genn.core.model.page.PageSortQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class EmergencyRescueCheckInfoQuery extends PageSortQuery {

    @ApiModelProperty(value = "企业ID", example = "1001")
    private Long companyId;

    @ApiModelProperty(value = "企业名称", example = "某某科技有限公司")
    private String companyName;

    @ApiModelProperty(value = "检查类型", example = "日常检查")
    private String inspectionType;

    @ApiModelProperty(value = "项目名称", example = "安全生产检查")
    private String project;

    @ApiModelProperty(value = "检查内容", example = "消防设施、安全通道检查")
    private String content;

    @ApiModelProperty(value = "问题描述", example = "灭火器过期，安全通道堵塞")
    private String description;

    @ApiModelProperty("问题Id")
    private Long problemId;

    @ApiModelProperty(value = "问题描述/检查项目/检查内容")
    private String keywords;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime createTimeStart;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime createTimeEnd;

    private String source;

    private List<Long> rangeCompanyIds;
}
