package cn.pf.orch.meiye.assembler;

import cn.pf.orch.meiye.domain.location.command.AddLocationCommand;
import cn.pf.orch.meiye.domain.location.dto.LocationTree;
import cn.pf.orch.meiye.domain.location.po.MeiyeLocation;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface LocationAssembler {

    MeiyeLocation dtoToPO(AddLocationCommand po);


    LocationTree POToTree(MeiyeLocation po);
    List<LocationTree> POToTreeList(List<MeiyeLocation> po);


}
