package cn.pf.orch.meiye.mapper;

import cn.pf.orch.meiye.domain.risk.po.MyApprovalLogPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MyApprovalLogMapper extends BaseMapper<MyApprovalLogPO> {


    default List<MyApprovalLogPO> selectByApprovalId(String approvalId){
        return selectList(Wrappers.lambdaQuery(MyApprovalLogPO.class)
                .eq(MyApprovalLogPO::getApprovalId,approvalId));
    }

    List<MyApprovalLogPO> selectLastApprovalIds(List<String> approvalIds);
}
