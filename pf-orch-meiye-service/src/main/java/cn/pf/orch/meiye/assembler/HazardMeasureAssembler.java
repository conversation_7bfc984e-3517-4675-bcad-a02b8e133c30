package cn.pf.orch.meiye.assembler;

import cn.pf.orch.meiye.domain.hazard.command.HazardMeasureSaveCommand;
import cn.pf.orch.meiye.domain.hazard.command.HazardMeasureUpdateCommand;
import cn.pf.orch.meiye.domain.hazard.dto.HazardMeasureDTO;
import cn.pf.orch.meiye.domain.hazard.po.HazardMeasurePO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface HazardMeasureAssembler {

    HazardMeasurePO saveCommand2PO(HazardMeasureSaveCommand command);

    HazardMeasurePO updateCommand2PO(HazardMeasureUpdateCommand command);

    HazardMeasureDTO PO2DTO(HazardMeasurePO po);

    List<HazardMeasureDTO> PO2DTO(List<HazardMeasurePO> po);
}
