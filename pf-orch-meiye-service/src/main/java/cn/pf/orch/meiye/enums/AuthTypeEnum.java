package cn.pf.orch.meiye.enums;

import cn.genn.core.exception.BusinessException;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;

@Getter
@AllArgsConstructor
public enum AuthTypeEnum {

    GROUP(1,"集团"),
    REGION(2,"区域公司"),
    COLLIERY(3,"煤矿"),
    ;

    @EnumValue
    @JsonValue
    private final Integer code;

    private final String description;


    private static final Map<Integer, AuthTypeEnum> VALUES = new HashMap<>();

    static {
        for (final AuthTypeEnum item : AuthTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static AuthTypeEnum of(Integer code) {
        return VALUES.get(code);
    }

    public static List<AuthTypeEnum> arrange(AuthTypeEnum authType){
        switch(authType){
            case GROUP: return Arrays.asList(GROUP, REGION, COLLIERY);
            case REGION: return Arrays.asList( REGION, COLLIERY);
            case COLLIERY: return Collections.singletonList(COLLIERY);
        }
        throw new BusinessException("未知的权限类型,authType:{}", String.valueOf(authType.code));
    }
}
