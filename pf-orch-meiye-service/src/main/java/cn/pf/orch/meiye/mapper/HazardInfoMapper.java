package cn.pf.orch.meiye.mapper;

import cn.pf.orch.meiye.domain.hazard.dto.HazardCountDTO;
import cn.pf.orch.meiye.domain.hazard.dto.HazardInfoDTO;
import cn.pf.orch.meiye.domain.hazard.dto.HazardInfoPageDTO;
import cn.pf.orch.meiye.domain.hazard.po.HazardInfoPO;
import cn.pf.orch.meiye.domain.hazard.query.HazardInfoQuery;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface HazardInfoMapper extends BaseMapper<HazardInfoPO> {

    IPage<HazardInfoPageDTO> selectByPage(IPage<HazardInfoPO> page, @Param("query") HazardInfoQuery query);

    List<HazardCountDTO> selectLevelCount(@Param("query") HazardInfoQuery query);

    default List<HazardInfoPO> selectByIdentifyIds(List<Long> ids) {
        LambdaQueryWrapper<HazardInfoPO> wrapper = Wrappers.lambdaQuery(HazardInfoPO.class)
                .in(HazardInfoPO::getHazardIdentifyId, ids);
        return this.selectList(wrapper);
    }

    default HazardInfoPO getMaxCode() {
        LambdaQueryWrapper<HazardInfoPO> wrapper = Wrappers.lambdaQuery(HazardInfoPO.class)
                .orderByDesc(HazardInfoPO::getCode)
                .last("limit 1");
        return selectOne(wrapper);
    }

    default HazardInfoPO selectByApprovalId(String approvalId){
        LambdaQueryWrapper<HazardInfoPO> wrapper = Wrappers.lambdaQuery(HazardInfoPO.class)
                .eq(HazardInfoPO::getApprovalId, approvalId)
                .last("limit 1");
        return selectOne(wrapper);
    }

    HazardInfoDTO selectDetailById(@Param("id") Long id);
}
