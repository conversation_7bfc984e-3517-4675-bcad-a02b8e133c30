package cn.pf.orch.meiye.processor;

import cn.genn.core.exception.BusinessException;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.pf.orch.meiye.domain.risk.command.MyRiskIdentifySaveCommand;
import cn.pf.orch.meiye.domain.risk.command.MyRiskIdentifyUpdateCommand;
import cn.pf.orch.meiye.domain.risk.po.RiskIdentifyPO;
import cn.pf.orch.meiye.domain.risk.po.RiskInfoPO;
import cn.pf.orch.meiye.exception.MessageCode;
import cn.pf.orch.meiye.mapper.RiskIdentifyMapper;
import cn.pf.orch.meiye.mapper.RiskInfoMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class RiskIdentifyProcessor {

    @Resource
    private RiskIdentifyMapper mapper;
    @Resource
    private RiskInfoMapper riskInfoMapper;

    public void saveCheck(MyRiskIdentifySaveCommand command){
        RiskIdentifyPO po = mapper.selectByName(command.getName());
        if(ObjUtil.isNotEmpty(po)){
            throw new BusinessException(MessageCode.RISK_IDENTIFY_NAME_EXIST);
        }
    }

    public void changeCheck(MyRiskIdentifyUpdateCommand command){
        RiskIdentifyPO po = mapper.selectByName(command.getName());
        if(ObjUtil.isNotEmpty(po) && !po.getId().equals(command.getId())){
            throw new BusinessException(MessageCode.RISK_IDENTIFY_NAME_EXIST);
        }
    }

    public void deleteCheck(List<Long> ids){
        List<RiskInfoPO> pos = riskInfoMapper.selectByIdentifyIds(ids);
        if(CollUtil.isNotEmpty(pos)){
            throw new BusinessException(MessageCode.RISK_EXIST_ERROR);
        }
    }
}
