package cn.pf.orch.meiye.processor;

import cn.genn.core.exception.BusinessException;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.pf.orch.meiye.domain.system.command.CompanyAddCommand;
import cn.pf.orch.meiye.domain.system.command.CompanyUpdateCommand;
import cn.pf.orch.meiye.domain.system.po.MyCompanyPO;
import cn.pf.orch.meiye.domain.system.po.MyUserPO;
import cn.pf.orch.meiye.exception.MessageCode;
import cn.pf.orch.meiye.mapper.MyCompanyMapper;
import cn.pf.orch.meiye.mapper.MyUserMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class CompanyProcessor {

    @Resource
    private MyCompanyMapper companyMapper;
    @Resource
    private MyUserMapper userMapper;

    public void checkSave(CompanyAddCommand command) {
        //名称去重校验
        List<MyCompanyPO> companyPOList = companyMapper.selectByName(command.getName());
        if(CollUtil.isNotEmpty(companyPOList)){
            throw new BusinessException(MessageCode.COMPANY_NAME_EXIST_ERROR);
        }
    }

    public MyCompanyPO checkUpdate(CompanyUpdateCommand command) {
        //名称去重校验
        List<MyCompanyPO> companyPOList = companyMapper.selectByName(command.getName());
        boolean isNameExist = companyPOList.stream().anyMatch(companyPO -> !companyPO.getId().equals(command.getId()));
        if(isNameExist){
            throw new BusinessException(MessageCode.COMPANY_NAME_EXIST_ERROR);
        }
        MyCompanyPO companyPO = companyMapper.selectById(command.getId());
        if(ObjUtil.isEmpty(companyPO)){
            throw new BusinessException(MessageCode.COMPANY_NO_EXIST_ERROR);
        }
        return companyPO;
    }

    public void checkDelete(Long companyId) {
        List<MyCompanyPO> companyList = companyMapper.getChildrenById(companyId);
        if(CollUtil.isNotEmpty(companyList)){
            throw new BusinessException(MessageCode.CHILDREN_DEPARTMENT_EXIST_ERROR);
        }
        List<MyUserPO> userList = userMapper.selectByCompanyId(companyId,null);
        if(CollUtil.isNotEmpty(userList)){
            throw new BusinessException(MessageCode.USER_EXIST_ERROR);
        }
    }
}
