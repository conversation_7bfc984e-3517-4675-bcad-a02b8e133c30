package cn.pf.orch.meiye.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 安全敏感类别
 */
@Getter
@AllArgsConstructor
public enum SensitiveCategoryEnum {

    SAFETY_INSPECTION_INFORMATION_MUTATION(0,"安全检测信息突变"),
    CROSSING_FAULTS(1,"过断层"),
    HYDROGEOLOGICAL_ANOMALY_ZONES(2,"水文地质异常区"),
    MAJOR_CONSTRUCTION_ADJUSTMENTS(3,"重大施工调整"),
    OTHERS(4, "其他")
    ;



    @EnumValue
    @JsonValue
    private final int code;

    private final String description;


}
