package cn.pf.orch.meiye.config;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.pf.orch.meiye.domain.system.dto.MyUserRoleRelDTO;
import cn.pf.orch.meiye.domain.system.po.MyCompanyPO;
import cn.pf.orch.meiye.enums.AuthTypeEnum;
import cn.pf.orch.meiye.enums.RoleTypeEnum;
import cn.pf.orch.meiye.exception.AuthException;
import cn.pf.orch.meiye.exception.MessageCode;
import cn.pf.orch.meiye.service.system.CompanyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 线程上下文存储用户信息 SsoAuthFilter赋值
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CurrentUserHolder {
    public static final ThreadLocal<UserInfoDTO> CURRENT_USER_THREAD_LOCAL = new ThreadLocal<>();

    public static CompanyService companyService;

    @Autowired
    public void setDepartmentService(CompanyService companyService) {
        CurrentUserHolder.companyService = companyService;
    }

    public static UserInfoDTO getCurrentUserAndLog() {
        log.info("threadInfo :{}", JsonUtils.toJson(CURRENT_USER_THREAD_LOCAL.get()));
        return CURRENT_USER_THREAD_LOCAL.get();
    }

    public static Boolean hasCurrentUser() {
        return ObjUtil.isNotNull(CURRENT_USER_THREAD_LOCAL.get());
    }

    public static UserInfoDTO getCurrentUser(){
        UserInfoDTO userInfoDTO = CURRENT_USER_THREAD_LOCAL.get();
        if(userInfoDTO == null){
            throw new AuthException(MessageCode.USER_INFO_ERROR);
        }
        return userInfoDTO;
    }

    public static String getToken() {
        String token = getCurrentUser().getToken();
        if (ObjUtil.isNull(token)) {
            throw new BusinessException(MessageCode.TOKEN_GET_ERROR);
        }
        return token;
    }

    public static AuthTypeEnum getAuthType(){
        if(isAdmin()){
            return AuthTypeEnum.GROUP;
        }
        AuthTypeEnum authType = getCurrentUser().getAuthType();
        if (ObjUtil.isNull(authType)) {
            throw new BusinessException(MessageCode.AUTH_TYPE_GET_ERROR);
        }
        return authType;
    }

    public static Boolean hasCompanyId() {
        Boolean hasCurrentUser = hasCurrentUser();
        if(hasCurrentUser){
            return ObjUtil.isNotNull(getCurrentUser().getCompanyId());
        }
        return Boolean.FALSE;
    }

    public static Long getCompanyId(){
        Long companyId = getCurrentUser().getCompanyId();
        if (ObjUtil.isNull(companyId)) {
            throw new BusinessException(MessageCode.DEPARTMENT_ID_GET_ERROR);
        }
        return companyId;
    }

    public static String getOpenId(){
        String openId = getCurrentUser().getOpenId();
        if (ObjUtil.isNull(openId)) {
            throw new BusinessException(MessageCode.OPEN_ID_GET_ERROR);
        }
        return openId;
    }

    public static String getName(){
        String name = getCurrentUser().getName();
        if (ObjUtil.isNull(name)) {
            throw new BusinessException(MessageCode.USER_INFO_ERROR);
        }
        return name;
    }

    public static List<MyUserRoleRelDTO> getRole(){
        List<MyUserRoleRelDTO> roles = getCurrentUser().getRole();
        if (ObjUtil.isNull(roles)) {
            throw new BusinessException(MessageCode.ROLE_NOT_EXIST_ERROR);
        }
        return roles;
    }

    /**
     * 获取权限范围内部门id
     */
    public static List<Long>  getRangeCompanyIds(){
        List<MyCompanyPO> rangeCompanyPOs = companyService.getRangeCompanyPO(getCompanyId());
        if(CollUtil.isEmpty(rangeCompanyPOs)){
            throw new BusinessException(MessageCode.DEPARTMENTS_ID_GET_ERROR);
        }
        return rangeCompanyPOs.stream().map(MyCompanyPO::getId).distinct().collect(Collectors.toList());
    }

    /**
     * 获取权限范围内煤矿层公司id
     */
    public static List<Long> getCollieryCompanyIds(){
        List<MyCompanyPO> rangeCompanyPOs = companyService.getRangeCompanyPO(getCompanyId());
        if(CollUtil.isEmpty(rangeCompanyPOs)){
            throw new BusinessException(MessageCode.DEPARTMENTS_ID_GET_ERROR);
        }
        return rangeCompanyPOs.stream().filter(companyPO -> companyPO.getAuthType().equals(AuthTypeEnum.COLLIERY))
                .map(MyCompanyPO::getId).distinct().collect(Collectors.toList());
    }

    /**
     * 是否超管
     * @return
     */
    public static boolean isAdmin(){
        return getRole().stream().anyMatch(role -> role.getRoleName().equals("admin") && role.getRoleType().equals(RoleTypeEnum.DEFAULT));
    }

}
