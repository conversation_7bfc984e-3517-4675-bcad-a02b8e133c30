package cn.pf.orch.meiye.domain.risk.po;

import cn.pf.orch.meiye.enums.ApprovalBizTypeEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * MyAddressRelPO对象
 *
 * <AUTHOR>
 * @desc 地点关联表
 */
@Data
@Accessors(chain = true)
@TableName(value = "my_address_rel", autoResultMap = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MyAddressRelPO {

    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 地址id
     */
    @TableField("address_id")
    private Long addressId;

    /**
     * 地址详情
     */
    @TableField("address_detail")
    private String addressDetail;

    /**
     * 业务类型
     */
    @TableField("biz_type")
    private ApprovalBizTypeEnum bizType;

    /**
     * 业务id
     */
    @TableField("biz_id")
    private Long bizId;

    /**
     * 公司id
     */
    @TableField("company_id")
    private Long companyId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;

    /**
     * 创建人
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

}

