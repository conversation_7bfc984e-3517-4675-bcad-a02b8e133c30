package cn.pf.orch.meiye.domain.risk.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * MyApprovalLogPO对象
 *
 * <AUTHOR>
 * @desc 
 */
@Data
@Accessors(chain = true)
@TableName(value = "my_approval_log", autoResultMap = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MyApprovalLogPO {

    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 审批id
     */
    @TableField("approval_id")
    private String approvalId;

    /**
     * 审批状态
     */
    @TableField("status")
    private String status;

    /**
     * 审批意见
     */
    @TableField("comment")
    private String comment;

    /**
     * 用户id
     */
    @TableField("open_id")
    private String openId;

    /**
     * 用户名称
     */
    @TableField("user_name")
    private String userName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

}

