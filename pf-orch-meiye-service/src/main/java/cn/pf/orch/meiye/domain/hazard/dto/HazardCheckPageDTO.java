package cn.pf.orch.meiye.domain.hazard.dto;

import cn.genn.core.model.enums.DeletedEnum;
import cn.pf.orch.meiye.enums.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class HazardCheckPageDTO {

    @ApiModelProperty(value = "")
    private Long id;

    @ApiModelProperty(value = "隐患编号")
    private String code;

    @ApiModelProperty(value = "公司id")
    private Long companyId;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty("地址id")
    private Long addressId;

    @ApiModelProperty("隐患地址")
    private String addressName;

    @ApiModelProperty(value = "隐患描述")
    private String remark;

    @ApiModelProperty(value = "隐患措施id")
    private Long hazardMeasureId;

    @ApiModelProperty(value = "隐患措施内容")
    private String content;

    @ApiModelProperty(value = "隐患id")
    private Long hazardId;

    @ApiModelProperty(value = "检查层级（1煤矿自查，2区域抽查，3集团抽查）")
    private CheckLevelEnum level;

    @ApiModelProperty(value = "检查结果（0通过，1未通过）")
    private CheckResultEnum result;

    @ApiModelProperty(value = "可控状态（0可控，1失控）")
    private ControlStatusEnum controlStatus;

    @ApiModelProperty(value = "检查详情")
    private String detail;

    @ApiModelProperty(value = "检查附件")
    private List<String> files;

    @ApiModelProperty(value = "删除状态")
    private DeletedEnum deleted;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人id")
    private String createUserId;

    @ApiModelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人id")
    private String updateUserId;

    @ApiModelProperty(value = "更新人")
    private String updateUserName;

    @ApiModelProperty(value = "隐患等级")
    private RiskLevelEnum hazardLevel;

    @ApiModelProperty(value = "隐患类别")
    private RiskTypeEnum hazardType;

    @ApiModelProperty(value = "隐患责任人")
    private String owner;

    @ApiModelProperty(value = "隐患责任人名称")
    private String ownerName;

    @ApiModelProperty(value = "隐患创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime hazardCreateTime;

    @ApiModelProperty(value = "排查计划名称")
    private String identifyName;

    @ApiModelProperty(value = "排查类型")
    private IdentifyTypeEnum identifyType;

    @ApiModelProperty(value = "类别详情")
    private IdentifyRiskDetailEnum identifyDetail;

    @ApiModelProperty(value = "排查日期")
    private LocalDateTime identifyTime;

    @ApiModelProperty("全路径地址")
    private String fullAddressName;


    @ApiModelProperty("整改单位id")
    private String departmentId;

    @ApiModelProperty("整改单位名称")
    private String departmentName;

    @ApiModelProperty("整改责任人id")
    private String rectifyOwner;

    @ApiModelProperty("整改责任人名称")
    private String rectifyOwnerName;

    @ApiModelProperty("整改期限")
    private LocalDateTime rectifyTime;

    @ApiModelProperty("整改资金(元)")
    private BigDecimal rectifyMoney;



}
