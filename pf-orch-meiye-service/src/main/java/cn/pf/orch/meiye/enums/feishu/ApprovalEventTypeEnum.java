package cn.pf.orch.meiye.enums.feishu;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum ApprovalEventTypeEnum {

    RISK_INSTANCE("risk-instance","安全风险-审批状态"),
    RISK_TASK("risk-task","安全风险-审批任务"),
    HAZARD_INSTANCE("hazard-instance","事故隐患-审批状态"),
    HAZARD_TASK("hazard-task","事故隐患-审批任务"),
            ;
    @EnumValue
    @JsonValue
    private final String code;

    private final String description;


    private static final Map<String, ApprovalEventTypeEnum> VALUES = new HashMap<>();

    static {
        for (final ApprovalEventTypeEnum item : ApprovalEventTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static ApprovalEventTypeEnum of(String code) {
        return VALUES.get(code);
    }
}
