package cn.pf.orch.meiye.domain.safetyviolation.command;

import cn.pf.orch.meiye.enums.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@ApiModel(value ="UpdateCommand", description = "更新三违信息")
public class UpdateCommand {

    @ApiModelProperty(value = "主键ID", example = "1", required = true)
    @NotNull
    private Long id;

    @ApiModelProperty(value = "煤矿ID", example = "1001", required = true)
    private Long companyId;

    @ApiModelProperty(value = "违章经过及原因",
            example = "作业人员未系安全绳进行高空作业，且现场无监护人员",
            required = true)
    private String violationDetails;

    @ApiModelProperty(value = "三违性质",
            required = true)
    private ViolationTypeEnum violationType;

    @ApiModelProperty(value = "被查出人姓名", example = "张三", required = true)
    private String personName;

    @ApiModelProperty(value = "被查出人所属部门/区队",
            example = "采煤一队",
            required = true)
    private String personDepartment;

    @ApiModelProperty(value = "被查出人工号", example = "CKY001", required = true)
    private String personJobNumber;

    @ApiModelProperty(value = "被查出人微信OpenID", example = "oXZ8Y5X1g2...")
    private String personJobOpenid;

    @ApiModelProperty
    private String personShift;

    @ApiModelProperty(value = "班次")
    private String classes;

    @ApiModelProperty(value = "职务", example = "采煤机司机")
    private String personPosition;

    @ApiModelProperty(value = "违章发生日期",
            example = "2023-06-15",
            required = true)
    private LocalDate violationDate;

    @ApiModelProperty(value = "违章地点ID", example = "2001", required = true)
    private Long violationLocationId;

    @ApiModelProperty(value = "信息报送日期", example = "2023-06-16")
    private LocalDate reportDate;

    @ApiModelProperty(value = "处理措施",
            example = "停工学习3天，扣除当月安全奖",
            required = true)
    private String punishment;

    @ApiModelProperty(value = "处罚依据条款",
            example = "《煤矿安全规程》第128条",
            required = true)
    private String clause;

    @ApiModelProperty(value = "罚款金额（单位：元）", example = "500.00")
    private BigDecimal fine;

    @ApiModelProperty(value = "查出方式",
            example = "安全巡查",
            allowableValues = "安全巡查,视频监控,举报核实")
    private DiscoveryMethodEnum discoveryMethod;

    @ApiModelProperty(value = "查出人姓名", example = "李四（安全员）")
    private String discoverer;

    @ApiModelProperty(value = "查处人工号", example = "李四")
    private String discovererNumber;

    @ApiModelProperty(value = "查处人openid", example = "李四")
    private String discovererOpenId;

    @ApiModelProperty(value = "查处人部门名称", example = "李四")
    private String discovererDepartment;

    @ApiModelProperty(value = "查处人部门名称", example  = "李四")
    private String discovererDepartmentId;

    @ApiModelProperty(value = "最后更新人ID", example = "user002")
    private String updateUserId;

    @ApiModelProperty(value = "最后更新人姓名", example = "系统管理员")
    private String updateUserName;

    @ApiModelProperty(value = "详细地点")
    private String detailLocationName;

    @ApiModelProperty(value = "查出人单位类别",example = "集团，区域，煤矿")
    private DiscovererUnitsEnum discovererUnits;

}
