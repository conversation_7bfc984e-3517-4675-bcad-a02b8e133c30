package cn.pf.orch.meiye.exception;

import cn.genn.core.exception.MessageCodeWrap;

/**
 * 业务错误码定义
 * 从201-899
 *
 */
public enum MessageCode implements MessageCodeWrap {

    UPLOAD_FILE_ERROR("201","上传文件失败"),
    FILE_NOT_EXIST("202","文件不存在"),
    DOWNLOAD_FILE_ERROR("203","导出文件失败"),
    // MATE_DATE_ERROR("204","元数据不正确"),

    RESOURCE_STOP_EDIT("301","当前资源禁止编辑"),
    COMPANY_NAME_EXIST_ERROR("302","公司名称已存在"),
    COMPANY_NO_EXIST_ERROR("302","公司不存在"),

    AUTH_FAIL("401", "鉴权失败"),
    TOKEN_GET_ERROR("402", "未获取到token"),
    LOGIN_ERROR("403", "登录态异常"),
    TOKEN_INVALID("404", "token已失效,未获取到用户信息,请重新进行登录"),
    USER_INFO_ERROR("405","未获取到用户信息"),
    OPEN_ID_GET_ERROR("406","未获取到openId"),
    AUTH_TYPE_GET_ERROR("407","未获取到authType"),
    DEPARTMENT_ID_GET_ERROR("408","未获取到公司id"),
    DEPARTMENTS_ID_GET_ERROR("409","未获取到公司ids"),
    ROLE_NAME_EXIST_ERROR("410","角色已存在"),
    ROLE_NOT_EXIST_ERROR("411","角色不存在"),
    ROLE_SYSTEM_NOT_DELETE_ERROR("412","系统角色禁止删除"),
    ROLE_SYSTEM_NOT_UPDATE_ERROR("412","系统角色禁止编辑"),
    CHILDREN_DEPARTMENT_EXIST_ERROR("413","存在子公司，禁止操作"),
    USER_EXIST_ERROR("414","用户存在,禁止操作"),
    ROLE_USER_ERROR("415","角色已使用,禁止删除"),
    LOGIN_NO_AUTH_ERROR("416","无登录权限"),

    RISK_IDENTIFY_NAME_EXIST("501","风险辨识计划名称已存在"),
    RISK_EXIST_ERROR("502","计划下已有风险，无法删除"),
    RISK_NO_DELETED("503","风险禁止删除"),
    STATUS_NOT_MATCH_STOP_CLOSE("504","状态不符,禁止关闭"),
    STATUS_NOT_MATCH_STOP_CHECK("505","状态不符,禁止检查"),
    RISK_NOT_EXIST("506","风险不存在"),
    RISK_MEASURE_NO_EXIST("507","风险措施不存在"),
    STATUS_NOT_MATCH_STOP_NOTIFY("508","状态不符,禁止发送通知"),
    STATUS_NOT_MATCH_STOP_APPROVAL("509","状态不符,禁止提交审批"),

    HAZARD_IDENTIFY_NAME_EXIST("601","隐患排查名称已存在"),
    HAZARD_EXIST_ERROR("602","计划下已有隐患，无法删除"),
    HAZARD_NO_DELETED("603","隐患禁止删除"),
    HAZARD_NOT_EXIST("606","隐患不存在"),
    HAZARD_MEASURE_NO_EXIST("607","隐患措施不存在"),

    SECURITY_NO_EXIST("701","安全敏感信息不存在"),
    SECURITY_DEL("702","安全敏感信息已被删除"),

    ;

    private final String code;
    private final String description;

    MessageCode(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
