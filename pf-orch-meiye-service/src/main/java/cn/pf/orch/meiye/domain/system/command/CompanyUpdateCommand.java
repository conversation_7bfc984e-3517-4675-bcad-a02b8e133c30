package cn.pf.orch.meiye.domain.system.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class CompanyUpdateCommand {

    @ApiModelProperty(value = "公司id")
    @NotNull(message = "id不能为空")
    private Long id;

    @ApiModelProperty(value = "公司名称")
    private String name;

    @ApiModelProperty(value = "飞书部门id映射")
    private List<String> departmentIds;

    @ApiModelProperty(value = "许可证")
    private List<CompanyLicenseAddCommand> licenseList;
}
