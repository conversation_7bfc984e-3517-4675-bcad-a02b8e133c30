package cn.pf.orch.meiye.mapper;

import cn.pf.orch.meiye.domain.risk.dto.RiskCountDTO;
import cn.pf.orch.meiye.domain.risk.dto.RiskInfoDTO;
import cn.pf.orch.meiye.domain.risk.dto.RiskInfoPageDTO;
import cn.pf.orch.meiye.domain.risk.po.RiskInfoPO;
import cn.pf.orch.meiye.domain.risk.query.RiskInfoQuery;
import cn.pf.orch.meiye.domain.system.dto.HomeTodoDTO;
import cn.pf.orch.meiye.domain.system.query.HomePageQuery;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface RiskInfoMapper extends BaseMapper<RiskInfoPO> {

    IPage<RiskInfoPageDTO> selectByPage(IPage<RiskInfoPO> page, @Param("query") RiskInfoQuery query);

    List<RiskCountDTO> selectLevelCount(@Param("query") RiskInfoQuery query);

    default List<RiskInfoPO> selectByIdentifyIds(List<Long> ids) {
        LambdaQueryWrapper<RiskInfoPO> wrapper = Wrappers.lambdaQuery(RiskInfoPO.class)
                .in(RiskInfoPO::getRiskIdentifyId, ids);
        return this.selectList(wrapper);
    }

    default RiskInfoPO getMaxCode() {
        LambdaQueryWrapper<RiskInfoPO> wrapper = Wrappers.lambdaQuery(RiskInfoPO.class)
                .orderByDesc(RiskInfoPO::getCode)
                .last("limit 1");
        return selectOne(wrapper);
    }

    default RiskInfoPO selectByApprovalId(String approvalId){
        LambdaQueryWrapper<RiskInfoPO> wrapper = Wrappers.lambdaQuery(RiskInfoPO.class)
                .eq(RiskInfoPO::getApprovalId, approvalId)
                .last("limit 1");
        return selectOne(wrapper);
    }

    RiskInfoDTO selectDetailById(@Param("id") Long id);

    IPage<HomeTodoDTO> homeTodoPage(IPage<RiskInfoPO> page, @Param("query") HomePageQuery query);


}
