package cn.pf.orch.meiye.service.securitysensitive;

import cn.pf.orch.meiye.domain.risk.command.SendNotifyCommand;
import cn.pf.orch.meiye.domain.securitysensitive.command.AddCommand;
import cn.pf.orch.meiye.domain.securitysensitive.command.UpdateCommand;
import cn.pf.orch.meiye.domain.securitysensitive.dto.MeiyeSecuritySensitiveInfoDTO;
import cn.pf.orch.meiye.domain.securitysensitive.po.MeiyeSecuritySensitiveInfo;
import cn.pf.orch.meiye.domain.securitysensitive.query.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 安全敏感信息表(MeiyeSecuritySensitiveInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-13 15:32:27
 */
public interface MeiyeSecuritySensitiveInfoService extends IService<MeiyeSecuritySensitiveInfo> {


    void add(AddCommand addCommand);

    void delete(Integer id);

    void update(UpdateCommand command);

    Page<MeiyeSecuritySensitiveInfoDTO> pageQuery(PageQuery query);

    MeiyeSecuritySensitiveInfoDTO queryDetail(Integer id);

    Boolean sendNotify(SendNotifyCommand command);
}

