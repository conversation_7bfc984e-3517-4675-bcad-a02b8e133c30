package cn.pf.orch.meiye.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum CheckResultEnum {

    PASS(0,"通过"),
    NO_PASS(1,"未通过"),
    ;

    @EnumValue
    @JsonValue
    private final Integer code;

    private final String description;


    private static final Map<Integer, CheckResultEnum> VALUES = new HashMap<>();

    static {
        for (final CheckResultEnum item : CheckResultEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static CheckResultEnum of(Integer code) {
        return VALUES.get(code);
    }
}
