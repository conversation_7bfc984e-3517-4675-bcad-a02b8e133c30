package cn.pf.orch.meiye.domain.securitysensitive.command;

import cn.pf.orch.meiye.enums.SensitiveCategoryEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Date;

@Data
public class UpdateCommand {

    @ApiModelProperty(value = "主键ID", required = true, example = "1")
    @NotNull
    private Long id;

    @ApiModelProperty(value = "单位ID", required = true, example = "1001")
    private Long companyId;

    @ApiModelProperty(value = "单位名称", example = "XX科技有限公司")
    private String companyName;

    @ApiModelProperty(value = "安全敏感类别", required = true, example = "商业机密/个人隐私")
    private SensitiveCategoryEnum sensitiveCategory;

    @ApiModelProperty(value = "安全敏感描述（枚举）", required = true)
    private String sensitiveDescription;

    @ApiModelProperty(value = "管控措施", example = "加密存储，访问权限控制")
    private String controlMeasures;

    @ApiModelProperty(value = "责任部门", required = true, example = "信息安全部")
    private String responsibleDepartment;

    @ApiModelProperty(value = "责任部门Id")
    private String responsibleDepartmentId;

    @ApiModelProperty(value = "责任人姓名", required = true, example = "张三")
    private String responsiblePerson;

    @ApiModelProperty(value = "责任人工号", example = "EMP2023001")
    private String responsiblePersonNumber;

    @ApiModelProperty(value = "责任人OpenID", example = "oXZ8Y5W7...")
    private String responsiblePersonOpenId;

    @ApiModelProperty(value = "管控结果", example = "已落实三级等保要求")
    private String controlResult;

    @ApiModelProperty(value = "更新人ID", example = "U1001")
    private String updateUserId;

    @ApiModelProperty(value = "更新人姓名", example = "李四")
    private String updateUserName;
}
