package cn.pf.orch.meiye.domain.system.dto;

import cn.pf.orch.meiye.enums.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HomeTodoDTO {

    private Long bizId;

    private ApprovalBizTypeEnum bizType;

    private String code;

    @ApiModelProperty(value = "风险状态（待提交，待审核，已驳回，待关闭，已关闭）")
    private RiskStatusEnum status;

    @ApiModelProperty(value = "风险等级")
    private RiskLevelEnum level;

    @ApiModelProperty(value = "风险类别")
    private RiskTypeEnum type;

    private String remark;

    private String companyId;

    private String companyName;

    @ApiModelProperty("风险地址")
    private String addressName;

    @ApiModelProperty("全路径地址")
    private String fullAddressName;

    @ApiModelProperty("地址详情")
    private String addressDetail;

    @ApiModelProperty("可控状态")
    private ControlStatusEnum controlStatus;

    @ApiModelProperty("整改单位id")
    private String departmentId;

    @ApiModelProperty("整改单位名称")
    private String departmentName;

    @ApiModelProperty("整改责任人id")
    private String rectifyOwner;

    @ApiModelProperty("整改责任人名称")
    private String rectifyOwnerName;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "责任人")
    private String owner;

    @ApiModelProperty(value = "责任人名称")
    private String ownerName;

}
