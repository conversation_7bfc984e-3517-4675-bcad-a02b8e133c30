package cn.pf.orch.meiye.service.safetygenerationstandard;

import cn.pf.orch.meiye.domain.safetygenerationstandard.po.ScoringSystemDetailTemplatePO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.query.ScoredDetailTemplateQuery;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 煤矿管理项目详情表(ScoringSystemDetailTemplate)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-21 10:56:09
 */
public interface ScoringSystemDetailTemplateService extends IService<ScoringSystemDetailTemplatePO> {


    /**
     * 查询体系详情模板列表
     * @param query
     * @return
     */
    List<ScoringSystemDetailTemplatePO> selectDetailTemplateByCondition(ScoredDetailTemplateQuery query);


    /**
     * 根据主键查询
     * @param id
     * @return
     */
    ScoringSystemDetailTemplatePO selectById(Long id);

}

