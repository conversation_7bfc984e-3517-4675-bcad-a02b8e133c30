package cn.pf.orch.meiye.handler.strategy;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import cn.pf.orch.meiye.domain.feishu.FeishuApprovalEventCommand;
import cn.pf.orch.meiye.enums.feishu.FeishuInstanceStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;

@Service
@Slf4j
public abstract class ApprovalEventStrategy {

    public abstract String getApprovalCode();

    /**
     * 待处理
     *
     * @return
     */
    public boolean pending(FeishuApprovalEventCommand event) {
        return true;
    }

    /**
     * 通过处理
     *
     * @return
     */
    public boolean approved(FeishuApprovalEventCommand event) {
        return true;
    }
    /**
     * 驳回处理
     *
     * @return
     */
    public boolean rejected(FeishuApprovalEventCommand event) {
        return true;
    }
    /**
     * 中断处理
     *
     * @return
     */
    public boolean canceled(FeishuApprovalEventCommand event) {
        return true;
    }
    /**
     * 删除处理
     *
     * @return
     */
    public boolean deleted(FeishuApprovalEventCommand event) {
        return true;
    }

    /**
     * 转交状态
     * @param event
     * @return
     */
    public boolean transferred(FeishuApprovalEventCommand event){
        return true;
    }

    /**
     * 退回状态
     * @param event
     * @return
     */
    public boolean rollback(FeishuApprovalEventCommand event){
        return true;
    }

    public Boolean execute(FeishuApprovalEventCommand event) {
        LocalDateTime operateTime = StrUtil.isBlank(event.getOperateOldTime()) ? LocalDateTime.now() : LocalDateTimeUtil.of(Long.parseLong(event.getOperateOldTime()), ZoneOffset.of("+8"));
        event.setOperateTime(operateTime);
        switch (FeishuInstanceStatusEnum.of(event.getStatus())) {
            case PENDING:
                return pending(event);
            case APPROVED:
                return approved(event);
            case REJECTED:
                return rejected(event);
            case CANCELED:
                return canceled(event);
            case DELETED:
                return deleted(event);
            case TRANSFERRED:
                return transferred(event);
            case ROLLBACK:
                return rollback(event);
        }
        return true;
    }
}
