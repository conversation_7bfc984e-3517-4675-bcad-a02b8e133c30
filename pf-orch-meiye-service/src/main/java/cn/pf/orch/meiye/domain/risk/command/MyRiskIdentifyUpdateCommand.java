package cn.pf.orch.meiye.domain.risk.command;

import cn.pf.orch.meiye.enums.IdentifyRiskDetailEnum;
import cn.pf.orch.meiye.enums.IdentifyTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
public class MyRiskIdentifyUpdateCommand {

    @NotNull(message = "id不能为空")
    private Long id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "辨识类型")
    private IdentifyTypeEnum identifyType;

    @ApiModelProperty(value = "类别详情")
    private IdentifyRiskDetailEnum identifyDetail;

    @ApiModelProperty(value = "责任人")
    private String identifyOwner;

    @ApiModelProperty(value = "责任人名称")
    private String identifyOwnerName;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;
}
