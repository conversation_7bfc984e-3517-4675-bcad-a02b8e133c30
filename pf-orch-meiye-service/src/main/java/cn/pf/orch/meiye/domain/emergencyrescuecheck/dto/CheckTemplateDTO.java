package cn.pf.orch.meiye.domain.emergencyrescuecheck.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("检查项模块信息")
public class CheckTemplateDTO {

    @ApiModelProperty(value = "ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "检查类型", example = "安全专项检查")
    private String inspectionType;

    @ApiModelProperty(value = "项目名称", example = "消防设施季度检查")
    private String project;

    @ApiModelProperty(value = "检查内容", example = "灭火器压力、消防栓水压测试")
    private String content;

    @ApiModelProperty(value = "检查主要资料及方法", example = "现场检查、压力测试仪检测")
    private String materialsMethods;

    @ApiModelProperty(value = "依据链接")
    private String referenceLink;

     @ApiModelProperty(value = "序号")
    private Integer serialNumber;
}
