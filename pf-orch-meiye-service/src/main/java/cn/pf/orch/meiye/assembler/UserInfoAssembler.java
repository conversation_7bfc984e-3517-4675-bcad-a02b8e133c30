package cn.pf.orch.meiye.assembler;

import cn.pf.orch.feishu.model.ContactSearchUserResponse;
import cn.pf.orch.meiye.config.UserInfoDTO;
import cn.pf.orch.meiye.domain.system.dto.ContactSearchUserRespDTO;
import cn.pf.orch.meiye.domain.system.dto.FSUserInfoDTO;
import com.lark.oapi.service.authen.v1.model.GetUserInfoRespBody;
import com.lark.oapi.service.contact.v3.model.AvatarInfo;
import com.lark.oapi.service.contact.v3.model.User;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UserInfoAssembler {

    UserInfoDTO resp2DTO(GetUserInfoRespBody respBody);

    default FSUserInfoDTO user2DTO(User user){
        return FSUserInfoDTO.builder()
                .openId(user.getOpenId())
                .name(user.getName())
                .avatarUrl(Optional.of(user.getAvatar()).map(AvatarInfo::getAvatarOrigin).orElse(null))
                .build();
    }

    default List<FSUserInfoDTO> user2DTO(List<User> list){
        return list.stream().map(this::user2DTO).collect(Collectors.toList());
    }

    default ContactSearchUserRespDTO resp2DTO(ContactSearchUserResponse response){
        ContactSearchUserRespDTO dto = new ContactSearchUserRespDTO();
        dto.setHasMore(response.getHasMore());
        dto.setPageToken(response.getPageToken());
        List<ContactSearchUserRespDTO.User> users = response.getUsers().stream().map(userDTO -> {
            ContactSearchUserRespDTO.User user = new ContactSearchUserRespDTO.User();
            BeanUtils.copyProperties(userDTO, user);
            user.setAvatarUrl(userDTO.getAvatar().getAvatarOrigin());
            return user;
        }).collect(Collectors.toList());
        dto.setUsers(users);
        return dto;
    }

}
