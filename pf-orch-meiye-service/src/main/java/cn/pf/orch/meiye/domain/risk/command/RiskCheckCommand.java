package cn.pf.orch.meiye.domain.risk.command;

import cn.pf.orch.meiye.enums.CheckLevelEnum;
import cn.pf.orch.meiye.enums.CheckResultEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class RiskCheckCommand {

    @ApiModelProperty(value = "风险措施id")
    @NotNull(message = "风险措施id不能为空")
    private Long riskMeasureId;

    @ApiModelProperty(value = "检查层级")
    @NotNull(message = "检查层级不能为空")
    private CheckLevelEnum checkLevel;

    @ApiModelProperty(value = "检查结果")
    @NotNull(message = "检查结果不能为空")
    private CheckResultEnum checkResult;

    @ApiModelProperty(value = "检查详情")
    @NotBlank(message = "检查结果不能为空")
    private String detail;

    @ApiModelProperty(value = "附件")
    private List<String> files;
}
