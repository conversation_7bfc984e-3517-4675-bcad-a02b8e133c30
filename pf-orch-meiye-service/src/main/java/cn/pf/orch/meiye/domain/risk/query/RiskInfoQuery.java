package cn.pf.orch.meiye.domain.risk.query;

import cn.genn.core.model.page.PageSortQuery;
import cn.pf.orch.meiye.enums.ControlStatusEnum;
import cn.pf.orch.meiye.enums.RiskLevelEnum;
import cn.pf.orch.meiye.enums.RiskStatusEnum;
import cn.pf.orch.meiye.enums.RiskTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * RiskInfo查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RiskInfoQuery extends PageSortQuery implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "风险编号")
    private String code;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "风险辨识计划")
    private String identifyName;

    @ApiModelProperty(value = "风险等级")
    private RiskLevelEnum level;

    @ApiModelProperty(value = "风险类别")
    private RiskTypeEnum riskType;

    @ApiModelProperty(value = "风险状态")
    private RiskStatusEnum riskStatus;

    @ApiModelProperty(value = "可控状态")
    private ControlStatusEnum controlStatus;

    @ApiModelProperty(value = "风险责任人")
    private String ownerName;

    @ApiModelProperty(value = "风险辨识开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "风险辨识结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    private List<Long> companyIds;

    @ApiModelProperty("描述")
    private String remark;




}

