package cn.pf.orch.meiye.assembler;

import cn.genn.core.model.page.PageResultDTO;
import cn.pf.orch.meiye.domain.hazard.dto.HazardCheckDTO;
import cn.pf.orch.meiye.domain.hazard.dto.HazardCheckPageDTO;
import cn.pf.orch.meiye.domain.hazard.po.HazardCheckPO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.Arrays;
import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface HazardCheckAssembler {
    @Mapping(target = "files", expression = "java(stringToList(po.getFiles()))")
    HazardCheckDTO PO2DTO(HazardCheckPO po);

    @Mapping(target = "pageNo", source = "current")
    @Mapping(target = "pageSize", source = "size")
    @Mapping(target = "list", source = "records")
    @Mapping(target = "totalPages", source = "pages")
    PageResultDTO<HazardCheckPageDTO> toPage(IPage<HazardCheckPageDTO> poPage);

    default List<String> stringToList(String departmentIds) {
        if (departmentIds == null || departmentIds.isEmpty()) {
            return null;
        }
        return Arrays.asList(departmentIds.split(","));
    }
}
