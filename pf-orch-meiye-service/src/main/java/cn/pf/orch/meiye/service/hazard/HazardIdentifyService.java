package cn.pf.orch.meiye.service.hazard;

import cn.genn.core.model.page.PageResultDTO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.pf.orch.meiye.assembler.HazardIdentifyAssembler;
import cn.pf.orch.meiye.config.CurrentUserHolder;
import cn.pf.orch.meiye.domain.hazard.command.MyHazardIdentifySaveCommand;
import cn.pf.orch.meiye.domain.hazard.command.MyHazardIdentifyUpdateCommand;
import cn.pf.orch.meiye.domain.hazard.dto.HazardIdentifyDTO;
import cn.pf.orch.meiye.domain.hazard.po.HazardIdentifyPO;
import cn.pf.orch.meiye.domain.hazard.po.HazardInfoPO;
import cn.pf.orch.meiye.domain.hazard.query.HazardIdentifyQuery;
import cn.pf.orch.meiye.domain.system.po.MyCompanyPO;
import cn.pf.orch.meiye.enums.IdentifyStatusEnum;
import cn.pf.orch.meiye.mapper.HazardIdentifyMapper;
import cn.pf.orch.meiye.mapper.HazardInfoMapper;
import cn.pf.orch.meiye.mapper.MyCompanyMapper;
import cn.pf.orch.meiye.mapper.MyUserMapper;
import cn.pf.orch.meiye.processor.HazardIdentifyProcessor;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class HazardIdentifyService {

    @Resource
    private HazardIdentifyMapper hazardIdentifyMapper;
    @Resource
    private HazardIdentifyProcessor hazardIdentifyProcessor;
    @Resource
    private HazardIdentifyAssembler assembler;
    @Resource
    private MyCompanyMapper myCompanyMapper;
    @Resource
    private HazardInfoMapper hazardInfoMapper;
    @Resource
    private MyUserMapper myUserMapper;

    public PageResultDTO<HazardIdentifyDTO> page(HazardIdentifyQuery query) {
        //权限
        query.setCompanyIds(CurrentUserHolder.getRangeCompanyIds());
        PageResultDTO<HazardIdentifyDTO> pageResult = assembler.toPage(hazardIdentifyMapper.selectByPage(new Page<>(query.getPageNo(), query.getPageSize()), query));
        if(CollUtil.isNotEmpty(pageResult.getList())){
            List<Long> ids = pageResult.getList().stream().map(HazardIdentifyDTO::getId).collect(Collectors.toList());
            List<HazardInfoPO> hazardInfoPOS = hazardInfoMapper.selectByIdentifyIds(ids);
            Map<Long, List<HazardInfoPO>> hazardInfoMap = hazardInfoPOS.stream().collect(Collectors.groupingBy(HazardInfoPO::getHazardIdentifyId));
            for (HazardIdentifyDTO hazardIdentifyDTO : pageResult.getList()) {
                List<HazardInfoPO> hazardInfoPOList = hazardInfoMap.get(hazardIdentifyDTO.getId());
                hazardIdentifyDTO.setHazardCount((long) (CollUtil.isNotEmpty(hazardInfoPOList)?hazardInfoPOList.size():0));
            }
        }
        return pageResult;
    }

    public HazardIdentifyDTO get(Long id) {
        HazardIdentifyPO po = hazardIdentifyMapper.selectById(id);
        HazardIdentifyDTO dto = assembler.PO2DTO(po);
        MyCompanyPO companyPO = myCompanyMapper.selectById(po.getCompanyId());
        dto.setCompanyName(companyPO.getName());
        return dto;
    }

    public Boolean save(MyHazardIdentifySaveCommand command) {
        hazardIdentifyProcessor.saveCheck(command);
        //补充责任人
        command.setIdentifyOwnerName(myUserMapper.selectById(command.getIdentifyOwner()).getName());
        HazardIdentifyPO po = assembler.command2PO(command);
        po.setStatus(IdentifyStatusEnum.getStatus(po.getStartTime(), po.getEndTime()));
        hazardIdentifyMapper.insert(po);
        return true;
    }

    public Boolean change(MyHazardIdentifyUpdateCommand command) {
        hazardIdentifyProcessor.changeCheck(command);
        if(StrUtil.isNotBlank(command.getIdentifyOwner())){
            command.setIdentifyOwnerName(myUserMapper.selectById(command.getIdentifyOwner()).getName());
        }
        HazardIdentifyPO po = assembler.updateCommand2PO(command);
        if(ObjUtil.isNotNull(command.getStartTime()) && ObjUtil.isNotNull(command.getEndTime())){
            po.setStatus(IdentifyStatusEnum.getStatus(po.getStartTime(), po.getEndTime()));
        }
        hazardIdentifyMapper.updateById(po);
        return true;
    }

    public Boolean batchRemove(List<Long> ids) {
        hazardIdentifyProcessor.deleteCheck(ids);
        hazardIdentifyMapper.deleteBatchIds(ids);
        return true;
    }

    public List<HazardIdentifyDTO> list() {
        List<Long> companyIds = CurrentUserHolder.getRangeCompanyIds();
        return assembler.PO2DTO(hazardIdentifyMapper.selectByCompanyIds(companyIds));
    }

}
