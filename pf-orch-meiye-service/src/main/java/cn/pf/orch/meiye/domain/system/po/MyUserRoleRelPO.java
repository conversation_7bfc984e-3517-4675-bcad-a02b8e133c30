package cn.pf.orch.meiye.domain.system.po;

import cn.pf.orch.meiye.enums.RoleTypeEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * MyUserRoleRelPO对象
 *
 * <AUTHOR>
 * @desc 用户角色关联表
 */
@Data
@Accessors(chain = true)
@TableName(value = "my_user_role_rel", autoResultMap = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MyUserRoleRelPO {

    /**
     * 主键自增
     */
    @TableId
    private Long id;

    /**
     * 用户id
     */
    @TableField("user_id")
    private String userId;

    /**
     * 角色id
     */
    @TableField("role_id")
    private Long roleId;

    /**
     * 角色类型
     */
    @TableField("role_type")
    private RoleTypeEnum roleType;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;

    /**
     * 创建用户名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

}

