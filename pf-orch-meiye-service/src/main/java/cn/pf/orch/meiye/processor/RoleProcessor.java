package cn.pf.orch.meiye.processor;

import cn.genn.core.exception.BusinessException;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.pf.orch.meiye.domain.system.command.MyRoleSaveCommand;
import cn.pf.orch.meiye.domain.system.command.MyRoleUpdateCommand;
import cn.pf.orch.meiye.domain.system.po.MyRolePO;
import cn.pf.orch.meiye.enums.RoleTypeEnum;
import cn.pf.orch.meiye.exception.MessageCode;
import cn.pf.orch.meiye.mapper.MyRoleMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Component
public class RoleProcessor {

    @Resource
    private MyRoleMapper roleMapper;

    public void checkSave(MyRoleSaveCommand command){
        //角色name校验
        if(StrUtil.isNotBlank(command.getName())){
            List<MyRolePO> rolePOList = roleMapper.selectByName(command.getName());
            if(CollectionUtil.isNotEmpty(rolePOList)){
                throw new BusinessException(MessageCode.ROLE_NAME_EXIST_ERROR);
            }
        }
    }

    public void checkChange(MyRoleUpdateCommand command){
        MyRolePO myRolePO = roleMapper.selectById(command.getId());
        if(Objects.isNull(myRolePO)){
            throw new BusinessException(MessageCode.ROLE_NOT_EXIST_ERROR);
        }
        if(RoleTypeEnum.SYSTEM.equals(myRolePO.getType())){
            throw new BusinessException(MessageCode.ROLE_SYSTEM_NOT_UPDATE_ERROR);
        }
        //角色name校验
        if(StrUtil.isNotBlank(command.getName())){
            List<MyRolePO> rolePOList = roleMapper.selectByName(command.getName());
            boolean exist = rolePOList.stream().noneMatch(role -> role.getId().equals(myRolePO.getId()));
            if(CollectionUtil.isNotEmpty(rolePOList) && exist){
                throw new BusinessException(MessageCode.ROLE_NAME_EXIST_ERROR);
            }
        }
    }
}
