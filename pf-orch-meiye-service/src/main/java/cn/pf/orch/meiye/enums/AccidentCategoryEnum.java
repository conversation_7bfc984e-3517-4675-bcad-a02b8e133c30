package cn.pf.orch.meiye.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 事故类别
 */
@Getter
@AllArgsConstructor
public enum AccidentCategoryEnum {


    /** 燃爆事故 */
    EXPLOSION("1","燃爆事故"),

    /** 伤亡事故 */
    CASUALTY("2","伤亡事故"),

    /** 生产运行事故 */
    PRODUCTION("3","生产运行事故"),

    /** 设备事故 */
    EQUIPMENT("4","设备事故"),

    /** 自然灾害 */
    NATURAL_DISASTER("5","自然灾害"),

    /** 未遂事故 */
    NEAR_MISS("6","未遂事故"),

    /** 涉险事故 */
    HAZARD("7", "涉险事故");

    @EnumValue
    @JsonValue
    private String code;

    private String desc;

}
