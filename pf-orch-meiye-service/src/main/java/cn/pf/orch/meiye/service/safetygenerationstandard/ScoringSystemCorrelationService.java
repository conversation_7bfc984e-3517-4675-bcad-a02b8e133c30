package cn.pf.orch.meiye.service.safetygenerationstandard;

import cn.pf.orch.meiye.domain.safetygenerationstandard.command.InitCompanyScoredSystemCommand;
import cn.pf.orch.meiye.domain.safetygenerationstandard.command.ScoredUpdateCommand;
import cn.pf.orch.meiye.domain.safetygenerationstandard.dto.ScoringSystemCorrelationDTO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.po.ScoringSystemCorrelationPO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.query.ScoringSystemQuery;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 管理项-评分记录中间表(ScoringSystemCorrelation)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-22 16:52:44
 */
public interface ScoringSystemCorrelationService extends IService<ScoringSystemCorrelationPO> {


    /**
     * 查询矿井评分体系
     * @param query
     * @return
     */
    List<ScoringSystemCorrelationDTO> queryScoredSystem(ScoringSystemQuery query);


    /**
     * 初始化煤矿的体系评分信息
     * 触发点：新增煤矿安全生产标准化信息时
     * @param command
     */
    void initCompanyScoredSystem(InitCompanyScoredSystemCommand command);


    /**
     * 更新煤矿管理体系的各项评分
     * 触发点：管理项下各个模板评分后更新管理项的评分
     */
    void scoredSystem(ScoredUpdateCommand command);

    void deleteByScoringRecordId(Long scoringRecordId);

}

