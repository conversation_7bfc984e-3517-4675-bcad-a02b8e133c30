package cn.pf.orch.meiye.domain.safetyaccident.dto;

import cn.pf.orch.meiye.enums.AccidentCategoryEnum;
import cn.pf.orch.meiye.enums.AccidentNature;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ApiModel("事故信息信息")
public class MeiyeSafetyAccidentDTO {

    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "煤矿ID", example = "1001")
    private Integer companyId;

    @ApiModelProperty(value = "煤矿名称", example = "某某煤矿")
    private String companyName;

    @ApiModelProperty(value = "事故性质", example = "责任事故")
    private AccidentNature accidentNature;

    @ApiModelProperty(value = "事故类别", example = "顶板事故")
    private AccidentCategoryEnum accidentCategory;

    @ApiModelProperty(value = "事故名称", example = "井下冒顶事故")
    private String accidentName;

    @ApiModelProperty(value = "发生时间", example = "2023-05-15 14:30:00")
    private LocalDateTime occurrenceTime;

    @ApiModelProperty(value = "上报时间", example = "2023-05-15 15:00:00")
    private LocalDateTime reportTime;

//    @ApiModelProperty(value = "时间描述", example = "事故发生于早班作业期间")
//    private String timeDescription;

    @ApiModelProperty(value = "事故地点ID", example = "2001")
    private Long locationId;

    @ApiModelProperty(value = "地点名称", example = "井下3号工作面")
    private String locationName;

    @ApiModelProperty(value = "地点全路径")
    private String locationFullPathName;

    @ApiModelProperty(value = "事故责任人", example = "张三")
    private String responsiblePerson;

    @ApiModelProperty(value = "责任人工号", example = "EMP1001")
    private String responsiblePersonNumber;

    @ApiModelProperty(value = "责任人OpenID", example = "abcd1234")
    private String responsiblePersonOpenId;

    @ApiModelProperty(value = "死亡人数", example = "2")
    private Integer deathToll;

    @ApiModelProperty(value = "重伤人数", example = "3")
    private Integer seriousInjury;

    @ApiModelProperty(value = "轻伤人数", example = "5")
    private Integer minorInjury;

    @ApiModelProperty(value = "经济损失（元）", example = "500000.00")
    private BigDecimal economicLoss;

    @ApiModelProperty(value = "创建人", example = "admin")
    private String createUserName;

    @ApiModelProperty(value = "更新人", example = "admin")
    private String updateUserName;

    @ApiModelProperty(value = "创建时间", example = "2023-05-15 16:00:00")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间", example = "2023-05-16 10:00:00")
    private LocalDateTime updateTime;
}
