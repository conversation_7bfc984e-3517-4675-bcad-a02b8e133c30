package cn.pf.orch.meiye.domain.safetyaccident.po;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import cn.pf.orch.meiye.enums.AccidentCategoryEnum;
import cn.pf.orch.meiye.enums.AccidentNature;
import cn.pf.orch.meiye.enums.DelEnum;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

/**
 * 安全事故记录表(MeiyeSafetyAccidentRecords)表实体类
 *
 * <AUTHOR>
 * @since 2025-04-13 19:28:18
 */
@Data
@TableName("meiye_safety_accident_records")
public class MeiyeSafetyAccidentRecords extends Model<MeiyeSafetyAccidentRecords> {
    //主键ID
    private Long id;
    //煤矿id
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private Long companyId;
    //煤矿名称
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String companyName;
    //事故性质
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private AccidentNature accidentNature;
    //事故类别
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private AccidentCategoryEnum accidentCategory;
    //事故名称
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String accidentName;
    //发生时间
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private LocalDateTime occurrenceTime;
    //上报时间
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private LocalDateTime reportTime;
//    //时间描述
//    private String timeDescription;
    //事故地点
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private Long locationId;
    //事故责任人
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String responsiblePerson;
    //事故责任人工号
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String responsiblePersonNumber;
    //责任人openid
    @TableField(value = "responsible_person_openId", updateStrategy = FieldStrategy.NOT_NULL)
    private String responsiblePersonOpenId;
    //死亡人数
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private Integer deathToll;
    //重伤人数
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private Integer seriousInjury;
    //轻伤人数
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private Integer minorInjury;
    //经济损失(元)
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private BigDecimal economicLoss;
    // 是否删除
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private DelEnum del;

    //创建人id
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;
    //创建人
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;
    //创建时间
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    //更新人id
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;
    //更新人
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;
    //更新时间
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}

