package cn.pf.orch.meiye.mapper;

import cn.genn.core.model.enums.DeletedEnum;
import cn.hutool.core.util.ObjUtil;
import cn.pf.orch.meiye.domain.system.dto.MyUserDTO;
import cn.pf.orch.meiye.domain.system.po.MyUserPO;
import cn.pf.orch.meiye.domain.system.query.MyUserPageQuery;
import cn.pf.orch.meiye.enums.ApprovalSignEnum;
import cn.pf.orch.meiye.enums.UserTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MyUserMapper extends BaseMapper<MyUserPO> {


    int insertBatch(List<MyUserPO> list);

    default List<MyUserPO> selectByCompanyId(Long companyId, UserTypeEnum userType) {
        return selectList(new LambdaQueryWrapper<MyUserPO>().eq(MyUserPO::getCompanyId, companyId).eq(ObjUtil.isNotEmpty(userType),MyUserPO::getType,userType));
    }

    default List<MyUserPO> selectByCompanyIds(List<Long> companyIds, UserTypeEnum userType,String name) {
        return selectList(new LambdaQueryWrapper<MyUserPO>()
                .in(MyUserPO::getCompanyId, companyIds)
                .eq(ObjUtil.isNotEmpty(userType),MyUserPO::getType,userType)
                .eq(MyUserPO::getDeleted, DeletedEnum.NOT_DELETED)
                .like(ObjUtil.isNotEmpty(name), MyUserPO::getName, name));
    }

    IPage<MyUserDTO> queryList(IPage<MyUserPO> page,@Param("query") MyUserPageQuery query, @Param("companyIds") List<Long> companyIds);

    void deleteByIds(List<String> idList);

    default void updateApprovalByIds(List<String> userIds, ApprovalSignEnum approvalSign) {
        update(Wrappers.lambdaUpdate(MyUserPO.class)
                .set(MyUserPO::getApprovalSign, approvalSign)
                .in(MyUserPO::getId, userIds));
    }

    default List<MyUserPO> selectApprovalSignAndCompanyId(ApprovalSignEnum approvalSign, Long companyId){
        return selectList(Wrappers.lambdaQuery(MyUserPO.class)
                .eq(MyUserPO::getApprovalSign, approvalSign)
                .eq(MyUserPO::getCompanyId, companyId));
    }
}
