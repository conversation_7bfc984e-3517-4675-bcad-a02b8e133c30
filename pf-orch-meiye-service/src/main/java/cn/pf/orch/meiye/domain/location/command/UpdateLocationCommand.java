package cn.pf.orch.meiye.domain.location.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class UpdateLocationCommand {

    @ApiModelProperty(value = "id")
    @NotNull
    private Long id;
    @ApiModelProperty(value = "新地点名称")
    private String newLocationName;
    @ApiModelProperty(value = "当前登录人id")
    private String updateUserId;
    @ApiModelProperty(value = "当前登录人名称")
    private String updateUserName;
}
