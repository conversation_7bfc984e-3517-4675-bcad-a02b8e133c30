package cn.pf.orch.meiye.domain.system.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
public class MyRoleUpdateCommand {

    @ApiModelProperty(value = "主键", required = true)
    @NotNull(message = "系统id不能为空")
    private Long id;

    @ApiModelProperty(value = "名称")
    @Size(max = 32, message = "名称最大长度不能超过32")
    private String name;



}
