package cn.pf.orch.meiye.processor;

import cn.genn.core.exception.BusinessException;
import cn.hutool.core.util.ObjUtil;
import cn.pf.orch.meiye.domain.hazard.command.HazardCheckCommand;
import cn.pf.orch.meiye.domain.hazard.po.HazardInfoPO;
import cn.pf.orch.meiye.domain.hazard.po.HazardMeasurePO;
import cn.pf.orch.meiye.enums.RiskStatusEnum;
import cn.pf.orch.meiye.exception.MessageCode;
import cn.pf.orch.meiye.mapper.HazardInfoMapper;
import cn.pf.orch.meiye.mapper.HazardMeasureMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class HazardCheckProcessor {

    @Resource
    private HazardMeasureMapper hazardMeasureMapper;
    @Resource
    private HazardInfoMapper hazardInfoMapper;

    public HazardMeasurePO check(HazardCheckCommand command){
        HazardMeasurePO hazardMeasurePO = hazardMeasureMapper.selectById(command.getHazardMeasureId());
        if(ObjUtil.isEmpty(hazardMeasurePO)){
            throw new BusinessException(MessageCode.HAZARD_MEASURE_NO_EXIST);
        }
        HazardInfoPO hazardInfoPO = hazardInfoMapper.selectById(hazardMeasurePO.getHazardId());
        if(ObjUtil.isEmpty(hazardInfoPO)){
            throw new BusinessException(MessageCode.HAZARD_NOT_EXIST);
        }
        if(!hazardInfoPO.getStatus().equals(RiskStatusEnum.NO_CLOSE)){
            throw new BusinessException(MessageCode.STATUS_NOT_MATCH_STOP_CHECK);
        }
        return hazardMeasurePO;
    }
}
