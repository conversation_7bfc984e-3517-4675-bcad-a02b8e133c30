package cn.pf.orch.meiye.domain.securitysensitive.po;

import java.time.LocalDateTime;

import cn.pf.orch.meiye.enums.DelEnum;
import cn.pf.orch.meiye.enums.SensitiveCategoryEnum;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

/**
 * 安全敏感信息表(MeiyeSecuritySensitiveInfo)表实体类
 *
 * <AUTHOR>
 * @since 2025-04-13 15:32:27
 */
@TableName("meiye_security_sensitive_info")
@Data
public class MeiyeSecuritySensitiveInfo extends Model<MeiyeSecuritySensitiveInfo> {
    //主键ID
    private Long id;
    //单位id
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private Long companyId;
    // 单位名称
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String companyName;
    //安全敏感类别
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private SensitiveCategoryEnum sensitiveCategory;
    //安全敏感描述
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String sensitiveDescription;
    //管控措施
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String controlMeasures;
    //责任部门
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String responsibleDepartment;
    //责任部门Id
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String responsibleDepartmentId;
    //责任人
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String responsiblePerson;
    //责任人工号
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String responsiblePersonNumber;
    //责任人openid
    @TableField(value = "responsible_person_openId", updateStrategy = FieldStrategy.NOT_NULL)
    private String responsiblePersonOpenId;
    //管控结果
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String controlResult;
    // 是否删除
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private DelEnum del;
    //创建人id
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;
    //创建人
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;
    //创建时间
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    //更新人id
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;
    //更新人
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;
    //更新时间
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}

