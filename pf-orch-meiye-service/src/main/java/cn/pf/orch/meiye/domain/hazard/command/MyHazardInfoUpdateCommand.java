package cn.pf.orch.meiye.domain.hazard.command;

import cn.pf.orch.meiye.enums.ControlStatusEnum;
import cn.pf.orch.meiye.enums.RiskLevelEnum;
import cn.pf.orch.meiye.enums.RiskTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class MyHazardInfoUpdateCommand {

    private Long id;

    @ApiModelProperty(value = "隐患排查id")
    private Long hazardIdentifyId;

    @ApiModelProperty(value = "隐患等级")
    private RiskLevelEnum level;

    @ApiModelProperty(value = "隐患类别")
    private RiskTypeEnum type;

    @ApiModelProperty(value = "可控状态（0可控，1失控）")
    private ControlStatusEnum controlStatus;

    @ApiModelProperty(value = "隐患责任人")
    private String owner;

    @ApiModelProperty(value = "隐患责任人名称")
    private String ownerName;

    @ApiModelProperty(value = "描述")
    private String remark;

    @ApiModelProperty("地址id")
    private Long addressId;

    @ApiModelProperty("地址详情")
    private String addressDetail;

    @ApiModelProperty("整改单位id")
    private String departmentId;

    @ApiModelProperty("整改单位名称")
    private String departmentName;

    @ApiModelProperty("整改责任人id")
    private String rectifyOwner;

    @ApiModelProperty("整改责任人名称")
    private String rectifyOwnerName;

    @ApiModelProperty("整改期限")
    private LocalDateTime rectifyTime;

    @ApiModelProperty("整改资金(元)")
    @DecimalMin(value = "0.00", message = "整改资金不能小于0")
    @DecimalMax(value = "100000000", message = "整改资金不能大于1亿")
    private BigDecimal rectifyMoney;
}
