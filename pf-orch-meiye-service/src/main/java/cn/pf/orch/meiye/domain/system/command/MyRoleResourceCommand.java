package cn.pf.orch.meiye.domain.system.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class MyRoleResourceCommand {

    @ApiModelProperty(value = "角色id")
    @NotNull(message = "roleId不能为空")
    private Long roleId;

    @ApiModelProperty(value = "资源id")
    private List<Long> resourceIdList;
}
