package cn.pf.orch.meiye.assembler;

import cn.pf.orch.meiye.domain.risk.dto.MyApprovalLogDTO;
import cn.pf.orch.meiye.domain.risk.po.MyApprovalLogPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ApprovalLogAssembler {

    MyApprovalLogDTO PO2DTO(MyApprovalLogPO po);

    List<MyApprovalLogDTO> PO2DTO(List<MyApprovalLogPO> po);

}
