package cn.pf.orch.meiye.service.risk;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.pf.orch.feishu.FeishuAppClient;
import cn.pf.orch.feishu.model.ApprovalInstanceModel;
import cn.pf.orch.feishu.model.InstanceFormModel;
import cn.pf.orch.feishu.model.enums.InstanceFormTypeEnum;
import cn.pf.orch.meiye.assembler.ApprovalLogAssembler;
import cn.pf.orch.meiye.assembler.RiskInfoAssembler;
import cn.pf.orch.meiye.assembler.RiskMeasureAssembler;
import cn.pf.orch.meiye.config.CurrentUserHolder;
import cn.pf.orch.meiye.domain.feishu.SendRiskDTO;
import cn.pf.orch.meiye.domain.risk.command.*;
import cn.pf.orch.meiye.domain.risk.dto.RiskCountDTO;
import cn.pf.orch.meiye.domain.risk.dto.RiskInfoDTO;
import cn.pf.orch.meiye.domain.risk.dto.RiskInfoPageDTO;
import cn.pf.orch.meiye.domain.risk.dto.RiskMeasureDTO;
import cn.pf.orch.meiye.domain.risk.po.*;
import cn.pf.orch.meiye.domain.risk.query.RiskInfoQuery;
import cn.pf.orch.meiye.domain.system.po.MyCompanyPO;
import cn.pf.orch.meiye.domain.system.po.MyUserPO;
import cn.pf.orch.meiye.enums.*;
import cn.pf.orch.meiye.enums.feishu.FeishuInstanceStatusEnum;
import cn.pf.orch.meiye.exception.MessageCode;
import cn.pf.orch.meiye.mapper.*;
import cn.pf.orch.meiye.processor.RiskProcessor;
import cn.pf.orch.meiye.properties.ApprovalProperties;
import cn.pf.orch.meiye.properties.MeiyeSeverProperties;
import cn.pf.orch.meiye.service.CardSendActionService;
import cn.pf.orch.meiye.utils.FsNotifyUrlUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lark.oapi.service.approval.v4.model.ApprovalNodeInfo;
import com.lark.oapi.service.approval.v4.model.GetApprovalRespBody;
import com.lark.oapi.service.approval.v4.model.NodeApprover;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RiskService {

    @Resource
    private MeiyeSeverProperties meiyeSeverProperties;
    @Resource
    private FeishuAppClient feishuAppClient;
    @Resource
    private RiskInfoMapper riskInfoMapper;
    @Resource
    private MyApprovalMapper myApprovalMapper;
    @Resource
    private CardSendActionService cardSendActionService;
    @Resource
    private RiskInfoAssembler assembler;
    @Resource
    private RiskProcessor riskProcessor;
    @Resource
    private MyAddressRelMapper myAddressRelMapper;
    @Resource
    private RiskMeasureMapper riskMeasureMapper;
    @Resource
    private RiskMeasureAssembler riskMeasureAssembler;
    @Resource
    private MyApprovalLogMapper myApprovalLogMapper;
    @Resource
    private ApprovalLogAssembler approvalLogAssembler;
    @Resource
    private MyUserMapper myUserMapper;
    @Resource
    private MyCompanyMapper myCompanyMapper;

    private final static String NOTIFY_TEMPLATE = "所属煤矿: {}\n风险等级: {}\n风险编号: {}\n风险类别: {}\n是否可控: {}\n辨识计划名称: {}\n辨识类型: {}\n辨识日期: {}\n风险地点: {}\n详细地点: {}\n责任人: {}\n风险描述: {}\n风险措施: {}";

    // 定义日期格式
    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");

    public PageResultDTO<RiskInfoPageDTO> page(RiskInfoQuery query) {
        // 权限
        query.setCompanyIds(CurrentUserHolder.getRangeCompanyIds());
        PageResultDTO<RiskInfoPageDTO> pageResult = assembler.toPage(riskInfoMapper.selectByPage(new Page<>(query.getPageNo(), query.getPageSize()), query));
        return pageResult;
    }

    public List<RiskCountDTO> count(RiskInfoQuery query) {
        // 权限
        query.setCompanyIds(CurrentUserHolder.getRangeCompanyIds());
        List<RiskCountDTO> riskCountDTOS = riskInfoMapper.selectLevelCount(query);
        Map<RiskLevelEnum, RiskCountDTO> map = riskCountDTOS.stream().collect(Collectors.toMap(RiskCountDTO::getLevel, Function.identity()));
        List<RiskCountDTO> riskCountDTOList = new ArrayList<>();
        for (RiskLevelEnum value : RiskLevelEnum.values()) {
            RiskCountDTO riskCountDTO = map.get(value);
            if (riskCountDTO == null) {
                riskCountDTO = RiskCountDTO.builder().level(value).levelName(value.getDescription()).count(0).build();
            }
            riskCountDTO.setLevelName(value.getDescription());
            riskCountDTOList.add(riskCountDTO);
        }
        return riskCountDTOList;
    }

    public RiskInfoDTO get(Long id) {
        RiskInfoDTO riskInfoDTO = riskInfoMapper.selectDetailById(id);
        // 措施
        List<RiskMeasurePO> measureDTOS = riskMeasureMapper.selectByRiskId(id);
        if (CollUtil.isNotEmpty(measureDTOS)) {
            riskInfoDTO.setRiskMeasureList(riskMeasureAssembler.PO2DTO(measureDTOS));
        }
        // 审核信息
        if (StrUtil.isNotBlank(riskInfoDTO.getApprovalId())) {
            List<MyApprovalLogPO> approvalLogPOS = myApprovalLogMapper.selectByApprovalId(riskInfoDTO.getApprovalId());
            riskInfoDTO.setRiskApprovalList(approvalLogAssembler.PO2DTO(approvalLogPOS));
        }
        riskInfoDTO.setCheckLevel(CheckLevelEnum.getByAuthType(CurrentUserHolder.getAuthType()));
        return riskInfoDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    public Long save(MyRiskInfoSaveCommand command) {
        RiskInfoPO po = assembler.saveCommand2PO(command);
        //补充责任人
        command.setOwnerName(myUserMapper.selectById(command.getOwner()).getName());
        // 风险编号
        po.setCode(this.generateCode());
        po.setStatus(RiskStatusEnum.WAIT_SUBMIT);
        riskInfoMapper.insert(po);
        // 地址关联
        myAddressRelMapper.insert(MyAddressRelPO.builder()
                .addressId(command.getAddressId())
                .addressDetail(command.getAddressDetail())
                .bizId(po.getId())
                .bizType(ApprovalBizTypeEnum.RISK)
                .companyId(po.getCompanyId())
                .build());
        return po.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public Long change(MyRiskInfoUpdateCommand command) {
        riskProcessor.changeCheck(command);
        if(StrUtil.isNotBlank(command.getOwner())){
            command.setOwnerName(myUserMapper.selectById(command.getOwner()).getName());
        }
        RiskInfoPO po = assembler.updateCommand2PO(command);
        riskInfoMapper.updateById(po);
        // 地址关联
        if (ObjUtil.isNotNull(command.getAddressId()) && StrUtil.isNotEmpty(command.getAddressDetail())) {
            myAddressRelMapper.deleteByBiz(ApprovalBizTypeEnum.RISK, Arrays.asList(po.getId()));
            myAddressRelMapper.insert(MyAddressRelPO.builder()
                    .addressId(command.getAddressId())
                    .addressDetail(command.getAddressDetail())
                    .bizId(po.getId())
                    .bizType(ApprovalBizTypeEnum.RISK)
                    .companyId(po.getCompanyId())
                    .build());
        }
        return command.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean batchRemove(List<Long> idList) {
        riskProcessor.deleteCheck(idList);
        riskInfoMapper.deleteBatchIds(idList);
        myAddressRelMapper.deleteByBiz(ApprovalBizTypeEnum.RISK, idList);
        return true;
    }

    public Boolean saveRiskMeasure(RiskMeasureSaveCommand command) {
        RiskMeasurePO po = riskMeasureAssembler.saveCommand2PO(command);
        RiskInfoPO riskInfoPO = riskInfoMapper.selectById(command.getRiskId());
        po.setCompanyId(riskInfoPO.getCompanyId());
        po.setRiskIdentifyId(riskInfoPO.getRiskIdentifyId());
        riskMeasureMapper.insert(po);
        return true;
    }

    public Boolean updateRiskMeasure(RiskMeasureUpdateCommand command) {
        RiskMeasurePO po = riskMeasureAssembler.updateCommand2PO(command);
        riskMeasureMapper.updateById(po);
        return true;
    }

    public Boolean deleteRiskMeasure(List<Long> idList) {
        riskMeasureMapper.deleteBatchIds(idList);
        return true;
    }

    public Boolean sendNotify(SendNotifyCommand command) {
        RiskInfoDTO riskInfoDTO = this.get(command.getId());
        if (ObjUtil.isEmpty(riskInfoDTO)) {
            throw new BusinessException(MessageCode.RISK_NOT_EXIST);
        }
        if (!riskInfoDTO.getStatus().equals(RiskStatusEnum.NO_CLOSE)) {
            throw new BusinessException(MessageCode.STATUS_NOT_MATCH_STOP_NOTIFY);
        }
        String pcUrl = meiyeSeverProperties.getCardSend().getJumpUrl().getRiskPcUrl() + "?id=" + command.getId();
        String appUrl = meiyeSeverProperties.getCardSend().getJumpUrl().getRiskAppUrl() + "?id=" + command.getId();
        SendRiskDTO dto = SendRiskDTO.builder()
                .companyName(riskInfoDTO.getCompanyName())
                .code(riskInfoDTO.getCode())
                .address(riskInfoDTO.getFullAddressName()+"-"+ riskInfoDTO.getAddressDetail())
                .type(riskInfoDTO.getType().getDescription())
                .remark(riskInfoDTO.getRemark())
                .pcUrl(FsNotifyUrlUtils.getPCUrl(pcUrl))
                .appUrl(appUrl)
                .openIds(command.getOpenIds())
                .build();
        cardSendActionService.sendSignInCode(dto);
        return true;
    }


    /**
     * 发送审批
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean sendApproval(Long id) {
        RiskInfoDTO dto = this.get(id);
        if (ObjUtil.isEmpty(dto)) {
            throw new BusinessException(MessageCode.RISK_NOT_EXIST);
        }
        if (!dto.getStatus().equals(RiskStatusEnum.WAIT_SUBMIT) && !dto.getStatus().equals(RiskStatusEnum.REJECT)) {
            throw new BusinessException(MessageCode.STATUS_NOT_MATCH_STOP_APPROVAL);
        }
        // 校验审批人员
        List<MyUserPO> myUserPOS = myUserMapper.selectApprovalSignAndCompanyId(ApprovalSignEnum.COLLIERY, dto.getCompanyId());
        if (CollUtil.isEmpty(myUserPOS)) {
            throw new BusinessException("缺乏煤矿审批人员,请联系系统管理员配置");
        }
        MyCompanyPO pCompany = myCompanyMapper.selectById(dto.getCompanyId());
        List<MyUserPO> myUserRanges = myUserMapper.selectApprovalSignAndCompanyId(ApprovalSignEnum.RANGE, pCompany.getPid());
        if (CollUtil.isEmpty(myUserRanges)) {
            throw new BusinessException("缺乏区域公司审批人员,请联系系统管理员配置");
        }
        String identifyType = dto.getIdentifyType().getDescription();
        identifyType = ObjUtil.isNotEmpty(dto.getIdentifyDetail()) ? identifyType + "-" + dto.getIdentifyDetail().getDescription() : identifyType;
        String identifyTime = dto.getIdentifyTime().format(formatter);
        String measures = null;
        if (CollUtil.isNotEmpty(dto.getRiskMeasureList())) {
            measures = "\n";
            for (int i = 0; i < dto.getRiskMeasureList().size(); i++) {
                RiskMeasureDTO riskMeasureDTO = dto.getRiskMeasureList().get(i);
                measures = measures + " " + (i+1) + "." + riskMeasureDTO.getContent() + "\n";
            }
        }
        // 审批详情内容
        StringBuilder content = new StringBuilder(StrUtil.format(NOTIFY_TEMPLATE, dto.getCompanyName(), dto.getLevel().getDescription(), dto.getCode(),
                        dto.getType().getDescription(), dto.getControlStatus().getDescription(), dto.getIdentifyName(), identifyType, identifyTime,
                        dto.getFullAddressName(), dto.getAddressDetail(), dto.getOwnerName(), dto.getRemark(), measures)
                .replaceAll("null", "-"));
        Optional<ApprovalProperties> approvalOptional = meiyeSeverProperties.getApproval().stream().filter(item -> item.getCode().equals(ApprovalBizTypeEnum.RISK.getCode())).findFirst();
        String riskCode = approvalOptional.map(ApprovalProperties::getApprovalCode).orElse(null);
        GetApprovalRespBody approval = feishuAppClient.getApprovalService().getApproval(riskCode);
        // 审批节点id
        String collieryNodeId = this.getNodeId(approval, "煤矿审批");
        String regionNodeId = this.getNodeId(approval, "区域公司审批");

        InstanceFormModel instanceFormModel = InstanceFormModel.builder()
                .type(InstanceFormTypeEnum.TEXTAREA)
                .required(true)
                .value(content).build();
        List<NodeApprover> nodeApproverOpenIdList = new ArrayList<>();
        nodeApproverOpenIdList.add(NodeApprover.newBuilder()
                .key(collieryNodeId)
                .value(myUserPOS.stream().map(MyUserPO::getId).distinct().toArray(String[]::new)).build());
        nodeApproverOpenIdList.add(NodeApprover.newBuilder()
                .key(regionNodeId)
                .value(myUserRanges.stream().map(MyUserPO::getId).distinct().toArray(String[]::new)).build());
        ApprovalInstanceModel model = ApprovalInstanceModel.builder()
                .openId(CurrentUserHolder.getOpenId())
                .approvalCode(riskCode)
                .formList(Collections.singletonList(instanceFormModel))
                .nodeApproverOpenIdList(nodeApproverOpenIdList).build();
        String instanceCode = feishuAppClient.getApprovalService().createApprovalInstance(model);
        riskInfoMapper.updateById(RiskInfoPO.builder().id(id).approvalId(instanceCode).status(RiskStatusEnum.WAIT_REVIEW).build());
        myApprovalMapper.insert(MyApprovalPO.builder()
                .bizId(id)
                .bizType(ApprovalBizTypeEnum.RISK)
                .companyId(dto.getCompanyId())
                .id(instanceCode)
                .content(content.toString())
                .promoter(CurrentUserHolder.getOpenId())
                .operateTime(LocalDateTime.now())
                .status(FeishuInstanceStatusEnum.PENDING)
                .build());
        myApprovalLogMapper.insert(MyApprovalLogPO.builder()
                .approvalId(instanceCode)
                .status("提交审核")
                .openId(CurrentUserHolder.getOpenId())
                .userName(CurrentUserHolder.getName())
                .createTime(LocalDateTime.now())
                .build());
        return true;
    }

    public Boolean close(Long id) {
        riskProcessor.closeCheck(id);
        riskInfoMapper.updateById(RiskInfoPO.builder().id(id).status(RiskStatusEnum.CLOSE).build());
        return true;
    }

    private String getNodeId(GetApprovalRespBody approval, String nodeName) {
        if (ObjUtil.isNotNull(approval)) {
            ApprovalNodeInfo[] nodeList = approval.getNodeList();
            for (ApprovalNodeInfo approvalNodeInfo : nodeList) {
                if (nodeName.equals(approvalNodeInfo.getName())) {
                    return approvalNodeInfo.getNodeId();
                }
            }
        }
        log.error("未获取到审批节点,approval:{},nodeName:{}", JsonUtils.toJson(approval), nodeName);
        throw new BusinessException("未获取到审批节点");
    }

    /**
     * 生成风险编号
     */
    private String generateCode() {
        // 取最大的运营商编号
        RiskInfoPO riskInfoPO = riskInfoMapper.getMaxCode();
        if (ObjUtil.isNull(riskInfoPO)) {
            return "F" + "000001";
        }
        String maxCode = riskInfoPO.getCode();
        if (!maxCode.matches(CodeTypeEnum.MATCH)) {
            log.error("风险编号不正确:{}", maxCode);
            throw new BusinessException("风险编号不正确");
        }
        int incrementedNum = Integer.parseInt(maxCode.substring(1, 7)) + 1;
        String formattedNumericPart = String.format("%06d", incrementedNum);

        // 返回组合后的下一个编号
        return CodeTypeEnum.RISK.getCode() + formattedNumericPart;

    }
}
