package cn.pf.orch.meiye.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/5/9
 */
@Getter
@AllArgsConstructor
public enum CodeTypeEnum {

    RISK(1, "安全风险","F"),
    HAZARD(2, "事故隐患","Y"),
    ;

    @EnumValue
    @JsonValue
    private final int type;

    private final String description;

    private final String code;

    /**
     * 编号正则
     */
    public static final String MATCH = "^(F|Y)\\d{6}";

    private static final Map<Integer, CodeTypeEnum> VALUES = new HashMap<>();
    static {
        for (final CodeTypeEnum item : CodeTypeEnum.values()) {
            VALUES.put(item.getType(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static CodeTypeEnum of(int code) {
        return VALUES.get(code);
    }
}
