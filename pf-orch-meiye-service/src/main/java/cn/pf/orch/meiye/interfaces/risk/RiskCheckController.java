package cn.pf.orch.meiye.interfaces.risk;

import cn.genn.core.model.page.PageResultDTO;
import cn.pf.orch.meiye.domain.risk.command.RiskCheckCommand;
import cn.pf.orch.meiye.domain.risk.dto.RiskCheckDTO;
import cn.pf.orch.meiye.domain.risk.dto.RiskCheckPageDTO;
import cn.pf.orch.meiye.domain.risk.query.RiskCheckPageQuery;
import cn.pf.orch.meiye.service.risk.RiskCheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@Api(tags = "安全风险-检查台账")
@RestController
@RequestMapping("/risk/check")
public class RiskCheckController {

    @Resource
    private RiskCheckService riskCheckService;

    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询列表")
    public PageResultDTO<RiskCheckPageDTO> page(@ApiParam(value = "查询类") @RequestBody RiskCheckPageQuery query) {
        return riskCheckService.page(query);
    }

    @PostMapping("/get")
    @ApiOperation(value = "根据id查询")
    public RiskCheckDTO get(@ApiParam(value = "查询类") @RequestParam Long id) {
        return riskCheckService.get(id);
    }


    @PostMapping("/check")
    @ApiOperation(value = "风险自查/抽查")
    public void check(@RequestBody @Validated RiskCheckCommand command) {
        riskCheckService.check(command);
    }

}
