package cn.pf.orch.meiye.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum IdentifyRiskDetailEnum {

    DESIGN("design", "组织较大设计前"),
    CHANGE("change", "重大变化和\"四新\"应用前"),
    SHUTDOWN("shutdown", "停工项目复工复产前"),
    ACCIDENT("accident","发生事故或险情时"),
    OTHER("other","其他"),
    ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;


    private static final Map<String, IdentifyRiskDetailEnum> VALUES = new HashMap<>();
    static {
        for (final IdentifyRiskDetailEnum item : IdentifyRiskDetailEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static IdentifyRiskDetailEnum of(String code) {
        return VALUES.get(code);
    }
}
