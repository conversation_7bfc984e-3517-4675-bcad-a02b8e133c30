package cn.pf.orch.meiye.domain.system.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class MyUserUpdateCommand {

    @ApiModelProperty(value = "用户id")
    @NotBlank(message = "用户id不能为空")
    private String id;

    @ApiModelProperty(value = "煤矿id")
    private Long companyId;

    @ApiModelProperty(value = "用户名称")
    private String name;

    @ApiModelProperty(value = "手机号")
    private String mobile;
}
