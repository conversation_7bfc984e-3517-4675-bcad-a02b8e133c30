package cn.pf.orch.meiye.domain.safetygenerationstandard.query;

import cn.genn.core.model.page.PageSortQuery;
import cn.pf.orch.meiye.enums.ScoringLevelEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
@ApiModel("查询生产标准化列表")
public class ScoringRecordQuery extends PageSortQuery {

    @ApiModelProperty("煤矿id")
    private Long companyId;

    @ApiModelProperty("评分级别")
    private ScoringLevelEnum scoringLevel;

    @ApiModelProperty("开始时间")
    private LocalDate scoringDateStart;

    @ApiModelProperty("结束时间")
    private LocalDate scoringDateEnd;

    @ApiModelProperty("煤矿名称")
    private String companyName;

    private List<Long> rangeCompanyIds;
}
