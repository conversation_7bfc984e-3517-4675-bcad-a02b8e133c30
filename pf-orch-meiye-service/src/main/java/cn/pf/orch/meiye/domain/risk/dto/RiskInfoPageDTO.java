package cn.pf.orch.meiye.domain.risk.dto;

import cn.genn.core.model.enums.DeletedEnum;
import cn.pf.orch.meiye.enums.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class RiskInfoPageDTO {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "编号")
    private String code;

    @ApiModelProperty(value = "公司id")
    private Long companyId;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "风险辨识id")
    private Long riskIdentifyId;

    @ApiModelProperty(value = "描述")
    private String remark;

    @ApiModelProperty(value = "风险等级")
    private RiskLevelEnum level;

    @ApiModelProperty(value = "风险类别")
    private RiskTypeEnum type;

    @ApiModelProperty(value = "风险状态（待提交，待审核，已驳回，待关闭，已关闭）")
    private RiskStatusEnum status;

    @ApiModelProperty(value = "可控状态（0可控，1失控）")
    private ControlStatusEnum controlStatus;

    @ApiModelProperty(value = "风险责任人")
    private String owner;

    @ApiModelProperty(value = "风险责任人名称")
    private String ownerName;

    @ApiModelProperty(value = "审批id")
    private String approvalId;

    @ApiModelProperty(value = "删除状态（0未删除，1已删除）")
    private DeletedEnum deleted;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人id")
    private String createUserId;

    @ApiModelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人id")
    private String updateUserId;

    @ApiModelProperty(value = "更新人")
    private String updateUserName;

    @ApiModelProperty("地址id")
    private Long addressId;

    @ApiModelProperty("风险地址")
    private String addressName;

    @ApiModelProperty("全路径地址")
    private String fullAddressName;

    @ApiModelProperty("地址详情")
    private String addressDetail;

    @ApiModelProperty(value = "辨识计划名称")
    private String identifyName;

    @ApiModelProperty(value = "辨识类型")
    private IdentifyTypeEnum identifyType;

    @ApiModelProperty(value = "类别详情")
    private IdentifyRiskDetailEnum identifyDetail;

    @ApiModelProperty(value = "辨识日期")
    private LocalDateTime identifyTime;


}
