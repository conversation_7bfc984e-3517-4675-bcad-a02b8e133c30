package cn.pf.orch.meiye.domain.feishu;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 飞书事件请求对象
 * @param <T>
 */
@Data
@Builder
public class FeishuEventBaseCommand<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "飞书测试连通字段")
    private String challenge;

    private String type;

    private String token;

    @ApiModelProperty(value = "事件模式")
    private String schema;

    @ApiModelProperty(value = "事件头")
    private FeishuEventHeaderCommand header;

    @ApiModelProperty(value = "事件体")
    private T event;
}
