package cn.pf.orch.meiye.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum RiskTypeEnum {

    ROOF("roof", "顶板"),
    FIRE("fire", "火灾"),
    FLOOD("flood", "水害"),
    ELECTRICAL("electrical", "电气"),
    GAS("gas", "瓦斯"),
    ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;


    private static final Map<String, RiskTypeEnum> VALUES = new HashMap<>();
    static {
        for (final RiskTypeEnum item : RiskTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static RiskTypeEnum of(String code) {
        return VALUES.get(code);
    }
}
