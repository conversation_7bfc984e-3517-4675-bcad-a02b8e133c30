package cn.pf.orch.meiye.domain.risk.query;

import cn.genn.core.model.page.PageSortQuery;
import cn.pf.orch.meiye.enums.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class RiskCheckPageQuery extends PageSortQuery implements Serializable {

    @ApiModelProperty(value = "风险编码")
    private String code;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "辨识计划名称")
    private String identifyName;

    @ApiModelProperty(value = "风险等级")
    private RiskLevelEnum riskLevel;

    @ApiModelProperty(value = "风险类别")
    private RiskTypeEnum riskType;

    @ApiModelProperty(value = "是否可控")
    private ControlStatusEnum controlStatus;

    @ApiModelProperty(value = "风险责任人名称")
    private String ownerName;

    @ApiModelProperty(value = "辨识开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime identifyStartTime;

    @ApiModelProperty(value = "辨识结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime identifyEndTime;

    @ApiModelProperty(value = "风险措施")
    private String measureContent;

    @ApiModelProperty(value = "检查层级")
    private CheckLevelEnum checkLevel;

    @ApiModelProperty(value = "检查结果")
    private CheckResultEnum result;

    @ApiModelProperty(value = "检查人")
    private String checkUserName;

    @ApiModelProperty(value = "检查开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime checkStartTime;

    @ApiModelProperty(value = "检查结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime checkEndTime;

    @ApiModelProperty(value = "措施或描述")
    private String search;

    private List<Long> companyIds;

}
