package cn.pf.orch.meiye.domain.risk.command;

import cn.pf.orch.meiye.enums.ControlStatusEnum;
import cn.pf.orch.meiye.enums.RiskLevelEnum;
import cn.pf.orch.meiye.enums.RiskTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MyRiskInfoUpdateCommand {

    private Long id;

    @ApiModelProperty(value = "风险辨识id")
    private Long riskIdentifyId;

    @ApiModelProperty(value = "风险等级")
    private RiskLevelEnum level;

    @ApiModelProperty(value = "风险类别")
    private RiskTypeEnum type;

    @ApiModelProperty(value = "可控状态（0可控，1失控）")
    private ControlStatusEnum controlStatus;

    @ApiModelProperty(value = "风险责任人")
    private String owner;

    @ApiModelProperty(value = "风险责任人名称")
    private String ownerName;

    @ApiModelProperty(value = "描述")
    private String remark;

    @ApiModelProperty("地址id")
    private Long addressId;

    @ApiModelProperty("地址详情")
    private String addressDetail;
}
