package cn.pf.orch.meiye.domain.safetyviolation.command;

import cn.pf.orch.meiye.enums.AuthTypeEnum;
import cn.pf.orch.meiye.enums.DiscovererUnitsEnum;
import cn.pf.orch.meiye.enums.DiscoveryMethodEnum;
import cn.pf.orch.meiye.enums.ViolationTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@ApiModel(value ="AddCommand", description = "添加三违信息")
public class AddCommand {

    @ApiModelProperty(value = "煤矿ID", example = "1001", required = true)
    @NotNull
    private Long companyId;

    @ApiModelProperty(value = "单位名称", required = true)
    @NotNull
    private String companyName;


    @ApiModelProperty(value = "违章经过及原因", example = "未佩戴安全帽进入作业区域")
    @NotEmpty
    private String violationDetails;

    @ApiModelProperty(value = "三违性质", example = "严重违章", required = true)
    @NotNull
    private ViolationTypeEnum violationType;

    @ApiModelProperty(value = "被查出人名称", example = "张三")
    @NotEmpty
    private String personName;

    @ApiModelProperty(value = "被查出人单位", example = "采煤一队")
    @NotEmpty
    private String personDepartment;

    @ApiModelProperty(value = "被查出人工号", example = "EMP1001")
    private String personJobNumber;

    @ApiModelProperty(value = "被查出人OpenId", example = "openid123")
    @NotEmpty
    private String personJobOpenid;

    @ApiModelProperty(value = "班别")
    @NotEmpty
    private String personShift;

    @ApiModelProperty(value = "班次")
    @NotEmpty
    private String classes;

    @ApiModelProperty(value = "被查出人职务", example = "采煤工")
    @NotEmpty
    private String personPosition;

    @ApiModelProperty(value = "违章日期", example = "2023-06-20", required = true)
    @NotNull
    private LocalDate violationDate;

    @ApiModelProperty(value = "地点ID", example = "2001")
    @NotNull
    private Long violationLocationId;

    @ApiModelProperty(value = "详细地点")
    private String detailLocationName;

    @ApiModelProperty(value = "报送日期", example = "2023-06-21")
    @NotNull
    private LocalDate reportDate;

    @ApiModelProperty(value = "处理措施", example = "停工培训3天")
    @NotEmpty
    private String punishment;

    @ApiModelProperty(value = "处罚条款", example = "《煤矿安全规程》第56条")
    @NotEmpty
    private String clause;

    @ApiModelProperty(value = "罚款金额(元)", example = "500.00")
    @NotNull
    private BigDecimal fine;

    @ApiModelProperty(value = "查出方式", example = "安全检查")
    @NotNull
    private DiscoveryMethodEnum discoveryMethod;

    @ApiModelProperty(value = "查出人", example = "李四")
    @NotEmpty
    private String discoverer;

    @ApiModelProperty(value = "查处人工号", example = "李四")
    private String discovererNumber;

    @ApiModelProperty(value = "查处人openid", example = "李四")
    private String discovererOpenId;

    @ApiModelProperty(value = "查处人部门名称", example = "李四")
    @NotEmpty
    private String discovererDepartment;

    @ApiModelProperty(value = "查出人单位类别",example = "集团，区域，煤矿")
    private DiscovererUnitsEnum discovererUnits;

    @ApiModelProperty(value = "查处人部门id", example = "李四")
//    @NotEmpty
    private String discovererDepartmentId;

    @ApiModelProperty(value = "创建人ID", example = "user001")
    private String createUserId;

    @ApiModelProperty(value = "创建人", example = "系统管理员")
    private String createUserName;

}
