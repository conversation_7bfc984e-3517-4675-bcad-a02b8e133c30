package cn.pf.orch.meiye.domain.risk.dto;

import cn.genn.core.model.enums.DeletedEnum;
import cn.pf.orch.meiye.enums.ApprovalBizTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * MyApprovalDTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MyApprovalDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "飞书审批id")
    private String id;

    @ApiModelProperty(value = "业务类型")
    private ApprovalBizTypeEnum bizType;

    @ApiModelProperty(value = "业务id")
    private Long bizId;

    @ApiModelProperty(value = "审批内容json")
    private String content;

    @ApiModelProperty(value = "发起人open_id")
    private String promoter;

    @ApiModelProperty(value = "审批状态")
    private String status;

    @ApiModelProperty(value = "发生时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime operateTime;

    @ApiModelProperty(value = "公司id")
    private Long companyId;

    @ApiModelProperty(value = "删除状态")
    private DeletedEnum deleted;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人id")
    private String createUserId;

    @ApiModelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人id")
    private String updateUserId;

    @ApiModelProperty(value = "更新人")
    private String updateUserName;


}

