package cn.pf.orch.meiye.service.safetyaccident.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.pf.orch.meiye.assembler.SafetyAccidentAssmbler;
import cn.pf.orch.meiye.domain.location.po.MeiyeLocation;
import cn.pf.orch.meiye.domain.safetyaccident.command.AddCommand;
import cn.pf.orch.meiye.domain.safetyaccident.command.UpdateCommand;
import cn.pf.orch.meiye.domain.safetyaccident.dto.MeiyeSafetyAccidentDTO;
import cn.pf.orch.meiye.domain.safetyaccident.po.MeiyeSafetyAccidentRecords;
import cn.pf.orch.meiye.domain.safetyaccident.query.PageQuery;
import cn.pf.orch.meiye.enums.DelEnum;
import cn.pf.orch.meiye.mapper.MeiyeSafetyAccidentRecordsMapper;
import cn.pf.orch.meiye.service.location.MeiyeLocationService;
import cn.pf.orch.meiye.service.safetyaccident.MeiyeSafetyAccidentRecordsService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.lark.oapi.service.aily.v1.model.Run;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;


/**
 * 安全事故记录表(MeiyeSafetyAccidentRecords)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-13 19:28:18
 */
@Slf4j
@Service
public class MeiyeSafetyAccidentRecordsServiceImpl extends ServiceImpl<MeiyeSafetyAccidentRecordsMapper, MeiyeSafetyAccidentRecords> implements MeiyeSafetyAccidentRecordsService {

    @Resource
    private MeiyeSafetyAccidentRecordsMapper mapper;

    @Resource
    private MeiyeLocationService locationService;

    @Resource
    private SafetyAccidentAssmbler assmbler;

    @Override
    public void add(AddCommand record) {
        MeiyeSafetyAccidentRecords entity = assmbler.addCommandToPO(record);
        mapper.insert(entity);
    }

    @Override
    public void update(UpdateCommand command) {
        MeiyeSafetyAccidentRecords meiyeSafetyAccidentRecords = assmbler.updateCommandToPO(command);
        mapper.updateById(meiyeSafetyAccidentRecords);
    }

    @Override
    public void delete(Integer id) {
        LambdaUpdateWrapper<MeiyeSafetyAccidentRecords> wrapper = Wrappers.lambdaUpdate(MeiyeSafetyAccidentRecords.class)
                .eq(MeiyeSafetyAccidentRecords::getId, id)
                .set(MeiyeSafetyAccidentRecords::getDel, DelEnum.DELETE);
        mapper.update(wrapper);
    }

    @Override
    public Page<MeiyeSafetyAccidentDTO> query(PageQuery query) {
        LambdaQueryWrapper<MeiyeSafetyAccidentRecords> wrapper = Wrappers.lambdaQuery(MeiyeSafetyAccidentRecords.class)
                .eq(ObjectUtil.isNotEmpty(query.getCompanyId()), MeiyeSafetyAccidentRecords::getCompanyId, query.getCompanyId())
                .eq(ObjectUtil.isNotEmpty(query.getAccidentCategory()), MeiyeSafetyAccidentRecords::getAccidentCategory, query.getAccidentCategory())
                .eq(ObjectUtil.isNotEmpty(query.getAccidentNature()), MeiyeSafetyAccidentRecords::getAccidentNature, query.getAccidentNature())
                .like(ObjectUtil.isNotEmpty(query.getAccidentName()), MeiyeSafetyAccidentRecords::getAccidentName, query.getAccidentName())
                .like(ObjectUtil.isNotEmpty(query.getResponsiblePerson()), MeiyeSafetyAccidentRecords::getResponsiblePerson, query.getResponsiblePerson())
                .eq(ObjectUtil.isNotEmpty(query.getResponsiblePersonNumber()), MeiyeSafetyAccidentRecords::getResponsiblePersonNumber, query.getResponsiblePersonNumber())
                .eq(ObjectUtil.isNotEmpty(query.getResponsiblePersonOpenId()), MeiyeSafetyAccidentRecords::getResponsiblePersonOpenId, query.getResponsiblePersonOpenId())
                .ge(ObjectUtil.isNotEmpty(query.getOccurrenceTimeStart()), MeiyeSafetyAccidentRecords::getOccurrenceTime, query.getOccurrenceTimeStart())
                .le(ObjectUtil.isNotEmpty(query.getOccurrenceTimeEnd()), MeiyeSafetyAccidentRecords::getOccurrenceTime, query.getOccurrenceTimeEnd())
                .ge(ObjectUtil.isNotEmpty(query.getReportTimeStart()), MeiyeSafetyAccidentRecords::getReportTime, query.getReportTimeStart())
                .le(ObjectUtil.isNotEmpty(query.getReportTimeEnd()), MeiyeSafetyAccidentRecords::getReportTime, query.getReportTimeEnd())
                .eq(MeiyeSafetyAccidentRecords::getDel, DelEnum.NO_DELETE.getCode())
                .in(ObjectUtil.isNotEmpty(query.getRangeCompanyIds()), MeiyeSafetyAccidentRecords::getCompanyId, query.getRangeCompanyIds())
                .orderByDesc(MeiyeSafetyAccidentRecords::getCreateTime);
        Page<MeiyeSafetyAccidentRecords> POPage = mapper.selectPage( new Page<>(query.getPageNo(), query.getPageSize()), wrapper);
        // 构建响应值
        List<MeiyeSafetyAccidentDTO> records = Lists.newArrayList();
        Optional.ofNullable(POPage.getRecords())
                .ifPresent(list -> {
                    list.forEach(po -> {
                        MeiyeSafetyAccidentDTO dto = assmbler.queryAessmble(po);
                        MeiyeLocation location = locationService.selectLocationById(dto.getLocationId());
                        dto.setLocationName(location.getName());
                        dto.setLocationFullPathName(location.getFullPathName());
                        records.add(dto);
                    });
                });
        Page<MeiyeSafetyAccidentDTO> dtoPage = new Page<>(POPage.getCurrent(), POPage.getSize(), POPage.getTotal());
        dtoPage.setRecords(records);
        return dtoPage;
    }

    @Override
    public MeiyeSafetyAccidentDTO detail(Integer id) {
        MeiyeSafetyAccidentRecords meiyeSafetyAccidentRecords = mapper.selectById(id);
        MeiyeSafetyAccidentDTO dto = assmbler.queryAessmble(meiyeSafetyAccidentRecords);
        return Optional.ofNullable(dto).map(info -> {
            MeiyeLocation location = locationService.selectLocationById(info.getLocationId());
            dto.setLocationName(location.getName());
            dto.setLocationFullPathName(location.getFullPathName());
            return dto;
        }).orElseThrow(() -> new RuntimeException("没查询到该记录的详细信息"));
    }
}

