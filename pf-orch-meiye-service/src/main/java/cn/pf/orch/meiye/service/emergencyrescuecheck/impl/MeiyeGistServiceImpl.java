package cn.pf.orch.meiye.service.emergencyrescuecheck.impl;

import cn.pf.orch.meiye.domain.emergencyrescuecheck.po.MeiyeGist;
import cn.pf.orch.meiye.mapper.MeiyeGistMapper;
import cn.pf.orch.meiye.service.emergencyrescuecheck.MeiyeGistService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 依据信息表(MeiyeGist)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-15 11:39:11
 */
@Service
public class MeiyeGistServiceImpl extends ServiceImpl<MeiyeGistMapper, MeiyeGist> implements MeiyeGistService {

    @Resource
    private MeiyeGistMapper meiyeGistMapper;

    @Override
    public List<MeiyeGist> queryByIds(List<String> ids) {
        return meiyeGistMapper.selectList(Wrappers.lambdaQuery(MeiyeGist.class).in(MeiyeGist::getId, ids));
    }

}

