package cn.pf.orch.meiye.domain.risk.po;

import cn.genn.core.model.enums.DeletedEnum;
import cn.pf.orch.meiye.enums.CheckLevelEnum;
import cn.pf.orch.meiye.enums.CheckResultEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * RiskCheckPO对象
 *
 * <AUTHOR>
 * @desc 风险检查表
 */
@Data
@Accessors(chain = true)
@TableName(value = "risk_check", autoResultMap = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskCheckPO {

	/**
	 *
	 */
	@TableId
	private Long id;

	/**
	 * 公司id
	 */
	@TableField("company_id")
	private Long companyId;

	/**
	 * 风险措施id
	 */
	@TableField("risk_measure_id")
	private Long riskMeasureId;

	/**
	 * 风险id
	 */
	@TableField("risk_id")
	private Long riskId;

	/**
	 * 检查层级（1煤矿自查，2区域抽查，3集团抽查）
	 */
	@TableField("level")
	private CheckLevelEnum level;

	/**
	 * 检查结果（0通过，1未通过）
	 */
	@TableField("result")
	private CheckResultEnum result;

	/**
	 * 检查详情
	 */
	@TableField("detail")
	private String detail;

	/**
	 * 检查附件
	 */
	@TableField("files")
	private String files;

	/**
	 * 删除状态
	 */
	@TableField("deleted")
	private DeletedEnum deleted;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 创建人id
	 */
	@TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
	private String createUserId;

	/**
	 * 创建人
	 */
	@TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
	private String createUserName;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	 * 更新人id
	 */
	@TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
	private String updateUserId;

	/**
	 * 更新人
	 */
	@TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
	private String updateUserName;

}

