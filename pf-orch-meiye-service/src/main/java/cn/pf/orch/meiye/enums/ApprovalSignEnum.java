package cn.pf.orch.meiye.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum ApprovalSignEnum {

    RANGE("range","区域公司审批"),
    COLLIERY("colliery","煤矿审批"),
    ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;


    private static final Map<String, ApprovalSignEnum> VALUES = new HashMap<>();

    static {
        for (final ApprovalSignEnum item : ApprovalSignEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static ApprovalSignEnum of(String code) {
        return VALUES.get(code);
    }
}
