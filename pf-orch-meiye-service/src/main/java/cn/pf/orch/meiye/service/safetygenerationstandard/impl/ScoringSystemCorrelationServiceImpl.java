package cn.pf.orch.meiye.service.safetygenerationstandard.impl;


import cn.genn.core.exception.BusinessException;
import cn.hutool.core.util.ObjectUtil;
import cn.pf.orch.meiye.domain.safetygenerationstandard.command.InitCompanyScoredSystemCommand;
import cn.pf.orch.meiye.domain.safetygenerationstandard.command.ScoredUpdateCommand;
import cn.pf.orch.meiye.domain.safetygenerationstandard.dto.ScoredStatusNumDTO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.dto.ScoringSystemCorrelationDTO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.dto.ScoringSystemDetailCorrelationDTO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.po.ScoringRecordsPO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.po.ScoringSystemCorrelationPO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.po.ScoringSystemTemplatePO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.query.ScoredStatusNumQuery;
import cn.pf.orch.meiye.domain.safetygenerationstandard.query.ScoredSystemDetailQuery;
import cn.pf.orch.meiye.domain.safetygenerationstandard.query.ScoringSystemQuery;
import cn.pf.orch.meiye.enums.DelEnum;
import cn.pf.orch.meiye.enums.MineTypeeEnum;
import cn.pf.orch.meiye.mapper.ScoringSystemCorrelationMapper;
import cn.pf.orch.meiye.mapper.ScoringSystemTemplateMapper;
import cn.pf.orch.meiye.service.safetygenerationstandard.ScoringDetailCorrelationService;
import cn.pf.orch.meiye.service.safetygenerationstandard.ScoringRecordsService;
import cn.pf.orch.meiye.service.safetygenerationstandard.ScoringSystemCorrelationService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 管理项-评分记录中间表(ScoringSystemCorrelation)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-22 16:52:44
 */
@Service
public class ScoringSystemCorrelationServiceImpl extends ServiceImpl<ScoringSystemCorrelationMapper, ScoringSystemCorrelationPO> implements ScoringSystemCorrelationService {

    @Resource
    private ScoringSystemTemplateMapper scoringSystemTemplateMapper;
    @Resource
    private ScoringSystemCorrelationMapper scoringSystemCorrelationMapper;
    @Resource
    private ScoringDetailCorrelationService scoringDetailCorrelationService;

    private ScoringRecordsService scoringRecordsService;
    @Autowired
    private void setScoringRecordsService(ScoringRecordsService scoringRecordsService) {
        this.scoringRecordsService = scoringRecordsService;
    }

    @Override
    public List<ScoringSystemCorrelationDTO> queryScoredSystem(ScoringSystemQuery query) {
        // 查询 煤矿安全一体化记录
        ScoringRecordsPO scoringRecordsPO = scoringRecordsService.conditionBy(query);
        Optional.ofNullable(scoringRecordsPO).orElseThrow(() -> new BusinessException("未查询到煤矿安全一体化信息！"));
        // 定义响应值
        List<ScoringSystemCorrelationDTO> result = Lists.newArrayList();
        // 根据记录id查询管理体系评分表
        LambdaQueryWrapper<ScoringSystemCorrelationPO> wrapper = Wrappers.lambdaQuery(ScoringSystemCorrelationPO.class)
                .eq(ScoringSystemCorrelationPO::getScoringRecordId, scoringRecordsPO.getId());
        List<ScoringSystemCorrelationPO> scoringSystemCorrelationPOS = scoringSystemCorrelationMapper.selectList(wrapper);
        Optional.ofNullable(scoringSystemCorrelationPOS).ifPresent(scoringSystemCorrelationPOList-> {
            scoringSystemCorrelationPOList.forEach(correlationPO -> {
                ScoringSystemTemplatePO templatePO = scoringSystemTemplateMapper.selectById(correlationPO.getScoringSystemId());
                ScoringSystemCorrelationDTO dto = new ScoringSystemCorrelationDTO();
                dto.setScoringSystemCorrelationId(correlationPO.getId());
                dto.setActualScore(correlationPO.getActualScore());
                dto.setCommonType(templatePO.getCommonType());
                dto.setWeight(templatePO.getWeight());
                dto.setStandardScore(templatePO.getStandardScore());
                // 查询进度
                ScoredStatusNumDTO scoredStatusNumDTO = scoringDetailCorrelationService.queryScoredStatusNum(new ScoredStatusNumQuery(correlationPO.getScoringRecordId(), correlationPO.getId()));
                dto.setScoredNumber(scoredStatusNumDTO.getScoredNumber());
                dto.setTotalSize(scoredStatusNumDTO.getTotal());
                result.add(dto);
            });
        });
        return result;
    }



    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public void initCompanyScoredSystem(InitCompanyScoredSystemCommand command) {
        // 去重校验
        LambdaQueryWrapper<ScoringSystemCorrelationPO> wrapper = Wrappers.lambdaQuery(ScoringSystemCorrelationPO.class)
                .eq(ScoringSystemCorrelationPO::getScoringRecordId, command.getScoringRecordId());
        List<ScoringSystemCorrelationPO> systemCorrelationPOS = scoringSystemCorrelationMapper.selectList(wrapper);
        if (ObjectUtils.isNotEmpty(systemCorrelationPOS)) {
            throw new BusinessException("当前煤矿标准化记录已有管理项评分体系，不可添加！");
        }
        // 构建体系表实体
        List<ScoringSystemTemplatePO> scoringSystemTemplatePOS =
                scoringSystemTemplateMapper.selectList(Wrappers.<ScoringSystemTemplatePO>lambdaQuery().eq(ScoringSystemTemplatePO::getMineType, MineTypeeEnum.UNDERGROUND_MINE));
        Optional.ofNullable(scoringSystemTemplatePOS).orElseThrow(() -> new BusinessException("管理项模板查询异常"));
        List<ScoringSystemCorrelationPO> insertList = genericScoringSystemCorrelationPOS(command, scoringSystemTemplatePOS);
        this.saveBatch(insertList);
        // 初始化体系下各项目评分表实体
        insertList.forEach(scoringSystemCorrelationPO -> {
            scoringDetailCorrelationService.initCompanyScoredDetail(scoringSystemCorrelationPO.getScoringRecordId(),scoringSystemCorrelationPO.getScoringSystemId(),scoringSystemCorrelationPO.getId());
        });
    }


    @Transactional
    @Override
    public void scoredSystem(ScoredUpdateCommand command) {
        ScoringSystemCorrelationPO scoringSystemCorrelationPO = scoringSystemCorrelationMapper.selectById(command.getScoringSystemCorrelationId());
        Optional.ofNullable(scoringSystemCorrelationPO).orElseThrow(() -> new RuntimeException("未查询到管理项评分信息，更新失败,command is" + command));
        // 查询当前管理项下的项目明细
        List<ScoringSystemDetailCorrelationDTO> scoringSystemDetailCorrelationDTOS = scoringDetailCorrelationService.queryScoringSystemDetailCorrelationList(new ScoredSystemDetailQuery(command.getScoredRecordId(), command.getScoringSystemCorrelationId()));
        // 计算管理项分数
        scoringSystemCorrelationPO.setActualScore(scoringSystemDetailCorrelationDTOS.stream().map(ScoringSystemDetailCorrelationDTO::getActualScore).filter(ObjectUtil::isNotNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        scoringSystemCorrelationMapper.updateById(scoringSystemCorrelationPO);
    }

    @Transactional
    @Override
    public void deleteByScoringRecordId(Long scoringRecordId) {
        Optional.ofNullable(scoringRecordId).orElseThrow(() -> new RuntimeException("scoringRecordId is null, forbid delete"));
        LambdaQueryWrapper<ScoringSystemCorrelationPO> wrapper = Wrappers.lambdaQuery(ScoringSystemCorrelationPO.class)
                .eq(ScoringSystemCorrelationPO::getScoringRecordId, scoringRecordId);
        List<ScoringSystemCorrelationPO> systemCorrelationPOS = scoringSystemCorrelationMapper.selectList(wrapper);
        Optional.ofNullable(systemCorrelationPOS).orElseThrow(() -> new RuntimeException("systemCorrelationPO is null, forbid delete"));
        systemCorrelationPOS.forEach(scoringSystemCorrelationPO -> {
            scoringSystemCorrelationPO.setDel(DelEnum.NO_DELETE);
        });
        this.updateBatchById(systemCorrelationPOS);
    }


    @NotNull
    private List<ScoringSystemCorrelationPO> genericScoringSystemCorrelationPOS(InitCompanyScoredSystemCommand command, List<ScoringSystemTemplatePO> scoringSystemTemplatePOS) {
        ArrayList<ScoringSystemCorrelationPO> insertList = Lists.newArrayList();
        for (ScoringSystemTemplatePO templatePO : scoringSystemTemplatePOS) {
            ScoringSystemCorrelationPO scoringSystemCorrelationPO = new ScoringSystemCorrelationPO();
            scoringSystemCorrelationPO.setScoringSystemId(templatePO.getId());
            scoringSystemCorrelationPO.setScoringRecordId(command.getScoringRecordId());
            scoringSystemCorrelationPO.setActualScore(BigDecimal.ZERO);
            scoringSystemCorrelationPO.setProgress(BigDecimal.ZERO);
            insertList.add(scoringSystemCorrelationPO);
        }
        return insertList;
    }

}

