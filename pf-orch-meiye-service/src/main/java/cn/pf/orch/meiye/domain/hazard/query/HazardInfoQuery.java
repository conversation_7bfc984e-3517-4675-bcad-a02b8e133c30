package cn.pf.orch.meiye.domain.hazard.query;

import cn.genn.core.model.page.PageSortQuery;
import cn.pf.orch.meiye.enums.ControlStatusEnum;
import cn.pf.orch.meiye.enums.RiskLevelEnum;
import cn.pf.orch.meiye.enums.RiskStatusEnum;
import cn.pf.orch.meiye.enums.RiskTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * HazardInfo查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class HazardInfoQuery extends PageSortQuery implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "隐患编号")
    private String code;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "隐患排查计划")
    private String identifyName;

    @ApiModelProperty(value = "隐患等级")
    private RiskLevelEnum level;

    @ApiModelProperty(value = "隐患类别")
    private RiskTypeEnum hazardType;

    @ApiModelProperty(value = "隐患状态")
    private RiskStatusEnum hazardStatus;

    @ApiModelProperty(value = "可控状态")
    private ControlStatusEnum controlStatus;

    @ApiModelProperty(value = "隐患责任人")
    private String ownerName;

    @ApiModelProperty(value = "隐患排查开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "隐患排查结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    private List<Long> companyIds;

    @ApiModelProperty("整改单位名称")
    private String departmentName;

    @ApiModelProperty("整改责任人名称")
    private String rectifyOwnerName;

    @ApiModelProperty("描述")
    private String remark;



}

