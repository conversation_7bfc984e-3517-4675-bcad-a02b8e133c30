package cn.pf.orch.meiye.domain.system.query;

import cn.genn.core.model.page.PageSortQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class HomePageQuery extends PageSortQuery implements Serializable {

    private String code;

    private String remark;

    private List<Long> companyIds;

    private String openId;
}
