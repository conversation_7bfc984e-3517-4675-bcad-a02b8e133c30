package cn.pf.orch.meiye.config;

import cn.genn.core.context.BaseRequestContext;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.pf.orch.feishu.config.FeishuAppContext;
import cn.pf.orch.meiye.common.Constants;
import cn.pf.orch.meiye.exception.AuthException;
import cn.pf.orch.meiye.exception.MessageCode;
import cn.pf.orch.meiye.properties.MeiyeSeverProperties;
import cn.pf.orch.meiye.service.SsoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

@Component
@Slf4j
public class SsoAuthFilter implements HandlerInterceptor, Ordered {

    @Resource
    private MeiyeSeverProperties meiyeSeverProperties;
    @Resource
    private SsoService ssoService;

    @Value(value = "${server.servlet.context-path}")
    private String contextPath;

    private final PathMatcher pathMatcher = new AntPathMatcher();

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!meiyeSeverProperties.getPermission().isEnable()) {
            return true;
        }
        String token = request.getHeader(Constants.TOKEN);
        String requestUri = request.getRequestURI();

        try {
            // 无需拦截的url
            if (this.filterIgnoreAccess(requestUri)) {
                log.debug("SsoAuthFilter ignore uri auth requestUri={}", requestUri);
                return true;
            }
            if (StrUtil.isBlank(token)) {
                throw new AuthException(MessageCode.AUTH_FAIL);
            }
            UserInfoDTO userInfoDTO = ssoService.getCacheUserInfo(token);
            log.debug("SsoAuthFilter userInfo from token dto={}", JSONUtil.toJsonStr(userInfoDTO));
            if (Objects.isNull(userInfoDTO)) {
                throw new AuthException(MessageCode.TOKEN_INVALID);
            }
            CurrentUserHolder.CURRENT_USER_THREAD_LOCAL.set(userInfoDTO);
            // core-上下文
            BaseRequestContext.putAttachment(Constants.OPEN_ID,userInfoDTO.getOpenId());
            BaseRequestContext.putAttachment(Constants.USER_NAME,userInfoDTO.getName());

            FeishuAppContext context = new FeishuAppContext();
            context.setUserAccessToken(userInfoDTO.getUserAccessToken());
            context.setOpenId(userInfoDTO.getOpenId());
            FeishuAppContext.set(context);
        } catch (AuthException ex) {
            log.warn("SsoAuthFilter error ", ex);
            throw ex;
        } catch (Exception ex) {
            log.error("SsoAuthFilter error ", ex);
            throw new AuthException(MessageCode.LOGIN_ERROR.getCode(), "鉴权过滤器异常: " + ex.getMessage());
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        CurrentUserHolder.CURRENT_USER_THREAD_LOCAL.remove();
        HandlerInterceptor.super.afterCompletion(request, response, handler, ex);
    }

    @Override
    public int getOrder() {
        return -99;
    }

    private boolean filterIgnoreAccess(String requestUri) {
        String requestUriSub = "";
        if (requestUri.startsWith(contextPath)) {
            requestUriSub = requestUri.substring(contextPath.length());
        }
        for (String ignoreUrl : meiyeSeverProperties.getPermission().getDefaultUris()) {
            if (pathMatcher.match(ignoreUrl, requestUri) || pathMatcher.match(ignoreUrl, requestUriSub)) {
                return true;
            }
        }
        for (String ignoreUrl : meiyeSeverProperties.getPermission().getExcludePatterns()) {
            if (pathMatcher.match(ignoreUrl, requestUri) || pathMatcher.match(ignoreUrl, requestUriSub)) {
                return true;
            }
        }
        return false;
    }

}
