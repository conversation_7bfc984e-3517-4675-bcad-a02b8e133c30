package cn.pf.orch.meiye.service.system;

import cn.genn.cache.redis.annotation.Cache;
import cn.genn.core.exception.BusinessException;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.pf.orch.feishu.FeishuAppClient;
import cn.pf.orch.meiye.assembler.CompanyAssembler;
import cn.pf.orch.meiye.common.CacheConstants;
import cn.pf.orch.meiye.config.CurrentUserHolder;
import cn.pf.orch.meiye.domain.system.command.CompanyAddCommand;
import cn.pf.orch.meiye.domain.system.command.CompanyUpdateCommand;
import cn.pf.orch.meiye.domain.system.dto.MyCompanyDTO;
import cn.pf.orch.meiye.domain.system.dto.MyCompanyTreeDTO;
import cn.pf.orch.meiye.domain.system.po.MyCompanyLicensePO;
import cn.pf.orch.meiye.domain.system.po.MyCompanyPO;
import cn.pf.orch.meiye.domain.system.query.MyCompanyQuery;
import cn.pf.orch.meiye.enums.AuthTypeEnum;
import cn.pf.orch.meiye.exception.MessageCode;
import cn.pf.orch.meiye.mapper.MyCompanyLicenseMapper;
import cn.pf.orch.meiye.mapper.MyCompanyMapper;
import cn.pf.orch.meiye.processor.CompanyProcessor;
import cn.pf.orch.meiye.utils.RedisUtils;
import com.lark.oapi.service.contact.v3.model.Department;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class CompanyService {

    @Resource
    private MyCompanyMapper myCompanyMapper;
    @Resource
    private MyCompanyLicenseMapper myCompanyLicenseMapper;
    @Resource
    private CompanyAssembler assembler;
    @Resource
    private CompanyProcessor companyProcessor;
    @Resource
    private FeishuAppClient feishuAppClient;
    @Resource
    private SyncFSContactService syncFSContactService;

    public List<MyCompanyTreeDTO> queryList(MyCompanyQuery query) {
        List<MyCompanyPO> departmentPOs = myCompanyMapper.queryList(query, CurrentUserHolder.getRangeCompanyIds());
        return assembler.PO2Tree(departmentPOs);
    }

    public MyCompanyDTO get(Long id) {
        MyCompanyPO companyPO = myCompanyMapper.selectById(id);
        MyCompanyDTO myCompanyDTO = assembler.PO2DTO(companyPO);
        // 上级名称
        if (!companyPO.getAuthType().equals(AuthTypeEnum.GROUP)) {
            MyCompanyPO companyPO1 = myCompanyMapper.selectById(id);
            myCompanyDTO.setPName(companyPO1.getName());
        }
        // 飞书部门
        if (CollUtil.isNotEmpty(myCompanyDTO.getDepartmentIds())) {
            List<Department> departmentBatch = feishuAppClient.getContactService().getDepartmentBatch(myCompanyDTO.getDepartmentIds());
            myCompanyDTO.setDepartments(departmentBatch);
        }
        // 资质
        List<MyCompanyLicensePO> myCompanyLicensePOS = myCompanyLicenseMapper.selectByCompanyId(id);
        myCompanyDTO.setLicenseList(assembler.licensePO2DTO(myCompanyLicensePOS));
        return myCompanyDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    public void add(CompanyAddCommand command) {
        // 参数校验
        companyProcessor.checkSave(command);
        // 写入数据库
        MyCompanyPO myCompanyPO = assembler.command2PO(command);
        myCompanyPO.setDepartmentIds(String.join(",", command.getDepartmentIds()));
        myCompanyMapper.insert(myCompanyPO);
        if (CollUtil.isNotEmpty(command.getLicenseList())) {
            List<MyCompanyLicensePO> myCompanyLicensePOS = assembler.licenseCommand2PO(command.getLicenseList());
            if(CollUtil.isNotEmpty(myCompanyLicensePOS)){
                for (MyCompanyLicensePO myCompanyLicensePO : myCompanyLicensePOS) {
                    myCompanyLicensePO.setCompanyId(myCompanyPO.getId());
                }
                myCompanyLicenseMapper.batchInsert(myCompanyLicensePOS);
            }
        }
        // 清理缓存
        RedisUtils.deleteKeysByPrefix(CacheConstants.CACHE_PRE_DEFAULT + CacheConstants.RANGE_COMPANY);
        // 同步fs用户,补充角色
        CompletableFuture.runAsync(() -> syncFSContactService.syncDepartmentAndUser(command.getDepartmentIds(), myCompanyPO)).exceptionally((ex -> {
            log.error("add同步fs用户异常: ", ex);
            return null;
        }));
    }


    public void update(CompanyUpdateCommand command) {
        // 参数校验
        MyCompanyPO myCompanyPO = companyProcessor.checkUpdate(command);
        // 写入数据库
        myCompanyPO.setDepartmentIds(CollUtil.isNotEmpty(command.getDepartmentIds()) ? String.join(",", command.getDepartmentIds()) : null);
        myCompanyPO.setName(StrUtil.isNotEmpty(command.getName()) ? command.getName() : null);
        myCompanyMapper.updateById(myCompanyPO);
        if (CollUtil.isNotEmpty(command.getLicenseList())) {
            List<MyCompanyLicensePO> myCompanyLicensePOS = assembler.licenseCommand2PO(command.getLicenseList());
            if(CollUtil.isNotEmpty(myCompanyLicensePOS)){
                for (MyCompanyLicensePO myCompanyLicensePO : myCompanyLicensePOS) {
                    myCompanyLicensePO.setCompanyId(command.getId());
                }
                myCompanyLicenseMapper.deleteCompanyId(command.getId());
                myCompanyLicenseMapper.batchInsert(myCompanyLicensePOS);
            }
        }
        // 同步fs用户,补充角色
        CompletableFuture.runAsync(() -> syncFSContactService.syncDepartmentAndUser(command.getDepartmentIds(), myCompanyPO)).exceptionally((ex -> {
            log.error("update同步fs用户异常: ", ex);
            return null;
        }));
    }

    public void delete(Long id) {
        companyProcessor.checkDelete(id);
        myCompanyMapper.deleteById(id);
        myCompanyLicenseMapper.deleteCompanyId(id);
        // 清理缓存
        RedisUtils.deleteKeysByPrefix(CacheConstants.CACHE_PRE_DEFAULT + CacheConstants.RANGE_COMPANY);
    }

    public List<MyCompanyTreeDTO> queryTreeRange() {
        AuthTypeEnum authType = CurrentUserHolder.getAuthType();
        List<MyCompanyPO> companyPOS = myCompanyMapper.selectByAuthType(authType);
        return assembler.PO2Tree(companyPOS);
    }

    public List<MyCompanyDTO> queryRange(AuthTypeEnum authType) {
        if (authType == null) {
            authType = CurrentUserHolder.getAuthType();
        } else {
            authType = authType.getCode() < CurrentUserHolder.getAuthType().getCode() ? CurrentUserHolder.getAuthType() : authType;
        }
        return assembler.PO2DTO(myCompanyMapper.selectByAuthType(authType));
    }

    /**
     * 获取权限范围内的所有公司信息
     */
    @Cache(value = CacheConstants.RANGE_COMPANY, fieldKey = "#companyId", expireTime = 60 * 60 * 24)
    public List<MyCompanyPO> getRangeCompanyPO(Long companyId) {
        MyCompanyPO companyPO = myCompanyMapper.selectById(companyId);
        switch (companyPO.getAuthType()) {
            case GROUP:
                return myCompanyMapper.selectList(null);
            case COLLIERY:
                return Collections.singletonList(companyPO);
            case REGION:
                List<MyCompanyPO> companyPOS = myCompanyMapper.selectByPid(companyId);
                companyPOS.add(companyPO);
                return companyPOS;
            default:
                throw new BusinessException(MessageCode.LOGIN_ERROR);
        }
    }
}
