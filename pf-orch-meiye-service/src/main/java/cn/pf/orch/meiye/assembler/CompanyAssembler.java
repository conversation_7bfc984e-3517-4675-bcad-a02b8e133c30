package cn.pf.orch.meiye.assembler;

import cn.hutool.core.collection.CollUtil;
import cn.pf.orch.meiye.domain.system.command.CompanyAddCommand;
import cn.pf.orch.meiye.domain.system.command.CompanyLicenseAddCommand;
import cn.pf.orch.meiye.domain.system.dto.MyCompanyDTO;
import cn.pf.orch.meiye.domain.system.dto.MyCompanyLicenseDTO;
import cn.pf.orch.meiye.domain.system.dto.MyCompanyTreeDTO;
import cn.pf.orch.meiye.domain.system.po.MyCompanyLicensePO;
import cn.pf.orch.meiye.domain.system.po.MyCompanyPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.*;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CompanyAssembler {

    @Mapping(target = "departmentIds", expression = "java(stringToList(po.getDepartmentIds()))")
    MyCompanyDTO PO2DTO(MyCompanyPO po);

    List<MyCompanyDTO> PO2DTO(List<MyCompanyPO> list);

    @Mapping(target = "departmentIds", expression = "java(listToString(command.getDepartmentIds()))")
    MyCompanyPO command2PO(CompanyAddCommand command);

    List<MyCompanyLicenseDTO> licensePO2DTO(List<MyCompanyLicensePO> poList);

    MyCompanyLicenseDTO licensePO2DTO(MyCompanyLicensePO po);

    MyCompanyLicensePO licenseCommand2PO(CompanyLicenseAddCommand command);

    default List<MyCompanyLicensePO> licenseCommand2PO(List<CompanyLicenseAddCommand> list){
        return list.stream().filter(command-> CollUtil.isNotEmpty(command.getLicenses()))
                .map(this::licenseCommand2PO).collect(Collectors.toList());
    }

    MyCompanyTreeDTO PO2Tree(MyCompanyPO po);

    default List<MyCompanyTreeDTO> PO2Tree(List<MyCompanyPO> poList){
        // 创建一个Map，用于快速查找节点
        Map<Long, MyCompanyTreeDTO> map = new HashMap<>();
        List<MyCompanyTreeDTO> result = new ArrayList<>();

        // 第一次遍历：将所有PO转换为DTO，并放入Map中
        for (MyCompanyPO po : poList) {
            MyCompanyTreeDTO dto = PO2Tree(po);
            dto.setChildren(new ArrayList<>());
            map.put(po.getId(), dto);
        }
        // 第二次遍历：构建树结构
        for (MyCompanyPO po : poList) {
            MyCompanyTreeDTO dto = map.get(po.getId());
            if (po.getPid() == null || !map.containsKey(po.getPid())) {
                // 如果没有父节点，则直接添加到结果列表中
                result.add(dto);
            } else {
                // 如果有父节点，则将其添加到父节点的children列表中
                MyCompanyTreeDTO parent = map.get(po.getPid());
                parent.getChildren().add(dto);
            }
        }
        return result;
    }

    default String listToString(List<String> departmentIds) {
        if (departmentIds == null || departmentIds.isEmpty()) {
            return null;
        }
        return String.join(",", departmentIds);
    }

    default List<String> stringToList(String departmentIds) {
        if (departmentIds == null || departmentIds.isEmpty()) {
            return null;
        }
        return Arrays.asList(departmentIds.split(","));
    }


}
