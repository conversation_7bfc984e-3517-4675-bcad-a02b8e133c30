package cn.pf.orch.meiye.interfaces.hazard;

import cn.genn.core.model.page.PageResultDTO;
import cn.pf.orch.meiye.domain.hazard.command.HazardMeasureSaveCommand;
import cn.pf.orch.meiye.domain.hazard.command.HazardMeasureUpdateCommand;
import cn.pf.orch.meiye.domain.hazard.command.MyHazardInfoSaveCommand;
import cn.pf.orch.meiye.domain.hazard.command.MyHazardInfoUpdateCommand;
import cn.pf.orch.meiye.domain.hazard.dto.HazardCountDTO;
import cn.pf.orch.meiye.domain.hazard.dto.HazardInfoDTO;
import cn.pf.orch.meiye.domain.hazard.dto.HazardInfoPageDTO;
import cn.pf.orch.meiye.domain.hazard.query.HazardInfoQuery;
import cn.pf.orch.meiye.domain.risk.command.SendNotifyCommand;
import cn.pf.orch.meiye.service.hazard.HazardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api(tags = "事故隐患-隐患")
@RestController
@RequestMapping("/hazard")
public class HazardController {

    @Resource
    private HazardService hazardService;

    @PostMapping("/page")
    @ApiOperation(value = "分页查询列表")
    public PageResultDTO<HazardInfoPageDTO> page(@ApiParam(value = "查询类") @RequestBody HazardInfoQuery query) {
        return hazardService.page(query);
    }

    @PostMapping("/count")
    @ApiOperation(value = "计算风险数量")
    public List<HazardCountDTO> count(@ApiParam(value = "查询类") @RequestBody HazardInfoQuery query){
        return hazardService.count(query);
    }

    @PostMapping("/get")
    @ApiOperation(value = "根据id查询")
    public HazardInfoDTO get(@ApiParam(value = "查询类") @RequestParam Long id) {
        return hazardService.get(id);
    }

    @PostMapping("/save")
    @ApiOperation(value = "添加")
    public Long save(@RequestBody @Validated MyHazardInfoSaveCommand command) {
        return hazardService.save(command);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public Long change(@RequestBody @Validated MyHazardInfoUpdateCommand command) {
        return hazardService.change(command);
    }

    @PostMapping("/batch/delete")
    @ApiOperation(value = "批量删除")
    public Boolean batchRemove(@RequestBody List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return true;
        }
        return hazardService.batchRemove(idList);
    }

    @PostMapping("/saveHazardMeasure")
    @ApiOperation(value = "新增隐患措施")
    public Boolean saveHazardMeasure(@RequestBody @Validated HazardMeasureSaveCommand command) {
        return hazardService.saveHazardMeasure(command);
    }

    @PostMapping("/updateHazardMeasure")
    @ApiOperation(value = "更新隐患措施")
    public Boolean updateHazardMeasure(@RequestBody @Validated HazardMeasureUpdateCommand command) {
        return hazardService.updateHazardMeasure(command);
    }

    @PostMapping("/batch/deleteHazardMeasure")
    @ApiOperation(value = "删除隐患措施")
    public Boolean deleteHazardMeasure(@RequestBody List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return true;
        }
        return hazardService.deleteHazardMeasure(idList);
    }

    @PostMapping("/sendNotify")
    @ApiOperation(value = "发起飞书通知")
    public Boolean sendNotify(@RequestBody @Validated SendNotifyCommand command) {
        return hazardService.sendNotify(command);
    }

    @PostMapping("/sendApproval")
    @ApiOperation(value = "提交审核")
    public Boolean sendApproval(@RequestParam("id") Long id) {
        return hazardService.sendApproval(id);
    }

    @PostMapping("/close")
    @ApiOperation(value = "关闭隐患")
    public Boolean close(@RequestParam("id") Long id) {
        return hazardService.close(id);
    }
}
