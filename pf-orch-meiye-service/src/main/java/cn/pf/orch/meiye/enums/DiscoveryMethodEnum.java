package cn.pf.orch.meiye.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DiscoveryMethodEnum {

    LIVE("1","现场"),
    MOBILE_VIDEO("2","移动视频"),
    VIDEO_VIEW("3", "视频查阅"),
    ;

    @EnumValue
    @JsonValue
    private final String code;
    private final String description;

}
