package cn.pf.orch.meiye.mapper;

import cn.pf.orch.meiye.domain.system.dto.MyRoleDTO;
import cn.pf.orch.meiye.domain.system.dto.MyUserRoleRelDTO;
import cn.pf.orch.meiye.domain.system.po.MyRolePO;
import cn.pf.orch.meiye.domain.system.query.MyRolePageQuery;
import cn.pf.orch.meiye.enums.AuthTypeEnum;
import cn.pf.orch.meiye.enums.RoleTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MyRoleMapper extends BaseMapper<MyRolePO> {

    IPage<MyRoleDTO> selectByPage(IPage<MyRolePO> page, @Param("query") MyRolePageQuery query, @Param("companyIds") List<Long> companyIds, @Param("admin") boolean admin);

    MyRoleDTO selectDTOById(@Param("id") Long id);

    default List<MyRolePO> selectByName(String name) {
        LambdaQueryWrapper<MyRolePO> wrapper = Wrappers.lambdaQuery(MyRolePO.class)
                .eq(MyRolePO::getName, name);
        return selectList(wrapper);
    }

    default List<MyRolePO> selectByCompanyId(Long companyId) {
        LambdaQueryWrapper<MyRolePO> wrapper = Wrappers.lambdaQuery(MyRolePO.class)
                .eq(MyRolePO::getCompanyId, companyId)
                .eq(MyRolePO::getType, RoleTypeEnum.CUSTOMIZE);
        return selectList(wrapper);
    }

    List<MyUserRoleRelDTO> selectByUserIds(@Param("userIds") List<String> userIds);

    default Long getSystemRoleId(AuthTypeEnum authType) {
        LambdaQueryWrapper<MyRolePO> wrapper = Wrappers.lambdaQuery(MyRolePO.class)
                .eq(MyRolePO::getType, RoleTypeEnum.SYSTEM)
                .eq(MyRolePO::getAuthType, authType);
        return selectOne(wrapper).getId();
    }
}
