package cn.pf.orch.meiye.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;


/**
 * 应急救援检查-检查类
 */
@Getter
@AllArgsConstructor
public enum InspectionType {

    /**
     * 应急救援检查
     */
    EMERGENCY_RESCUE_INSPECTION(1,"应急救援检查"),

    /**
     * 紧急系统避险检查
     */
    EMERGENCY_SYSTEM_INSPECTION(2, "紧急系统避险检查"),

    /**
     * 通讯系统联络检查
     */
    COMMUNICATION_SYSTEM_INSPECTION(3, "通讯系统联络检查"),

    /**
     * 人员位置监测系统检查
     */
    PERSONNEL_TRACKING_INSPECTION(4, "人员位置监测系统检查"),

    /**
     * 压风自救、供水施救系统检查
     */
    AIR_WATER_SUPPLY_INSPECTION(5, "压风自救、供水施救系统检查")
    ;

    @EnumValue
    @JsonValue
    private final Integer code;
    private final String description;

    private static final Map<Integer, InspectionType> VALUES = new HashMap<>();
    static {
        for (final InspectionType item : InspectionType.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static InspectionType of(Integer code) {
        return VALUES.get(code);
    }



}
