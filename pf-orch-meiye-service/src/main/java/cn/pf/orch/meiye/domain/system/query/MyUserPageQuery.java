package cn.pf.orch.meiye.domain.system.query;

import cn.genn.core.model.page.PageSortQuery;
import cn.pf.orch.meiye.enums.ApprovalSignEnum;
import cn.pf.orch.meiye.enums.AuthTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
public class MyUserPageQuery extends PageSortQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户名称")
    private String name;

    @ApiModelProperty(value = "公司层级")
    private AuthTypeEnum authType;

    @ApiModelProperty(value = "用户归属公司名称")
    private String companyName;

    @ApiModelProperty(value = "审核权限")
    private ApprovalSignEnum approvalSign;

}
