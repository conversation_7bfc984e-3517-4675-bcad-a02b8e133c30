package cn.pf.orch.meiye.domain.feishu;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SendRiskDTO {

    private String companyName;

    private String code;

    private String address;

    private String type;

    private String remark;

    private String pcUrl;

    private String appUrl;

    private List<String> openIds;
}
