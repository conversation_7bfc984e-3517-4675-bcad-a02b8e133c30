package cn.pf.orch.meiye.job;

import cn.pf.orch.meiye.common.CacheConstants;
import cn.pf.orch.meiye.service.SsoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class RefreshUserTokenTask {

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private SsoService ssoService;

    /**
     * 实现飞书userToken续时间
     */
    @Scheduled(cron = "0 */10 * * * ?")
    public void executeTask() {
        log.info("定时任务:刷新user_token开始执行");
        long start = System.currentTimeMillis();
        List<String> keys = scanKeys(CacheConstants.getAuthToken("*"));
        for (String key : keys) {
            String token = key.substring(key.lastIndexOf(":") + 1);
            try {
                ssoService.getUserInfo(token);
            } catch (Exception e){
                log.warn("定时任务:刷新user_token失败,token:{}", token,e);
                stringRedisTemplate.delete(CacheConstants.getAuthToken(token));
            }
        }
        log.info("定时任务:执行耗时:{}s", (System.currentTimeMillis() - start) / 1000);
    }

    private List<String> scanKeys(String pattern) {
        List<String> keys = new ArrayList<>();
        ScanOptions options = ScanOptions.scanOptions().match(pattern).build();
        stringRedisTemplate.execute((RedisCallback<Void>) connection -> {
            try (Cursor<byte[]> cursor = connection.scan(options)) {
                while (cursor.hasNext()) {
                    keys.add(new String(cursor.next()));
                }
            }
            return null;
        });
        return keys;
    }
}
