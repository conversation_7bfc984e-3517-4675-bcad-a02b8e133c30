package cn.pf.orch.meiye.domain.safetygenerationstandard.query;

import cn.pf.orch.meiye.enums.ScoringStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 条件查询煤矿体系详情评分记录
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ScoredSystemDetailQuery {

    @ApiModelProperty(value = "安全生产化记录id")
    @NotNull
    private Long scoredRecordId;

    @ApiModelProperty(value = "管理部分得分表ID")
    @NotNull
    private Long scoringSystemCorrelationId;

    @ApiModelProperty(value = "项目内容")
    private String itemContent;

    @ApiModelProperty(value = "状态")
    private ScoringStatusEnum scoringStatus;


    public ScoredSystemDetailQuery(Long scoredRecordId,Long scoringSystemCorrelationId) {
        this.scoredRecordId = scoredRecordId;
        this.scoringSystemCorrelationId = scoringSystemCorrelationId;
    }


}
