package cn.pf.orch.meiye.processor;

import cn.genn.core.exception.BusinessException;
import cn.hutool.core.util.ObjUtil;
import cn.pf.orch.meiye.domain.risk.command.RiskCheckCommand;
import cn.pf.orch.meiye.domain.risk.po.RiskInfoPO;
import cn.pf.orch.meiye.domain.risk.po.RiskMeasurePO;
import cn.pf.orch.meiye.enums.RiskStatusEnum;
import cn.pf.orch.meiye.exception.MessageCode;
import cn.pf.orch.meiye.mapper.RiskInfoMapper;
import cn.pf.orch.meiye.mapper.RiskMeasureMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class RiskCheckProcessor {


    @Resource
    private RiskMeasureMapper riskMeasureMapper;
    @Resource
    private RiskInfoMapper riskInfoMapper;

    public RiskMeasurePO check(RiskCheckCommand command){
        RiskMeasurePO riskMeasurePO = riskMeasureMapper.selectById(command.getRiskMeasureId());
        if(ObjUtil.isEmpty(riskMeasurePO)){
            throw new BusinessException(MessageCode.RISK_MEASURE_NO_EXIST);
        }
        RiskInfoPO riskInfoPO = riskInfoMapper.selectById(riskMeasurePO.getRiskId());
        if(ObjUtil.isEmpty(riskInfoPO)){
            throw new BusinessException(MessageCode.RISK_NOT_EXIST);
        }
        if(!riskInfoPO.getStatus().equals(RiskStatusEnum.NO_CLOSE)){
            throw new BusinessException(MessageCode.STATUS_NOT_MATCH_STOP_CHECK);
        }
        return riskMeasurePO;
    }
}
