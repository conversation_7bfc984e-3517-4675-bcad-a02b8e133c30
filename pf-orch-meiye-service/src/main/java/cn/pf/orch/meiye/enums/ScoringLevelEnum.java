package cn.pf.orch.meiye.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ScoringLevelEnum {

    COMPANY_LEVEL("0","煤矿评级"),

    PROVINCE_LEVEL("1","集团评级"),

    COUNTRIES_LEVEL("2","国家评级")
    ;


    @EnumValue
    @JsonValue
    private final String code;
    private final String desc;

}
