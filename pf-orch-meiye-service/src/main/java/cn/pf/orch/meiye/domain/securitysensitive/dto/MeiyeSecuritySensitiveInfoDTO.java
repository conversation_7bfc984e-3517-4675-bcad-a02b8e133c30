package cn.pf.orch.meiye.domain.securitysensitive.dto;

import cn.pf.orch.meiye.enums.DelEnum;
import cn.pf.orch.meiye.enums.SensitiveCategoryEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;

@Data
@ApiModel(description = "安全敏感信息")
public class MeiyeSecuritySensitiveInfoDTO {

    @ApiModelProperty(value = "主键ID", example = "1", position = 1)
    private Long id;

    @ApiModelProperty(value = "单位ID", example = "1001", required = true, position = 2)
    private Long companyId;

    @ApiModelProperty(value = "单位名称", example = "某某煤矿有限公司", position = 3)
    private String companyName;

    @ApiModelProperty(
            value = "安全敏感类别")
    private SensitiveCategoryEnum sensitiveCategory;

    @ApiModelProperty(value = "安全敏感描述",
            example = "采煤机液压系统泄漏风险",
            required = true)
    private String sensitiveDescription;

    @ApiModelProperty(value = "管控措施",
            example = "1. 每日点检\n2. 每月专项维护\n3. 安装压力报警装置",
            required = true)
    private String controlMeasures;

    @ApiModelProperty(value = "责任部门", example = "设备管理部", required = true, position = 7)
    private String responsibleDepartment;

    @ApiModelProperty(value = "责任部门Id")
    private String responsibleDepartmentId;

    @ApiModelProperty(value = "责任人", example = "张工", required = true, position = 8)
    private String responsiblePerson;

    @ApiModelProperty(value = "责任人工号", example = "EMP2023", position = 9)
    private String responsiblePersonNumber;

    @ApiModelProperty(value = "责任人OpenID", example = "wx123456abcd", position = 10)
    private String responsiblePersonOpenId;

    @ApiModelProperty(value = "管控结果",
            example = "已安装实时监测系统，最近3个月无异常")
    private String controlResult;

    @ApiModelProperty(value = "是否删除（0-未删除，1-已删除）",
            example = "0")
    private DelEnum del;

    @ApiModelProperty(value = "创建人ID", example = "user001", position = 13)
    private String createUserId;

    @ApiModelProperty(value = "创建人", example = "系统管理员", position = 14)
    private String createUserName;

    @ApiModelProperty(value = "更新人ID", example = "user002", position = 15)
    private String updateUserId;

    @ApiModelProperty(value = "更新人", example = "安全专员", position = 16)
    private String updateUserName;

    @ApiModelProperty(value = "创建时间", example = "2023-06-01 08:00:00", position = 17)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间", example = "2023-06-15 14:30:00", position = 18)
    private LocalDateTime updateTime;
}
