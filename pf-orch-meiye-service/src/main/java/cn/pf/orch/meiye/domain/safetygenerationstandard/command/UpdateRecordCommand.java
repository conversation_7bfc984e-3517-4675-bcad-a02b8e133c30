package cn.pf.orch.meiye.domain.safetygenerationstandard.command;

import cn.pf.orch.meiye.enums.ScoringLevelEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
@ApiModel("更新煤矿评分记录")
public class UpdateRecordCommand {

    @ApiModelProperty(value = "主键id")
    @NotNull
    private Long id;

    @ApiModelProperty(value = "级别")
    @NotNull
    private ScoringLevelEnum scoreLevel;

    @ApiModelProperty(value = "评分时间")
    @NotNull
    private LocalDate scoreDate;
}
