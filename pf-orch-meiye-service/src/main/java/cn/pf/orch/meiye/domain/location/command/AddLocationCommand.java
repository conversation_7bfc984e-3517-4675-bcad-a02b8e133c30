package cn.pf.orch.meiye.domain.location.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;

@Data
public class  AddLocationCommand {

    @ApiModelProperty(value = "地点名称")
    @NotNull
    private String name;

    @ApiModelProperty(value = "所属煤矿Id")
    @NotNull
    private Long companyId;

    @ApiModelProperty(value = "所属煤矿名称")
    @NotNull
    private String companyName;

    @ApiModelProperty(value = "上级地点id")
    private Integer parentId;

    @ApiModelProperty(value = "上级地点级别")
    private Integer parentLevel;

    @ApiModelProperty(value = "当前登录人id")
    private String createUserId;

    @ApiModelProperty(value = "当前登录人名称")
    private String createUserName;

}
