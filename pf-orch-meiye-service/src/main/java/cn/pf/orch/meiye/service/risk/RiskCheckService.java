package cn.pf.orch.meiye.service.risk;

import cn.genn.core.model.page.PageResultDTO;
import cn.hutool.core.util.StrUtil;
import cn.pf.orch.meiye.assembler.RiskCheckAssembler;
import cn.pf.orch.meiye.config.CurrentUserHolder;
import cn.pf.orch.meiye.domain.risk.command.RiskCheckCommand;
import cn.pf.orch.meiye.domain.risk.dto.RiskCheckDTO;
import cn.pf.orch.meiye.domain.risk.dto.RiskCheckPageDTO;
import cn.pf.orch.meiye.domain.risk.po.RiskCheckPO;
import cn.pf.orch.meiye.domain.risk.po.RiskMeasurePO;
import cn.pf.orch.meiye.domain.risk.query.RiskCheckPageQuery;
import cn.pf.orch.meiye.mapper.RiskCheckMapper;
import cn.pf.orch.meiye.processor.RiskCheckProcessor;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;

@Slf4j
@Service
public class RiskCheckService {

    @Resource
    private RiskCheckMapper riskCheckMapper;
    @Resource
    private RiskCheckProcessor processor;
    @Resource
    private RiskCheckAssembler assembler;

    public PageResultDTO<RiskCheckPageDTO> page(RiskCheckPageQuery query) {
        query.setCompanyIds(CurrentUserHolder.getRangeCompanyIds());
        PageResultDTO<RiskCheckPageDTO> pageResult = assembler.toPage(riskCheckMapper.selectByPage(new Page<>(query.getPageNo(), query.getPageSize()), query));
        return pageResult;
    }

    public RiskCheckDTO get(Long id) {
        RiskCheckDTO riskCheckDTO = riskCheckMapper.selectDetailById(id);
        if(StrUtil.isNotBlank(riskCheckDTO.getFileStr())){
            riskCheckDTO.setFiles(Arrays.asList(riskCheckDTO.getFileStr().split(",")));
        }

        return riskCheckDTO;
    }


    public void check(RiskCheckCommand command) {
        RiskMeasurePO riskMeasurePO = processor.check(command);
        RiskCheckPO po = RiskCheckPO.builder()
                .companyId(riskMeasurePO.getCompanyId())
                .riskMeasureId(riskMeasurePO.getId())
                .riskId(riskMeasurePO.getRiskId())
                .level(command.getCheckLevel())
                .result(command.getCheckResult())
                .detail(command.getDetail())
                .files(String.join(",", command.getFiles()))
                .build();
        riskCheckMapper.insert(po);
    }
}
