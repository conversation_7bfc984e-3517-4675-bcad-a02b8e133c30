package cn.pf.orch.meiye.mapper;

import cn.pf.orch.meiye.domain.risk.po.RiskMeasurePO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface RiskMeasureMapper extends BaseMapper<RiskMeasurePO> {

    default List<RiskMeasurePO>  selectByRiskId(Long riskId){
        LambdaQueryWrapper<RiskMeasurePO> wrapper = Wrappers.lambdaQuery(RiskMeasurePO.class)
                .eq(RiskMeasurePO::getRiskId, riskId)
                .orderByAsc(RiskMeasurePO::getCreateTime);
        return selectList(wrapper);
    }
}
