package cn.pf.orch.meiye.assembler;

import cn.pf.orch.meiye.domain.safetyaccident.command.AddCommand;
import cn.pf.orch.meiye.domain.safetyaccident.command.UpdateCommand;
import cn.pf.orch.meiye.domain.safetyaccident.dto.MeiyeSafetyAccidentDTO;
import cn.pf.orch.meiye.domain.safetyaccident.po.MeiyeSafetyAccidentRecords;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SafetyAccidentAssmbler {

    MeiyeSafetyAccidentRecords addCommandToPO(AddCommand command);

    MeiyeSafetyAccidentRecords updateCommandToPO(UpdateCommand command);

    MeiyeSafetyAccidentDTO queryAessmble(MeiyeSafetyAccidentRecords po);

    List<MeiyeSafetyAccidentDTO> queryAessmbleList(List<MeiyeSafetyAccidentRecords> po);




}
