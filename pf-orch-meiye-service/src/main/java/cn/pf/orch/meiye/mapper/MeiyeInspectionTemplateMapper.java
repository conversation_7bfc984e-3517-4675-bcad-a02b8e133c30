package cn.pf.orch.meiye.mapper;

import cn.pf.orch.meiye.domain.emergencyrescuecheck.dto.EmergencyRescueCheckInfoDTO;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.dto.InspectionDropDownDTO;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.po.MeiyeInspectionTemplate;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.query.EmergencyRescueCheckInfoQuery;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 检查项目模板表(MeiyeInspectionTemplate)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-15 11:29:44
 */
public interface MeiyeInspectionTemplateMapper extends BaseMapper<MeiyeInspectionTemplate> {



    Page<EmergencyRescueCheckInfoDTO> queryEmergencyCheckInfo(IPage<EmergencyRescueCheckInfoDTO> page, @Param("query") EmergencyRescueCheckInfoQuery query);


    @Select("SELECT\n" +
            "    inspection_type as inspectionType,\n" +
            "    GROUP_CONCAT(DISTINCT project ORDER BY project SEPARATOR '、') AS projectsList\n" +
            "FROM\n" +
            "    meiye_inspection_template\n" +
            "GROUP BY\n" +
            "    inspection_type;")
    List<Map<String,String>> dropDown();

}

