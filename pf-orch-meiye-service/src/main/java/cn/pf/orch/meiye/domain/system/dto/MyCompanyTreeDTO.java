package cn.pf.orch.meiye.domain.system.dto;

import cn.genn.core.model.enums.DeletedEnum;
import cn.pf.orch.meiye.enums.AuthTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MyCompanyTreeDTO {

    @ApiModelProperty(value = "")
    private Long id;

    @ApiModelProperty(value = "父id")
    private Long pid;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "权限类型（集团，区域公司，煤矿）")
    private AuthTypeEnum authType;

    @ApiModelProperty(value = "飞书部门id映射")
    private String departmentIds;

    @ApiModelProperty(value = "逻辑删除（0：未删除；1：删除）")
    private DeletedEnum deleted;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建用户ID")
    private String createUserId;

    @ApiModelProperty(value = "创建用户名")
    private String createUserName;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新用户ID")
    private String updateUserId;

    @ApiModelProperty(value = "更新用户名")
    private String updateUserName;

    private List<MyCompanyTreeDTO> children;


}
