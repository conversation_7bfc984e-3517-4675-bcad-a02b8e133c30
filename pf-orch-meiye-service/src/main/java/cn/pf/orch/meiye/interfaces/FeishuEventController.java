package cn.pf.orch.meiye.interfaces;

import cn.genn.web.spring.annotation.ResponseResultWrapper;
import cn.hutool.core.util.StrUtil;
import cn.pf.orch.feishu.model.callback.EventCallBackDTO;
import cn.pf.orch.feishu.model.callback.EventCallbackCommand;
import cn.pf.orch.meiye.service.FeishuEventService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/white/feishu/event")
public class FeishuEventController {

    @Resource
    private FeishuEventService feishuEventService;

    @PostMapping("/subscribe")
    @ResponseResultWrapper(ignore = true)
    public <T> EventCallBackDTO callback(@RequestBody EventCallbackCommand<T> command){
        if (StrUtil.isNotBlank(command.getChallenge())){
            return EventCallBackDTO.builder().challenge(command.getChallenge()).build();
        }
        return feishuEventService.callback(command);
    }

}
