package cn.pf.orch.meiye.interfaces;

import cn.pf.orch.meiye.config.CurrentUserHolder;
import cn.pf.orch.meiye.domain.risk.command.SendNotifyCommand;
import cn.pf.orch.meiye.domain.securitysensitive.command.AddCommand;
import cn.pf.orch.meiye.domain.securitysensitive.command.UpdateCommand;
import cn.pf.orch.meiye.domain.securitysensitive.dto.MeiyeSecuritySensitiveInfoDTO;
import cn.pf.orch.meiye.domain.securitysensitive.query.PageQuery;
import cn.pf.orch.meiye.service.securitysensitive.MeiyeSecuritySensitiveInfoService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;

@RestController
@Api(tags = "安全敏感信息表")
@RequestMapping("securitysensitive")
public class MeiyeSecuritySensitiveController {

    @Resource
    private MeiyeSecuritySensitiveInfoService securitySensitiveInfoService;

    @ApiOperation("新增")
    @PostMapping("/add")
    public void add(@RequestBody @Validated @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) AddCommand command) {
        securitySensitiveInfoService.add(command);
    }

    @ApiOperation("删除")
    @GetMapping("/delete")
    public void delete(@NotNull Integer id) {
        securitySensitiveInfoService.delete(id);
    }

    @ApiOperation("编辑")
    @PostMapping("/update")
    public void update(@RequestBody @Validated UpdateCommand command) {
        securitySensitiveInfoService.update(command);
    }

    @ApiOperation("分页")
    @PostMapping("/pageQuery")
    public Page<MeiyeSecuritySensitiveInfoDTO> pageQuery(@RequestBody @Validated @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) PageQuery query) {
        List<Long> rangeCompanyIds = CurrentUserHolder.getRangeCompanyIds();
        query.setRangeCompanyIds(rangeCompanyIds);
        return securitySensitiveInfoService.pageQuery(query);
    }

    @ApiOperation("/详情接口")
    @GetMapping("/queryDetail")
    public MeiyeSecuritySensitiveInfoDTO queryDetail(@NotNull Integer id) {
        return securitySensitiveInfoService.queryDetail(id);
    }

    @ApiOperation("通知")
    @PostMapping("sendNotify")
    public Boolean sendNotify(@RequestBody @Validated SendNotifyCommand command) {
        return securitySensitiveInfoService.sendNotify(command);
    }
}
