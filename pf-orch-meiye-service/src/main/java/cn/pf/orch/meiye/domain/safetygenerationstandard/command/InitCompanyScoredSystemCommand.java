package cn.pf.orch.meiye.domain.safetygenerationstandard.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import javax.validation.constraints.NotNull;

/**
 * 初始化煤矿评分体系信息
 */
@Data
@ApiModel("初始化煤矿体系评分")
@AllArgsConstructor
public class InitCompanyScoredSystemCommand {

    @ApiModelProperty("安全生产标准化记录id")
    @NotNull
    private Long scoringRecordId;



}
