package cn.pf.orch.meiye.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Resource
    @Lazy
    private SsoAuthFilter ssoAuthFilter;

    private static final String[] IGNORE_PATHS = {"/static/**", "/public/**", "/resources/**","/swagger-resources/**", "/webjars/**", "/v2/**", "/swagger-ui.html/**", "/doc.html", "/favicon.ico", "/error"};

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(ssoAuthFilter).addPathPatterns("/**").excludePathPatterns(IGNORE_PATHS);
    }

    @Bean
    public MetaObjectHandler metaObjectHandler() {
        return new InsertOrUpdateMetaObjectHandler();
    }
}
