package cn.pf.orch.meiye.assembler;

import cn.genn.core.model.page.PageResultDTO;
import cn.pf.orch.meiye.domain.hazard.command.MyHazardIdentifySaveCommand;
import cn.pf.orch.meiye.domain.hazard.command.MyHazardIdentifyUpdateCommand;
import cn.pf.orch.meiye.domain.hazard.dto.HazardIdentifyDTO;
import cn.pf.orch.meiye.domain.hazard.po.HazardIdentifyPO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface HazardIdentifyAssembler {

    HazardIdentifyPO command2PO(MyHazardIdentifySaveCommand command);

    HazardIdentifyPO updateCommand2PO(MyHazardIdentifyUpdateCommand command);

    HazardIdentifyDTO PO2DTO(HazardIdentifyPO po);

    List<HazardIdentifyDTO> PO2DTO(List<HazardIdentifyPO> list);

    @Mapping(target = "pageNo", source = "current")
    @Mapping(target = "pageSize", source = "size")
    @Mapping(target = "list", source = "records")
    @Mapping(target = "totalPages", source = "pages")
    PageResultDTO<HazardIdentifyDTO> toPage(IPage<HazardIdentifyDTO> poPage);
}
