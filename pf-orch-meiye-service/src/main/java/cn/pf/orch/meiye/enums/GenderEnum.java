package cn.pf.orch.meiye.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum GenderEnum {

    SECRECY(0, "保密"),
    MALE(1, "男"),
    FEMALE(2, "女"),
    OTHER(3, "其他"),
    ;

    @EnumValue
    @JsonValue
    private final Integer code;

    private final String description;


    private static final Map<Integer, GenderEnum> VALUES = new HashMap<>();

    static {
        for (final GenderEnum item : GenderEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static GenderEnum of(Integer code) {
        return VALUES.get(code);
    }

}
