package cn.pf.orch.meiye.mapper;

import cn.pf.orch.meiye.domain.risk.po.MyApprovalPO;
import cn.pf.orch.meiye.domain.system.dto.HomeMessagesDTO;
import cn.pf.orch.meiye.domain.system.query.HomePageQuery;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface MyApprovalMapper extends BaseMapper<MyApprovalPO> {

    IPage<HomeMessagesDTO> homeMessagePage(IPage<MyApprovalPO> page, @Param("query") HomePageQuery query);
}
