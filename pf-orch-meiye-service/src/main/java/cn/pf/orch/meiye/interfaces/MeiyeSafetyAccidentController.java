package cn.pf.orch.meiye.interfaces;

import cn.pf.orch.meiye.config.CurrentUserHolder;
import cn.pf.orch.meiye.domain.safetyaccident.command.AddCommand;
import cn.pf.orch.meiye.domain.safetyaccident.command.UpdateCommand;
import cn.pf.orch.meiye.domain.safetyaccident.dto.MeiyeSafetyAccidentDTO;
import cn.pf.orch.meiye.domain.safetyaccident.query.PageQuery;
import cn.pf.orch.meiye.service.safetyaccident.MeiyeSafetyAccidentRecordsService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;

@RestController
@Api(tags = "事故台账模块")
@RequestMapping("/safetyaccident")
public class MeiyeSafetyAccidentController {

    @Resource
    private MeiyeSafetyAccidentRecordsService service;

    @ApiOperation(value = "新增")
    @PostMapping("/add")
    public void add(@RequestBody @Validated @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) AddCommand record){
        service.add(record);
    }

    @ApiOperation(value = "编辑")
    @PostMapping("/update")
    public void update(@RequestBody @Validated @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) UpdateCommand record){
        service.update(record);
    }

    @ApiOperation(value = "删除")
    @GetMapping("/delete")
    public void delete(@NotNull Integer id){
        service.delete(id);
    }


    @ApiOperation(value = "分页")
    @PostMapping("/page")
    public Page<MeiyeSafetyAccidentDTO> query(@RequestBody @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) PageQuery query){
        List<Long> rangeCompanyIds = CurrentUserHolder.getRangeCompanyIds();
        query.setRangeCompanyIds(rangeCompanyIds);
        return service.query(query);
    }

    @ApiOperation("详情")
    @GetMapping("/detail")
    public MeiyeSafetyAccidentDTO detail(@NotNull Integer id){
        return service.detail(id);
    }

}
