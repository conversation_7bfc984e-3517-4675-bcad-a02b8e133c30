package cn.pf.orch.meiye.domain.emergencyrescuecheck.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ApiModel("应急救援检查列表")
public class EmergencyRescueCheckInfoDTO {

    @ApiModelProperty(value = "企业ID", example = "1001")
    private Long companyId;

    @ApiModelProperty(value = "企业名称", example = "某某科技有限公司")
    private String companyName;

    @ApiModelProperty(value = "检查项模板id", example = "某某科技有限公司")
    private Long templateId;

    @ApiModelProperty(value = "检查类型", example = "日常检查")
    private String inspectionType;

    @ApiModelProperty(value = "检查项序号")
    private Integer serialNumber;

    @ApiModelProperty(value = "主要资料和方法")
    private String materialsMethods;

    @ApiModelProperty(value = "项目名称", example = "安全生产检查")
    private String project;

    @ApiModelProperty(value = "检查内容", example = "消防设施、安全通道检查")
    private String content;

    @ApiModelProperty(value = "依据集合")
    private String referenceLink;

    @ApiModelProperty(value = "问题Id")
    private Long problemId;

    @ApiModelProperty(value = "问题描述", example = "灭火器过期，安全通道堵塞")
    private String description;

    @ApiModelProperty(value = "问题附件")
    private String attachment;

    @ApiModelProperty(value = "创建人ID", example = "user001")
    private String createUserId;

    @ApiModelProperty(value = "创建人姓名", example = "张三")
    private String createUserName;

    @ApiModelProperty(value = "创建时间", example = "2023-01-01T10:00:00")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人ID", example = "user002")
    private String updateUserId;

    @ApiModelProperty(value = "更新人姓名", example = "李四")
    private String updateUserName;

    @ApiModelProperty(value = "更新时间", example = "2023-01-02T15:30:00")
    private LocalDateTime updateTime;




}
