package cn.pf.orch.meiye.domain.hazard.po;

import cn.genn.core.model.enums.DeletedEnum;
import cn.pf.orch.meiye.enums.ControlStatusEnum;
import cn.pf.orch.meiye.enums.RiskLevelEnum;
import cn.pf.orch.meiye.enums.RiskStatusEnum;
import cn.pf.orch.meiye.enums.RiskTypeEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * HazardInfoPO对象
 *
 * <AUTHOR>
 * @desc 隐患表
 */
@Data
@Accessors(chain = true)
@TableName(value = "hazard_info", autoResultMap = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HazardInfoPO {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 编号
     */
    @TableField("code")
    private String code;

    /**
     * 公司id
     */
    @TableField("company_id")
    private Long companyId;

    /**
     * 隐患排查id
     */
    @TableField("hazard_identify_id")
    private Long hazardIdentifyId;

    /**
     * 描述
     */
    @TableField("remark")
    private String remark;

    /**
     * 隐患等级
     */
    @TableField("level")
    private RiskLevelEnum level;

    /**
     * 隐患类别
     */
    @TableField("type")
    private RiskTypeEnum type;

    /**
     * 隐患状态（待提交，待审核，已驳回，待关闭，已关闭）
     */
    @TableField("status")
    private RiskStatusEnum status;

    /**
     * 可控状态（0可控，1失控）
     */
    @TableField("control_status")
    private ControlStatusEnum controlStatus;

    /**
     * 隐患责任人
     */
    @TableField("owner")
    private String owner;

    /**
     * 责任人名称
     */
    @TableField("owner_name")
    private String ownerName;

    /**
     * 审批id
     */
    @TableField("approval_id")
    private String approvalId;

    /**
     * 删除状态（0未删除，1已删除）
     */
    @TableField("deleted")
    private DeletedEnum deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;

    /**
     * 创建人
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人id
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    /**
     * 更新人
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 整改单位id
     */
    @TableField("department_id")
    private String departmentId;

    /**
     * 整改单位名称
     */
    @TableField("department_name")
    private String departmentName;

    /**
     * 整改责任人id
     */
    @TableField("rectify_owner")
    private String rectifyOwner;

    /**
     * 整改责任人名称
     */
    @TableField("rectify_owner_name")
    private String rectifyOwnerName;

    /**
     * 整改期限
     */
    @TableField("rectify_time")
    private LocalDateTime rectifyTime;

    /**
     * 整改资金（元）
     */
    @TableField("rectify_money")
    private BigDecimal rectifyMoney;

}

