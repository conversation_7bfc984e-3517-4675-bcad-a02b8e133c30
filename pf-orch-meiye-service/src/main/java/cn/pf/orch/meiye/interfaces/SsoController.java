package cn.pf.orch.meiye.interfaces;

import cn.hutool.core.util.StrUtil;
import cn.pf.orch.feishu.FeishuAppClient;
import cn.pf.orch.feishu.model.SignatureModel;
import cn.pf.orch.meiye.aspect.UserLog;
import cn.pf.orch.meiye.config.CurrentUserHolder;
import cn.pf.orch.meiye.config.UserInfoDTO;
import cn.pf.orch.meiye.domain.system.dto.UserTokenDTO;
import cn.pf.orch.meiye.domain.system.query.JsTicketQuery;
import cn.pf.orch.meiye.domain.system.query.SsoUserLoginQuery;
import cn.pf.orch.meiye.domain.system.query.SsoUserTokenQuery;
import cn.pf.orch.meiye.enums.OperateTypeEnum;
import cn.pf.orch.meiye.service.SsoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@Api(tags = "免登相关")
@RestController
@RequestMapping("/sso/web")
public class SsoController {

    @Resource
    private SsoService ssoService;
    @Resource
    private FeishuAppClient feishuAppClient;

    @GetMapping("/getCode")
    @ApiOperation("获取授权码-测试接口")
    public String getCode(@RequestParam("code") String code){
        log.info("授权码:{}",code);
        return code;
    }

    @PostMapping("/getUserToken")
    @ApiOperation("免登过程-获取token")
    @UserLog(operateType = OperateTypeEnum.LOGIN, detail = "#username +'(' + #fsOpenId+')已登录'")
    public UserTokenDTO getUserToken(@Validated @RequestBody SsoUserLoginQuery query) {
        log.info("getUserToken query:{}",query);
        return ssoService.getUserToken(query);
    }

    @PostMapping("/getJsTicket")
    @ApiOperation("获取js_sdk_ticket")
    public SignatureModel getJsTicket(@Validated @RequestBody JsTicketQuery query) {
        return feishuAppClient.getAuthService().generateSignature(query.getUrl());
    }

    @PostMapping("/getUserInfo")
    @ApiOperation("获取用户信息")
    public UserInfoDTO getUserInfo(@Validated @RequestBody SsoUserTokenQuery query) {
        String token = query.getToken();
        if (StrUtil.isBlank(token)) {
            query.setToken(CurrentUserHolder.getToken());
        }
        UserInfoDTO userInfo = ssoService.getUserInfo(query.getToken());
        userInfo.setAuthConfig(ssoService.getAuthConfig(userInfo.getOpenId()));
        ssoService.getResource(userInfo);
        return userInfo;
    }

    @PostMapping("/refreshToken")
    @ApiOperation("刷新token")
    public Boolean refreshToken(@Validated @RequestBody SsoUserTokenQuery query) {
        return ssoService.refreshToken(query);
    }

    @PostMapping("/logout")
    @ApiOperation("登出")
    public Boolean logout(@Validated @RequestBody SsoUserTokenQuery query) {
        return ssoService.logout(query.getToken());
    }


}
