package cn.pf.orch.meiye.enums;

import cn.genn.core.exception.BusinessException;
import cn.pf.orch.meiye.exception.MessageCode;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum CheckLevelEnum {

    GROUP("group","集团抽查"),
    REGION("region","区域公司抽查"),
    COLLIERY("colliery","煤矿自查"),
    ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;


    private static final Map<String, CheckLevelEnum> VALUES = new HashMap<>();

    static {
        for (final CheckLevelEnum item : CheckLevelEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static CheckLevelEnum of(String code) {
        return VALUES.get(code);
    }

    public static CheckLevelEnum getByAuthType(AuthTypeEnum authType) {
        switch (authType){
            case GROUP:
                return GROUP;
            case COLLIERY:
                return COLLIERY;
            case REGION:
                return REGION;
            default:
                throw new BusinessException(MessageCode.AUTH_TYPE_GET_ERROR);
        }
    }
}
