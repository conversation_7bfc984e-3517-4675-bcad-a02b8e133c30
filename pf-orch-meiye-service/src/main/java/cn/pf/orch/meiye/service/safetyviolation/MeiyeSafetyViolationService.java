package cn.pf.orch.meiye.service.safetyviolation;

import cn.pf.orch.meiye.domain.safetyviolation.command.AddCommand;
import cn.pf.orch.meiye.domain.safetyviolation.command.UpdateCommand;
import cn.pf.orch.meiye.domain.safetyviolation.dto.MeiyeSafetyViolationDTO;
import cn.pf.orch.meiye.domain.safetyviolation.po.MeiyeSafetyViolation;
import cn.pf.orch.meiye.domain.safetyviolation.query.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.constraints.NotNull;

/**
 * 三违记录表(MeiyeSafetyViolation)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-14 10:27:37
 */
public interface MeiyeSafetyViolationService extends IService<MeiyeSafetyViolation> {

    void add(AddCommand command);

    void update(UpdateCommand command);

    void delete(Long id);

    Page<MeiyeSafetyViolationDTO> page(PageQuery query);

    MeiyeSafetyViolationDTO detail(Long id);
}

