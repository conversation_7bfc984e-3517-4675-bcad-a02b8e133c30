package cn.pf.orch.meiye.properties;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class LoginProperties {

    /**
     * token生效时间,单位秒,默认7天
     */
    private long timeOut = 3600 * 24 * 7;

    /**
     * feishu_user_token 刷新频率 单位秒,默认30分钟
     */
    private long userTokenRefreshTime = 60 * 30;

    //超管账号
    private List<String> adminTelephones = new ArrayList<>();
}
