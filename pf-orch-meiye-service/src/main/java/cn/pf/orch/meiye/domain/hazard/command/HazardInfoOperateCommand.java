package cn.pf.orch.meiye.domain.hazard.command;

import cn.genn.core.model.enums.DeletedEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * HazardInfo操作对象
 *
 * <AUTHOR>
 */
@Data
public class HazardInfoOperateCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "编号")
    private String code;

    @ApiModelProperty(value = "公司id")
    private Long companyId;

    @ApiModelProperty(value = "隐患排查id")
    private Long hazardIdentifyId;

    @ApiModelProperty(value = "描述")
    private String remark;

    @ApiModelProperty(value = "隐患等级")
    private Integer level;

    @ApiModelProperty(value = "隐患类别")
    private String type;

    @ApiModelProperty(value = "隐患状态（待提交，待审核，已驳回，待关闭，已关闭）")
    private Byte status;

    @ApiModelProperty(value = "可控状态（0可控，1失控）")
    private Byte controlStatus;

    @ApiModelProperty(value = "隐患责任人")
    private String owner;

    @ApiModelProperty(value = "审批id")
    private String apprivalId;

    @ApiModelProperty(value = "删除状态（0未删除，1已删除）")
    private DeletedEnum deleted;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人id")
    private String createUserId;

    @ApiModelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人id")
    private String updateUserId;

    @ApiModelProperty(value = "更新人")
    private String updateUserName;


}

