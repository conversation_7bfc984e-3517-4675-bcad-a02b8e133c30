package cn.pf.orch.meiye.domain.feishu;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 飞书事件请求headerDTO。
 *
 * <AUTHOR>
 * @since 2024/8/22
 */
public class FeishuEventHeaderCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 事件 ID。
     */
    @ApiModelProperty(value = "事件 ID")
    private String eventId;

    /**
     * 事件类型。
     */
    @ApiModelProperty(value = "事件类型")
    private String eventType;

    /**
     * 事件创建时间戳（单位：毫秒）。
     */
    @ApiModelProperty(value = "事件创建时间戳（单位：毫秒）")
    private String createTime;

    /**
     * 事件 Token。
     */
    @ApiModelProperty(value = "事件 Token")
    private String token;

    /**
     * 应用 ID。
     */
    @ApiModelProperty(value = "应用 ID")
    private String appId;

    /**
     * 租户 Key。
     */
    @ApiModelProperty(value = "租户 Key")
    private String tenantKey;
}
