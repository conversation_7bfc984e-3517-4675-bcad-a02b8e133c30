package cn.pf.orch.meiye.domain.safetygenerationstandard.po;

import cn.pf.orch.meiye.enums.DelEnum;
import cn.pf.orch.meiye.enums.ScoringStatusEnum;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 管理项明细评分记录表(ScoringDetailCorrelation)表实体类
 *
 * <AUTHOR>
 * @since 2025-04-22 16:49:34
 */
@Data
@TableName("scoring_detail_correlation")
public class ScoringDetailCorrelationPO extends Model<ScoringDetailCorrelationPO> {
    //主键ID
    private Long id;
    //安全生产标准表ID
    private Long scoringRecordId;
    //管理部分得分表ID
    private Long scoringSystemCorrelationId;
    //体系详情模板ID
    private Long scoringSystemDetailId;
    //项目评分(0-100)
    private BigDecimal actualScore;
    //状态（0-未评分；1-已评分）
    private ScoringStatusEnum status;

    private DelEnum del;
}

