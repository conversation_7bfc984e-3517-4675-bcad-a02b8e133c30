package cn.pf.orch.meiye.domain.system.po;

import cn.genn.core.model.enums.DeletedEnum;
import cn.pf.orch.meiye.enums.ApprovalSignEnum;
import cn.pf.orch.meiye.enums.AuthTypeEnum;
import cn.pf.orch.meiye.enums.GenderEnum;
import cn.pf.orch.meiye.enums.UserTypeEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * MyUserPO对象
 *
 * <AUTHOR>
 * @desc 用户信息表
 */
@Data
@Accessors(chain = true)
@TableName(value = "my_user", autoResultMap = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MyUserPO {

    /**
     * 用户的open_id，应用内用户的唯一标识
     */
    @TableId
    private String id;

    /**
     * 用户名
     */
    @TableField("name")
    private String name;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 手机号
     */
    @TableField("mobile")
    private String mobile;

    /**
     * 性别
     */
    @TableField("gender")
    private GenderEnum gender;

    /**
     * 权限类型（集团，区域公司，煤矿）
     */
    @TableField("auth_type")
    private AuthTypeEnum authType;

    /**
     * 所属公司
     */
    @TableField("company_id")
    private Long companyId;

    /**
     * 头像的文件Key
     */
    @TableField("avatar_key")
    private String avatarKey;

    /**
     * 用户类型(1:内部,2:外部)
     */
    @TableField("type")
    private UserTypeEnum type;

    /**
     * 用户头像信息
     */
    @TableField("avatar_origin")
    private String avatarOrigin;

    /**
     * 工号
     */
    @TableField("employee_no")
    private String employeeNo;

    /**
     * 员工类型
     */
    @TableField("employee_type")
    private Integer employeeType;

    /**
     * 企业邮箱
     */
    @TableField("enterprise_email")
    private String enterpriseEmail;

    @TableField("department_id")
    private String departmentId;

    /**
     * 是否暂停用户
     */
    @TableField("is_frozen")
    private Boolean isFrozen;

    /**
     * 审核标签
     */
    @TableField("approval_sign")
    private ApprovalSignEnum approvalSign;

    /**
     * 逻辑删除（0：未删除；1：删除）
     */
    @TableField("deleted")
    @TableLogic
    private DeletedEnum deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;

    /**
     * 创建用户名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新用户ID
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    /**
     * 更新用户名
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

}

