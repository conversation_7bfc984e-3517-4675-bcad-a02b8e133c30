package cn.pf.orch.meiye.assembler;

import cn.genn.core.model.page.PageResultDTO;
import cn.pf.orch.meiye.domain.risk.command.MyRiskIdentifySaveCommand;
import cn.pf.orch.meiye.domain.risk.command.MyRiskIdentifyUpdateCommand;
import cn.pf.orch.meiye.domain.risk.dto.RiskIdentifyDTO;
import cn.pf.orch.meiye.domain.risk.po.RiskIdentifyPO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RiskIdentifyAssembler {

    RiskIdentifyPO command2PO(MyRiskIdentifySaveCommand command);

    RiskIdentifyPO updateCommand2PO(MyRiskIdentifyUpdateCommand command);

    RiskIdentifyDTO PO2DTO(RiskIdentifyPO po);

    List<RiskIdentifyDTO> PO2DTO(List<RiskIdentifyPO> list);

    @Mapping(target = "pageNo", source = "current")
    @Mapping(target = "pageSize", source = "size")
    @Mapping(target = "list", source = "records")
    @Mapping(target = "totalPages", source = "pages")
    PageResultDTO<RiskIdentifyDTO> toPage(IPage<RiskIdentifyDTO> poPage);
}
