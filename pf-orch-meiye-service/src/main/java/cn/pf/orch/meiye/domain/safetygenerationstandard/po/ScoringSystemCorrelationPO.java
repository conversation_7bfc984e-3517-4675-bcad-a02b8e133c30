package cn.pf.orch.meiye.domain.safetygenerationstandard.po;

import cn.pf.orch.meiye.enums.DelEnum;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 管理项-评分记录中间表(ScoringSystemCorrelation)表实体类
 *
 * <AUTHOR>
 * @since 2025-04-22 16:52:44
 */
@Data
@TableName("scoring_system_correlation")
public class ScoringSystemCorrelationPO extends Model<ScoringSystemCorrelationPO> {
    //主键ID
    private Long id;
    //安全生产标准表ID
    private Long scoringRecordId;
    //管理项模板ID
    private Long scoringSystemId;
    //管理项评分(0-100)
    private BigDecimal actualScore;
    //管理项评分进度(0-100%)
    private BigDecimal progress;

    private DelEnum del;


}

