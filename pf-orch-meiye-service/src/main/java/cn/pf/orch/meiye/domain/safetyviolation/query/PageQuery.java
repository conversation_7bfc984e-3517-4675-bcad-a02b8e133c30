package cn.pf.orch.meiye.domain.safetyviolation.query;

import cn.genn.core.model.page.PageSortQuery;
import cn.pf.orch.meiye.enums.AccidentCategoryEnum;
import cn.pf.orch.meiye.enums.AccidentNature;
import cn.pf.orch.meiye.enums.ViolationTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "PageQuery", description = "三违信息分页查询")
public class PageQuery extends PageSortQuery {

    @ApiModelProperty(value = "煤矿ID", example = "1001")
    private Long companyId;

    @ApiModelProperty(value = "违章经过及原因（模糊查询）", example = "未戴安全帽")
    private String violationDetails;

    @ApiModelProperty(value = "三违性质",
            example = "违章")
    private ViolationTypeEnum violationType;

    @ApiModelProperty(value = "被查处人姓名（模糊查询）", example = "张三")
    private String personName;

    @ApiModelProperty(value = "查出人姓名（模糊查询）", example = "李四")
    private String discoverer;

    @ApiModelProperty(value = "违章日期-开始（查询范围）",
            example = "2023-01-01")
    private LocalDate violationDateStart;

    @ApiModelProperty(value = "违章日期-结束（查询范围）",
            example = "2023-12-31")
    private LocalDate violationDateEnd;

    @ApiModelProperty(value = "违章地点ID", example = "2001")
    private Long violationLocationId;

    private List<Long> rangeCompanyIds;


}
