package cn.pf.orch.meiye.domain.system.po;

import cn.genn.core.model.enums.DeletedEnum;
import cn.pf.orch.meiye.enums.AuthTypeEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * MyCompanyPO对象
 *
 * <AUTHOR>
 * @desc 公司表
 */
@Data
@Accessors(chain = true)
@TableName(value = "my_company", autoResultMap = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MyCompanyPO {

    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 父id
     */
    @TableField("pid")
    private Long pid;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 权限类型（集团，区域公司，煤矿）
     */
    @TableField("auth_type")
    private AuthTypeEnum authType;

    /**
     * 飞书部门id映射
     */
    @TableField("department_ids")
    private String departmentIds;

    /**
     * 逻辑删除（0：未删除；1：删除）
     */
    @TableField("deleted")
    @TableLogic
    private DeletedEnum deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;

    /**
     * 创建用户名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新用户ID
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    /**
     * 更新用户名
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

}

