package cn.pf.orch.meiye.domain.hazard.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class CompanyRangeUserQuery {

    @ApiModelProperty("公司id")
    @NotNull(message = "公司id不能为空")
    private Long companyId;

    @ApiModelProperty("是否递归查询,true会查询子公司下用户")
    private Boolean recursion;

    @ApiModelProperty("是否查询外部用户,true会返回外部用户")
    private Boolean outUser;

    @ApiModelProperty("用户名模糊查询")
    private String name;
}
