package cn.pf.orch.meiye.assembler;

import cn.pf.orch.meiye.domain.emergencyrescuecheck.command.AddProblemCommand;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.command.UpdateProblemCommand;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.dto.CheckTemplateDTO;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.po.MeiyeCompanyProblemRecode;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.po.MeiyeInspectionTemplate;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.query.DetailQuery;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.query.EmergencyRescueCheckInfoQuery;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface EmergencyRescueCheckAssembler {


    CheckTemplateDTO checkTemplateTODTO(MeiyeInspectionTemplate checkTemplate);
    List<CheckTemplateDTO> checkTemplateTODTO(List<MeiyeInspectionTemplate> checkTemplate);


    MeiyeCompanyProblemRecode problemAddCommandToPO(AddProblemCommand command);
    MeiyeCompanyProblemRecode problemUpdateCommandToPO(UpdateProblemCommand command);


    EmergencyRescueCheckInfoQuery detailQueryToQuery(DetailQuery query);


}
