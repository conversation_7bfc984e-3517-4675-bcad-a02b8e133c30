package cn.pf.orch.meiye.interfaces;

import cn.pf.orch.meiye.config.CurrentUserHolder;
import cn.pf.orch.meiye.domain.safetygenerationstandard.command.AddRecordCommand;
import cn.pf.orch.meiye.domain.safetygenerationstandard.command.ScoredSystemDetailCommand;
import cn.pf.orch.meiye.domain.safetygenerationstandard.command.UpdateRecordCommand;
import cn.pf.orch.meiye.domain.safetygenerationstandard.dto.ScoringRecordsDTO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.dto.ScoringSystemCorrelationDTO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.dto.ScoringSystemDetailCorrelationDTO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.query.ScoredSystemDetailQuery;
import cn.pf.orch.meiye.domain.safetygenerationstandard.query.ScoringRecordQuery;
import cn.pf.orch.meiye.domain.safetygenerationstandard.query.ScoringSystemQuery;
import cn.pf.orch.meiye.service.safetygenerationstandard.ScoringDetailCorrelationService;
import cn.pf.orch.meiye.service.safetygenerationstandard.ScoringRecordsService;
import cn.pf.orch.meiye.service.safetygenerationstandard.ScoringSystemCorrelationService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;

@RestController
@Api(tags = "安全生产标准化")
@RequestMapping("safetyGenerationStandard")
public class SafetyGenerationStandardController {

    @Resource
    private ScoringRecordsService scoringRecordsService;
    @Resource
    private ScoringSystemCorrelationService scoringSystemCorrelationService;
    @Resource
    private ScoringDetailCorrelationService scoringDetailCorrelationService;

    /**
     *  生产安全标准化页面
     * @param query
     * @return
     */

    @ApiOperation("分页查询安全生产标准化列表")
    @PostMapping("/scoringRecordsPage")
    public Page<ScoringRecordsDTO> page(@RequestBody ScoringRecordQuery query) {
        List<Long> rangeCompanyIds = CurrentUserHolder.getRangeCompanyIds();
        query.setRangeCompanyIds(rangeCompanyIds);
        return scoringRecordsService.page(query);
    }

    @ApiOperation("新增煤矿安全生产标准化信息")
    @PostMapping("/addScoringRecords")
    public void addScoredRecord(@RequestBody @Validated AddRecordCommand command) {
        scoringRecordsService.add(command);
    }

    @ApiOperation("编辑煤矿安全生产标准化信息")
    @PostMapping("/updateScoringRecords")
    public void updateScoringRecords(@RequestBody @Validated UpdateRecordCommand command) {
        scoringRecordsService.update(command);
    }

    // 删除
    @ApiOperation("删除")
    @GetMapping("/delete")
    public void deleteScoringRecords(@RequestParam @NotNull Long id) {
        scoringRecordsService.delete(id);
    }


    /**
     * 管理项评分页面
     */
    @ApiOperation("查询矿井评分体系")
    @PostMapping("/queryScoredSystem")
    public List<ScoringSystemCorrelationDTO> queryScoredSystem(@RequestBody @Validated ScoringSystemQuery query) {
        return scoringSystemCorrelationService.queryScoredSystem(query);
    }


    @ApiOperation("查看")
    @PostMapping("/queryScoredSystemDetail")
    public List<ScoringSystemDetailCorrelationDTO> queryScoredSystemDetail(@RequestBody @Validated ScoredSystemDetailQuery query) {
        return scoringDetailCorrelationService.queryScoringSystemDetailCorrelationList(query);
    }


    @ApiOperation("评分")
    @PostMapping("scoring")
    public void scoring(@RequestBody @Validated List<ScoredSystemDetailCommand> scoreds) {
        scoringDetailCorrelationService.scoring(scoreds);
    }


    @ApiOperation("详情")
    @GetMapping("detail")
    public ScoringRecordsDTO detail(@NotNull Long id) {
        return scoringRecordsService.findById(id);
    }
}
