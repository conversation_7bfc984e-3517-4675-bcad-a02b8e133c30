package cn.pf.orch.meiye.domain.system.dto;

import cn.pf.orch.meiye.domain.system.po.MyResourcePO;
import cn.pf.orch.meiye.enums.AuthTypeEnum;
import cn.pf.orch.meiye.enums.ResourceTypeEnum;
import cn.pf.orch.meiye.enums.StatusBooleanEnum;
import cn.pf.orch.meiye.enums.StatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * UpmResourceTreeDTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MyResourceTreeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty("资源类型（1: 菜单，2：按钮）")
    private ResourceTypeEnum type;

    @ApiModelProperty("权限类型（集团，区域公司，煤矿）")
    private AuthTypeEnum authType;

    @ApiModelProperty("菜单、面包屑、多标签页显示的名称")
    private String title;

    @ApiModelProperty("排序")
    private Integer resourceSort;

    @ApiModelProperty("上级菜单")
    private Long pid;

    @ApiModelProperty("前端path")
    private String path;

    @ApiModelProperty("状态（1：启用；2：停用）")
    private StatusEnum status;

    @ApiModelProperty("按钮权限标识")
    private String auths;

    @ApiModelProperty("是否在菜单中显示（默认值：true）")
    private StatusBooleanEnum showLink;

    @ApiModelProperty("是否显示父级菜单（默认值：true）")
    private StatusBooleanEnum showParent;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty(value = "子菜单")
    private List<MyResourceTreeDTO> children;


    public MyResourceTreeDTO(MyResourcePO po) {
        this.id = po.getId();
        this.type = po.getType();
        this.authType = po.getAuthType();
        this.title = po.getTitle();
        this.resourceSort = po.getResourceSort();
        this.pid = po.getPid();
        this.path = po.getPath();
        this.status = po.getStatus();
        this.auths = po.getAuths();
        this.showLink = po.getShowLink();
        this.showParent = po.getShowParent();
        this.remark = po.getRemark();
    }

}

