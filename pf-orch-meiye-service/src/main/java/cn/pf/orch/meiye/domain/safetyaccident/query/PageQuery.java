package cn.pf.orch.meiye.domain.safetyaccident.query;

import cn.genn.core.model.page.PageSortQuery;
import cn.pf.orch.meiye.enums.AccidentCategoryEnum;
import cn.pf.orch.meiye.enums.AccidentNature;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel(value = "PageQuery", description = "事故台账分页查询")
public class PageQuery extends PageSortQuery {

    @ApiModelProperty(value = "煤矿ID", example = "1001", required = true)
    private Integer companyId;

    @ApiModelProperty(value = "事故性质", required = true)
    private AccidentNature accidentNature;

    @ApiModelProperty(value = "事故类别", required = true)
    private AccidentCategoryEnum accidentCategory;

    @ApiModelProperty(value = "事故名称", example = "井下瓦斯爆炸", required = true)
    private String accidentName;

    @ApiModelProperty(value = "事故责任人", example = "张三")
    private String responsiblePerson;

    @ApiModelProperty(value = "事故责任人工号", example = "EMP1001")
    private String responsiblePersonNumber;

    @ApiModelProperty(value = "责任人OpenID", example = "wx1234567890abcdef")
    private String responsiblePersonOpenId;

    @ApiModelProperty(value = "起始发生时间", example = "2023-01-01 00:00:00")
    private LocalDateTime occurrenceTimeStart;

    @ApiModelProperty(value = "结束发生时间", example = "2023-12-31 23:59:59")
    private LocalDateTime occurrenceTimeEnd;

    @ApiModelProperty(value = "起始上报时间", example = "2023-01-01 00:00:00")
    private LocalDateTime reportTimeStart;

    @ApiModelProperty(value = "结束上报时间", example = "2023-12-31 23:59:59")
    private LocalDateTime reportTimeEnd;

    private List<Long> rangeCompanyIds;




}
