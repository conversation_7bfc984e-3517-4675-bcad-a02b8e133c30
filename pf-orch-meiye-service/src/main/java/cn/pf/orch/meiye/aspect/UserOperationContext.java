package cn.pf.orch.meiye.aspect;

import java.util.HashMap;
import java.util.Map;

/**
 * 若入参、返回都无法满足，可放入上下文中，供切面获取赋值
 */
public class UserOperationContext {

    public static final ThreadLocal<Map<String, Object>> context = ThreadLocal.withInitial(HashMap::new);

    public static void put(String key, Object value) {
        context.get().put(key, value);
    }

    public static Object get(String key) {
        return context.get().get(key);
    }

    public static void clear() {
        context.get().clear();
    }

}
