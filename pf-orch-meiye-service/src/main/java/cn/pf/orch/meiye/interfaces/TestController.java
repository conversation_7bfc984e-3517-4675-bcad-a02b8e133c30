package cn.pf.orch.meiye.interfaces;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.pf.orch.feishu.FeishuAppClient;
import cn.pf.orch.feishu.model.ApprovalInstanceModel;
import cn.pf.orch.feishu.model.InstanceFormModel;
import cn.pf.orch.feishu.model.enums.InstanceFormTypeEnum;
import com.lark.oapi.service.approval.v4.model.GetApprovalRespBody;
import com.lark.oapi.service.approval.v4.model.NodeApprover;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
@Api(tags = "测试")
@RestController
@RequestMapping("/test")
public class TestController {

    @Resource
    private FeishuAppClient feishuAppClient;

    @PostMapping("/test1")
    public Object test1() {
        GetApprovalRespBody approval = feishuAppClient.getApprovalService().getApproval("41383010-832A-4C24-93CC-8179D286E39A");
        log.info("approval:{}", JsonUtils.toJson(approval));

        ApprovalInstanceModel model = new ApprovalInstanceModel();
        model.setOpenId("ou_903888ecea01ab59bf8916ddbaeb5ce7");
        model.setApprovalCode("41383010-832A-4C24-93CC-8179D286E39A");
        InstanceFormModel instanceFormModel = new InstanceFormModel();
        instanceFormModel.setType(InstanceFormTypeEnum.TEXTAREA);
        instanceFormModel.setRequired(true);
        instanceFormModel.setValue("aaa");

        List<NodeApprover> nodeApproverOpenIdList = new ArrayList<>();
        NodeApprover nodeApprover = new NodeApprover();
        nodeApprover.setKey("e9a420d9bc27f8cf6674dda2ceb39c96");
        nodeApprover.setValue(new String[]{"ou_f7972b035193d58a6cc106103a70a0e8"});
        nodeApproverOpenIdList.add(nodeApprover);
        NodeApprover nodeApprover2 = new NodeApprover();
        nodeApprover2.setKey("b01e824df2fa73dfd2e49b4eeca3c561");
        nodeApprover2.setValue(new String[]{"ou_01d7df22174f7ba223bfed4a406d7824"});
        nodeApproverOpenIdList.add(nodeApprover2);
        model.setFormList(Collections.singletonList(instanceFormModel));
        model.setNodeApproverOpenIdList(nodeApproverOpenIdList);
        String approvalInstance = feishuAppClient.getApprovalService().createApprovalInstance(model);
        log.info("approvalInstance:{}", approvalInstance);
        return null;
    }


}
