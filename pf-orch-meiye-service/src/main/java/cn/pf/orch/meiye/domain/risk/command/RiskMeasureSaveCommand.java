package cn.pf.orch.meiye.domain.risk.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class RiskMeasureSaveCommand {

    @ApiModelProperty(value = "风险id")
    @NotNull(message = "风险id不能为空")
    private Long riskId;

    @ApiModelProperty(value = "措施内容")
    @NotBlank(message = "措施内容不能为空")
    private String content;

}
