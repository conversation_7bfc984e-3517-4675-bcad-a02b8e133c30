package cn.pf.orch.meiye.domain.safetyaccident.command;

import cn.pf.orch.meiye.enums.AccidentCategoryEnum;
import cn.pf.orch.meiye.enums.AccidentNature;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ApiModel(value ="UpdateCommand", description = "更新事故台账")
public class UpdateCommand {

    @ApiModelProperty(value = "主键ID", required = true)
    @NotNull
    private Long Id;

    @ApiModelProperty(value = "煤矿ID", example = "1001", required = true)
    private Long companyId;

    @ApiModelProperty(value = "煤矿名称", example = "山西大同煤矿", required = true)
    private String companyName;

    @ApiModelProperty(value = "事故性质", required = true)
    private AccidentNature accidentNature;

    @ApiModelProperty(value = "事故类别", required = true)
    private AccidentCategoryEnum accidentCategory;

    @ApiModelProperty(value = "事故名称", example = "井下瓦斯爆炸", required = true)
    private String accidentName;

    @ApiModelProperty(value = "发生时间", example = "2023-05-15 14:30:00", required = true)
    private LocalDateTime occurrenceTime;

    @ApiModelProperty(value = "上报时间", example = "2023-05-15 15:00:00", required = true)
    private LocalDateTime reportTime;

//    @ApiModelProperty(value = "时间描述", example = "事故发生在早班交接期间")
//    @NotEmpty
//    private String timeDescription;

    @ApiModelProperty(value = "事故地点ID", example = "location-001", required = true)
    private Long locationId;

    @ApiModelProperty(value = "事故责任人", example = "张三")
    private String responsiblePerson;

    @ApiModelProperty(value = "事故责任人工号", example = "EMP1001")
//    @NotEmpty
    private String responsiblePersonNumber;

    @ApiModelProperty(value = "责任人openid", example = "wx1234567890abcdef")
    private String responsiblePersonOpenId;

    @ApiModelProperty(value = "死亡人数", example = "2")
    private Integer deathToll;

    @ApiModelProperty(value = "重伤人数", example = "3")
    private Integer seriousInjury;

    @ApiModelProperty(value = "轻伤人数", example = "5")
    private Integer minorInjury;

    @ApiModelProperty(value = "经济损失(元)", example = "500000.00")
    private BigDecimal economicLoss;

    //更新人id
    @ApiModelProperty(value = "当前登录人id")
    private String updateUserId;

    //更新人
    @ApiModelProperty(value = "当前登录人名称")
    private String updateUserName;
}
