package cn.pf.orch.meiye.service.safetygenerationstandard.impl;

import cn.genn.core.exception.BusinessException;
import cn.hutool.core.lang.Opt;
import cn.pf.orch.meiye.assembler.SafetyGenerationStandardAssembler;
import cn.pf.orch.meiye.domain.safetygenerationstandard.command.AddRecordCommand;
import cn.pf.orch.meiye.domain.safetygenerationstandard.command.InitCompanyScoredSystemCommand;
import cn.pf.orch.meiye.domain.safetygenerationstandard.command.ScoredUpdateCommand;
import cn.pf.orch.meiye.domain.safetygenerationstandard.command.UpdateRecordCommand;
import cn.pf.orch.meiye.domain.safetygenerationstandard.dto.ScoredStatusNumDTO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.dto.ScoringRecordsDTO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.dto.ScoringSystemCorrelationDTO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.dto.ScoringSystemDetailCorrelationDTO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.po.ScoringRecordsPO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.query.ScoredStatusNumQuery;
import cn.pf.orch.meiye.domain.safetygenerationstandard.query.ScoringRecordQuery;
import cn.pf.orch.meiye.domain.safetygenerationstandard.query.ScoringSystemQuery;
import cn.pf.orch.meiye.enums.DelEnum;
import cn.pf.orch.meiye.mapper.ScoringRecordsMapper;
import cn.pf.orch.meiye.service.safetygenerationstandard.ScoringDetailCorrelationService;
import cn.pf.orch.meiye.service.safetygenerationstandard.ScoringRecordsService;
import cn.pf.orch.meiye.service.safetygenerationstandard.ScoringSystemCorrelationService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 安全生产煤矿评分记录表(ScoringRecords)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-22 10:29:37
 */
@Slf4j
@Service
public class ScoringRecordsServiceImpl extends ServiceImpl<ScoringRecordsMapper, ScoringRecordsPO> implements ScoringRecordsService {

    @Resource
    private ScoringRecordsMapper scoringRecordsMapper;
    @Resource
    private SafetyGenerationStandardAssembler assembler;

    private ScoringSystemCorrelationService scoringSystemCorrelationService;
    private ScoringDetailCorrelationService scoringDetailCorrelationService;
    @Autowired
    private void setService(@Lazy ScoringSystemCorrelationService ssc, @Lazy ScoringDetailCorrelationService sds) {
        this.scoringSystemCorrelationService = ssc;
        this.scoringDetailCorrelationService = sds;
    }


    @Override
    public Page<ScoringRecordsDTO> page(ScoringRecordQuery query) {
        LambdaQueryWrapper<ScoringRecordsPO> wrapper = Wrappers.lambdaQuery(ScoringRecordsPO.class)
                .eq(ObjectUtils.isNotNull(query.getCompanyId()), ScoringRecordsPO::getCompanyId, query.getCompanyId())
                .eq(ObjectUtils.isNotNull(query.getScoringLevel()), ScoringRecordsPO::getScoreLevel, query.getScoringLevel())
                .like(ObjectUtils.isNotNull(query.getCompanyName()), ScoringRecordsPO::getCompanyName, query.getCompanyName())
                .ge(ObjectUtils.isNotNull(query.getScoringDateStart()), ScoringRecordsPO::getCreateTime, query.getScoringDateStart())
                .le(ObjectUtils.isNotNull(query.getScoringDateEnd()), ScoringRecordsPO::getCreateTime, query.getScoringDateEnd())
                .eq(ScoringRecordsPO::getDel, DelEnum.NO_DELETE)
                .in(ObjectUtils.isNotEmpty(query.getRangeCompanyIds()), ScoringRecordsPO::getCompanyId, query.getRangeCompanyIds())
                .orderByDesc(ScoringRecordsPO::getCreateTime);
        Page<ScoringRecordsPO> pagePO = scoringRecordsMapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), wrapper);
        Page<ScoringRecordsDTO> pageDTO = new Page<>(pagePO.getCurrent(), pagePO.getSize(), pagePO.getTotal());
        // 设置进度
        List<ScoringRecordsDTO> scoringRecordsDTOS = assembler.scoringRecordPOToDTOList(pagePO.getRecords());
        scoringRecordsDTOS.forEach(scoringRecordsDTO -> {
            ScoredStatusNumDTO scoredStatusNumDTO = scoringDetailCorrelationService.queryScoredStatusNum(new ScoredStatusNumQuery(scoringRecordsDTO.getId()));
            scoringRecordsDTO.setScoredNumber(scoredStatusNumDTO.getScoredNumber());
            scoringRecordsDTO.setTotalSize(scoredStatusNumDTO.getTotal());
        });
        pageDTO.setRecords(scoringRecordsDTOS);
        return pageDTO;
    }

    @Override
    public ScoringRecordsDTO findById(Long id) {
        ScoringRecordsPO scoringRecordsPO = scoringRecordsMapper.selectById(id);
        return Optional.ofNullable(scoringRecordsPO)
                .filter(info -> Objects.equals(info.getDel(), DelEnum.NO_DELETE))
                .map(info -> {
                    ScoringRecordsDTO scoringRecordsDTO = assembler.scoringRecordPOToDTO(info);
                    ScoredStatusNumDTO scoredStatusNumDTO = scoringDetailCorrelationService.queryScoredStatusNum(new ScoredStatusNumQuery(scoringRecordsDTO.getId()));
                    scoringRecordsDTO.setScoredNumber(scoredStatusNumDTO.getScoredNumber());
                    scoringRecordsDTO.setTotalSize(scoredStatusNumDTO.getTotal());
                    return scoringRecordsDTO;
                })
                .orElse(null);
    }

    @Override
    public ScoringRecordsPO conditionBy(ScoringSystemQuery query) {
        LambdaQueryWrapper<ScoringRecordsPO> wrapper = Wrappers.lambdaQuery(ScoringRecordsPO.class)
                .eq(ScoringRecordsPO::getCompanyId, query.getCompanyId())
                .eq(ScoringRecordsPO::getScoreLevel, query.getScoringLevel())
                .eq(ScoringRecordsPO::getScoreDate, query.getScoringDate())
                .eq(ScoringRecordsPO::getDel, DelEnum.NO_DELETE);
        return scoringRecordsMapper.selectOne(wrapper);
    }


    @Transactional
    @Override
    public void add(AddRecordCommand command) {
        // 重复校验
        LocalDate now = LocalDate.now();
//        LocalDate firstDay;
//        LocalDate lastDay;
//        switch (command.getScoreLevel()) {
//            case COMPANY_LEVEL:
//                firstDay = now.with(TemporalAdjusters.firstDayOfMonth());
//                lastDay = now.with(TemporalAdjusters.lastDayOfMonth());
//                break;
//            case PROVINCE_LEVEL:
//                int quarter = (now.getMonthValue() - 1) / 3 + 1;
//                firstDay = LocalDate.of(now.getYear(), (quarter - 1) * 3 + 1, 1);
//                lastDay = firstDay.plusMonths(2);
//                break;
//            case COUNTRIES_LEVEL:
//                firstDay = now.with(TemporalAdjusters.firstDayOfYear());
//                lastDay = now.with(TemporalAdjusters.lastDayOfYear());
//                break;
//            default:
//                throw new BusinessException("评分级别设置异常！");
//        }
        LambdaQueryWrapper<ScoringRecordsPO> wrapper = Wrappers.lambdaQuery(ScoringRecordsPO.class)
                .eq(ScoringRecordsPO::getCompanyId, command.getCompanyId())
                .eq(ScoringRecordsPO::getScoreLevel, command.getScoreLevel())
                .eq(ScoringRecordsPO::getScoreDate, now);
        List<ScoringRecordsPO> repeat = scoringRecordsMapper.selectList(wrapper);
        if(ObjectUtils.isNotEmpty(repeat)) {
            throw new BusinessException("已有评分记录，请勿重复添加！");
        }
        ScoringRecordsPO scoringRecordsPO = assembler.addCommandToPO(command);
        scoringRecordsMapper.insert(scoringRecordsPO);
        InitCompanyScoredSystemCommand initCompanyScoredSystemCommand = new InitCompanyScoredSystemCommand(scoringRecordsPO.getId());
        log.info("add scored record and initCompanyScoredSystem :{}", initCompanyScoredSystemCommand);
        scoringSystemCorrelationService.initCompanyScoredSystem(initCompanyScoredSystemCommand);
    }

    @Override
    public void update(UpdateRecordCommand command) {
        ScoringRecordsPO scoringRecordsPO = scoringRecordsMapper.selectById(command.getId());
        Optional.ofNullable(scoringRecordsPO).orElseThrow(() -> new BusinessException("该条记录为空，无法修改"));
        scoringRecordsPO.setScoreLevel(command.getScoreLevel());
        scoringRecordsPO.setScoreDate(command.getScoreDate());
        scoringRecordsMapper.updateById(scoringRecordsPO);
    }

    @Override
    public void scoredSystem(ScoredUpdateCommand command) {
        // 计算管理项评分得出安全记录的评分
        ScoringRecordsPO scoringRecordsPO = scoringRecordsMapper.selectById(command.getScoredRecordId());
        List<ScoringSystemCorrelationDTO> scoringSystemCorrelationDTOS = scoringSystemCorrelationService.queryScoredSystem(new ScoringSystemQuery(scoringRecordsPO.getCompanyId(), scoringRecordsPO.getScoreLevel(), scoringRecordsPO.getScoreDate()));
        // 计算评分
        scoringRecordsPO.setTotalScore(calculateScored(scoringSystemCorrelationDTOS));
        scoringRecordsMapper.updateById(scoringRecordsPO);
    }

    @Transactional
    @Override
    public void delete(Long id) {
        ScoringRecordsPO scoringRecordsPO = scoringRecordsMapper.selectById(id);
        Optional.ofNullable(scoringRecordsPO).orElseThrow(() -> new RuntimeException("未找见评分信息，删除失败"));
        scoringRecordsPO.setDel(DelEnum.DELETE);
        scoringRecordsMapper.updateById(scoringRecordsPO);
        // 级联删除关联表信息
        scoringSystemCorrelationService.deleteByScoringRecordId(id);
        scoringDetailCorrelationService.deleteByScoringRecordId(id);
    }

    // 分数计算
    private BigDecimal calculateScored(List<ScoringSystemCorrelationDTO> products) {
        BigDecimal sum = BigDecimal.ZERO;
        for (ScoringSystemCorrelationDTO dto : products) {
            BigDecimal productTotal = dto.getActualScore().multiply(dto.getWeight());
            sum = sum.add(productTotal);
        }
        return sum;
    }
}

