package cn.pf.orch.meiye.mapper;

import cn.pf.orch.meiye.domain.risk.po.MyAddressRelPO;
import cn.pf.orch.meiye.enums.ApprovalBizTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MyAddressRelMapper extends BaseMapper<MyAddressRelPO> {


    default void deleteByBiz(ApprovalBizTypeEnum bizType, List<Long> bizIds) {
        LambdaQueryWrapper<MyAddressRelPO> wrapper = Wrappers.lambdaQuery(MyAddressRelPO.class)
                .eq(MyAddressRelPO::getBizType, bizType)
                .in(MyAddressRelPO::getBizId, bizIds);
        delete(wrapper);
    }
}
