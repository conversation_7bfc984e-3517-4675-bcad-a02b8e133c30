package cn.pf.orch.meiye.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum ViolationTypeEnum {

    GENERAL("1", "较重三违"),

    TYPICAL("2", "严重三违"),

    IRREGULAR("3", "典型三维"),
    ;

    @EnumValue
    @JsonValue
    private final String code;
    private final String description;


    private static final Map<String, ViolationTypeEnum> VALUES = new HashMap<>();
    static {
        for (final ViolationTypeEnum item : ViolationTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static ViolationTypeEnum of(String code) {
        return VALUES.get(code);
    }
}
