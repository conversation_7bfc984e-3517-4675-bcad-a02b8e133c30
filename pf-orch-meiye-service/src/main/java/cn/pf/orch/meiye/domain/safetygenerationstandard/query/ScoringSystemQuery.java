package cn.pf.orch.meiye.domain.safetygenerationstandard.query;

import cn.pf.orch.meiye.enums.ScoringLevelEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "查询矿井评分体系")
public class ScoringSystemQuery {

    @ApiModelProperty(value = "煤矿Id")
    @NotNull
    private Long companyId;

    @ApiModelProperty(value = "评分等级")
    @NotNull
    private ScoringLevelEnum scoringLevel;

    @ApiModelProperty(value = "评分日期")
    @NotNull
    private LocalDate scoringDate;

}
