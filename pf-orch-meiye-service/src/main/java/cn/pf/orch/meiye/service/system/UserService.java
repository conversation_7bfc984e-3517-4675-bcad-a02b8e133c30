package cn.pf.orch.meiye.service.system;

import cn.genn.core.model.page.PageResultDTO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.pf.orch.meiye.assembler.UserAssembler;
import cn.pf.orch.meiye.config.CurrentUserHolder;
import cn.pf.orch.meiye.domain.hazard.query.CompanyRangeUserQuery;
import cn.pf.orch.meiye.domain.system.command.MyUserApprovalSignCommand;
import cn.pf.orch.meiye.domain.system.command.MyUserRoleRelationCommand;
import cn.pf.orch.meiye.domain.system.command.MyUserSaveCommand;
import cn.pf.orch.meiye.domain.system.command.MyUserUpdateCommand;
import cn.pf.orch.meiye.domain.system.dto.MyUserDTO;
import cn.pf.orch.meiye.domain.system.dto.MyUserRoleRelDTO;
import cn.pf.orch.meiye.domain.system.po.MyCompanyPO;
import cn.pf.orch.meiye.domain.system.po.MyUserPO;
import cn.pf.orch.meiye.domain.system.po.MyUserRoleRelPO;
import cn.pf.orch.meiye.domain.system.query.MyUserPageQuery;
import cn.pf.orch.meiye.enums.AuthTypeEnum;
import cn.pf.orch.meiye.enums.RoleTypeEnum;
import cn.pf.orch.meiye.enums.UserTypeEnum;
import cn.pf.orch.meiye.mapper.MyCompanyMapper;
import cn.pf.orch.meiye.mapper.MyRoleMapper;
import cn.pf.orch.meiye.mapper.MyUserMapper;
import cn.pf.orch.meiye.mapper.MyUserRoleRelMapper;
import cn.pf.orch.meiye.service.SsoService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserService {

    @Resource
    private MyUserMapper myUserMapper;
    @Resource
    private UserAssembler assembler;
    @Resource
    private MyCompanyMapper myCompanyMapper;
    @Resource
    private MyRoleMapper myRoleMapper;
    @Resource
    private MyUserRoleRelMapper myUserRoleRelMapper;
    @Resource
    private SsoService ssoService;
    @Resource
    private CompanyService companyService;

    public PageResultDTO<MyUserDTO> page(MyUserPageQuery query) {
        List<Long> companyIds = CurrentUserHolder.getRangeCompanyIds();
        if (StrUtil.isNotBlank(query.getCompanyName())) {
            List<MyCompanyPO> myCompanyPOS = myCompanyMapper.selectByLikeName(query.getCompanyName());
            if (CollUtil.isEmpty(myCompanyPOS)) {
                return PageResultDTO.empty(query.getPageNo(), query.getPageSize());
            }
            companyIds = myCompanyPOS.stream().map(MyCompanyPO::getId).filter(companyIds::contains).collect(Collectors.toList());
        }
        PageResultDTO<MyUserDTO> userPageDTO = assembler.toPage(myUserMapper.queryList(new Page<>(query.getPageNo(), query.getPageSize()), query, companyIds));
        if (CollUtil.isNotEmpty(userPageDTO.getList())) {
            // 查询关联角色
            List<String> userIds = userPageDTO.getList().stream().map(MyUserDTO::getId).distinct().collect(Collectors.toList());
            List<MyUserRoleRelDTO> relDTOAlls = myRoleMapper.selectByUserIds(userIds);
            if(CollUtil.isNotEmpty(relDTOAlls)){
                Map<String, List<MyUserRoleRelDTO>> roleRelMap = relDTOAlls.stream().collect(Collectors.groupingBy(MyUserRoleRelDTO::getUserId));
                for (MyUserDTO myUserDTO : userPageDTO.getList()) {
                    List<MyUserRoleRelDTO> relDTOs = roleRelMap.get(myUserDTO.getId());
                    if(CollUtil.isNotEmpty(relDTOs)){
                        myUserDTO.setRoleRel(relDTOs);
                        myUserDTO.setRoleNames(relDTOs.stream().map(MyUserRoleRelDTO::getRoleName).collect(Collectors.joining(",")));
                        for (MyUserRoleRelDTO relDTO : relDTOs) {
                            if (RoleTypeEnum.SYSTEM.equals(relDTO.getRoleType())) {
                                myUserDTO.setSystemRole(relDTO);
                            }
                            if (RoleTypeEnum.CUSTOMIZE.equals(relDTO.getRoleType())) {
                                myUserDTO.getCustomizeRole().add(relDTO);
                            }
                        }
                    }
                }
            }

        }
        return userPageDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean relatedRole(MyUserRoleRelationCommand command) {
        // 清除用户角色关联
        myUserRoleRelMapper.deleteByUserIdAndCustomize(command.getUserId());
        // 新增关联
        if (CollUtil.isNotEmpty(command.getRoleIdList())) {
            List<MyUserRoleRelPO> poList = command.getRoleIdList().stream().map(roleId ->
                    MyUserRoleRelPO.builder().userId(command.getUserId()).roleId(roleId).roleType(RoleTypeEnum.CUSTOMIZE).build()
            ).collect(Collectors.toList());
            myUserRoleRelMapper.saveBatch(poList);
        }
        // 清除对应用户缓存
        ssoService.logoutByOpenId(Collections.singletonList(command.getUserId()));
        return true;
    }

    public Boolean relatedApproval(MyUserApprovalSignCommand command) {
        myUserMapper.updateApprovalByIds(Collections.singletonList(command.getUserId()), command.getApprovalSign());
        return true;
    }

    public Boolean save(MyUserSaveCommand command) {
        MyUserPO po = MyUserPO.builder()
                .id(IdUtil.fastSimpleUUID())
                .mobile(command.getMobile())
                .companyId(command.getCompanyId())
                .name(command.getName())
                .authType(AuthTypeEnum.COLLIERY)
                .type(UserTypeEnum.OUT)
                .build();
        myUserMapper.insert(po);
        return true;
    }

    public Boolean change(MyUserUpdateCommand command) {
        MyUserPO po = MyUserPO.builder()
                .id(command.getId())
                .mobile(StrUtil.isNotBlank(command.getMobile()) ? command.getMobile() : null)
                .companyId(ObjUtil.isNotEmpty(command.getCompanyId()) ? command.getCompanyId() : null)
                .name(StrUtil.isNotBlank(command.getName()) ? command.getName() : null)
                .build();
        myUserMapper.updateById(po);
        return true;
    }

    public Boolean batchRemove(List<String> idList) {
        myUserMapper.deleteBatchIds(idList);
        return true;
    }

    public List<MyUserDTO> selectByCompanyId(CompanyRangeUserQuery query) {
        List<MyCompanyPO> companyPOs = new ArrayList<>();
        if (query.getRecursion()) {
            companyPOs = companyService.getRangeCompanyPO(query.getCompanyId());
        } else{
            companyPOs.add(myCompanyMapper.selectById(query.getCompanyId()));
        }
        List<Long> companyIds = companyPOs.stream().map(MyCompanyPO::getId).collect(Collectors.toList());
        Map<Long, MyCompanyPO> companyPOMap = companyPOs.stream().collect(Collectors.toMap(MyCompanyPO::getId, Function.identity()));
        List<MyUserDTO> myUserDTOS = assembler.PO2DTO(myUserMapper.selectByCompanyIds(companyIds, query.getOutUser() ? null : UserTypeEnum.IN,  query.getName()));
        for (MyUserDTO myUserDTO : myUserDTOS) {
            myUserDTO.setCompanyName(companyPOMap.get(myUserDTO.getCompanyId()).getName());
        }
        return myUserDTOS;
    }
}