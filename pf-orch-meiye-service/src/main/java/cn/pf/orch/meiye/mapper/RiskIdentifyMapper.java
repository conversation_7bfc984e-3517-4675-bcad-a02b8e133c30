package cn.pf.orch.meiye.mapper;

import cn.pf.orch.meiye.domain.risk.dto.RiskIdentifyDTO;
import cn.pf.orch.meiye.domain.risk.po.RiskIdentifyPO;
import cn.pf.orch.meiye.domain.risk.query.RiskIdentifyQuery;
import cn.pf.orch.meiye.enums.IdentifyStatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface RiskIdentifyMapper extends BaseMapper<RiskIdentifyPO> {

    IPage<RiskIdentifyDTO> selectByPage(IPage<RiskIdentifyPO> page, @Param("query") RiskIdentifyQuery query);


    default RiskIdentifyPO selectByName(String name) {
        LambdaQueryWrapper<RiskIdentifyPO> wrapper = Wrappers.lambdaQuery(RiskIdentifyPO.class)
                .eq(RiskIdentifyPO::getName, name)
                .last("limit 1");
        return selectOne(wrapper);
    }

    default List<RiskIdentifyPO> selectByNoEndStatus(){
        LambdaQueryWrapper<RiskIdentifyPO> wrapper = Wrappers.lambdaQuery(RiskIdentifyPO.class)
                .notIn(RiskIdentifyPO::getStatus, IdentifyStatusEnum.FIVE);
        return selectList(wrapper);
    }

    default List<RiskIdentifyPO> selectByCompanyIds(List<Long> companyIds){
        LambdaQueryWrapper<RiskIdentifyPO> wrapper = Wrappers.lambdaQuery(RiskIdentifyPO.class)
                .gt(RiskIdentifyPO::getEndTime, LocalDateTime.now())
                .in(RiskIdentifyPO::getCompanyId, companyIds);
        return selectList(wrapper);
    }
}
