package cn.pf.orch.meiye.assembler;

import cn.pf.orch.meiye.domain.safetyviolation.command.AddCommand;
import cn.pf.orch.meiye.domain.safetyviolation.command.UpdateCommand;
import cn.pf.orch.meiye.domain.safetyviolation.dto.MeiyeSafetyViolationDTO;
import cn.pf.orch.meiye.domain.safetyviolation.po.MeiyeSafetyViolation;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SafetyViolationAssembler {

    MeiyeSafetyViolation addCommandToPO(AddCommand command);

    MeiyeSafetyViolation updateCommandToPO(UpdateCommand command);

    MeiyeSafetyViolationDTO poToDto(MeiyeSafetyViolation po);
}
