package cn.pf.orch.meiye.handler.strategy;

import cn.pf.orch.meiye.domain.feishu.FeishuApprovalEventCommand;
import cn.pf.orch.meiye.domain.risk.po.MyApprovalPO;
import cn.pf.orch.meiye.domain.risk.po.RiskInfoPO;
import cn.pf.orch.meiye.enums.RiskStatusEnum;
import cn.pf.orch.meiye.enums.feishu.ApprovalEventTypeEnum;
import cn.pf.orch.meiye.enums.feishu.FeishuInstanceStatusEnum;
import cn.pf.orch.meiye.mapper.MyApprovalMapper;
import cn.pf.orch.meiye.mapper.RiskInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 飞书-合同审批订阅
 */
@Slf4j
@Component
public class RiskInstanceStrategy extends ApprovalEventStrategy {

    @Resource
    private RiskInfoMapper riskInfoMapper;
    @Resource
    private MyApprovalMapper myApprovalMapper;

    @Override
    public String getApprovalCode() {
        return ApprovalEventTypeEnum.RISK_INSTANCE.getCode();
    }

    @Override
    public boolean approved(FeishuApprovalEventCommand event) {
        return this.handlerMethod(event,RiskStatusEnum.NO_CLOSE,FeishuInstanceStatusEnum.APPROVED);
    }

    @Override
    public boolean rejected(FeishuApprovalEventCommand event) {
        return this.handlerMethod(event,RiskStatusEnum.REJECT,FeishuInstanceStatusEnum.REJECTED);
    }

    @Override
    public boolean canceled(FeishuApprovalEventCommand event) {
        return this.handlerMethod(event,RiskStatusEnum.REJECT,FeishuInstanceStatusEnum.REJECTED);
    }

    @Override
    public boolean deleted(FeishuApprovalEventCommand event) {
        return this.handlerMethod(event,RiskStatusEnum.REJECT,FeishuInstanceStatusEnum.REJECTED);
    }

    private boolean handlerMethod(FeishuApprovalEventCommand event,RiskStatusEnum riskStatus,FeishuInstanceStatusEnum instanceStatus) {
        RiskInfoPO po = riskInfoMapper.selectByApprovalId(event.getInstanceCode());
        if (po == null) {
            return true;
        }
        riskInfoMapper.updateById(RiskInfoPO.builder().id(po.getId()).status(riskStatus).build());
        myApprovalMapper.updateById(MyApprovalPO.builder().id(event.getInstanceCode())
                .status(instanceStatus)
                .operateTime(event.getOperateTime()).build());
        return true;
    }


}
