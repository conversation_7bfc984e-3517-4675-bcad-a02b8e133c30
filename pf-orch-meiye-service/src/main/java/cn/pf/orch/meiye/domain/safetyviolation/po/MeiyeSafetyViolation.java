package cn.pf.orch.meiye.domain.safetyviolation.po;

import java.time.LocalDate;
import java.time.LocalDateTime;

import cn.pf.orch.meiye.enums.DelEnum;
import cn.pf.orch.meiye.enums.DiscovererUnitsEnum;
import cn.pf.orch.meiye.enums.DiscoveryMethodEnum;
import cn.pf.orch.meiye.enums.ViolationTypeEnum;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 三违记录表(MeiyeSafetyViolation)表实体类
 *
 * <AUTHOR>
 * @since 2025-04-14 10:27:37
 */
@Data
public class MeiyeSafetyViolation extends Model<MeiyeSafetyViolation> {
    //主键ID
    private Long id;
    //煤矿ID
     
    private Long companyId;
    // 煤矿名称
     
    private String companyName;
    //违章经过及原因
     
    private String violationDetails;
    //三违性质
     
    private ViolationTypeEnum violationType;
    //被查出人名称
     
    private String personName;
    //被查出人单位
     
    private String personDepartment;
    //被查出人工号
    @TableField(updateStrategy =  FieldStrategy.ALWAYS)
    private String personJobNumber;
    //被查出人openId

    private String personJobOpenid;
    //班别
     
    private String personShift;
    // 班次
     
    private String classes;
    //被查出人职务
     
    private String personPosition;
    //违章日期
     
    private LocalDate violationDate;
    //违章地点ID
     
    private Long violationLocationId;
    // 详细地点
    @TableField(updateStrategy =  FieldStrategy.ALWAYS)
    private String detailLocationName;
    //报送日期
     
    private LocalDate reportDate;
    //处理措施
     
    private String punishment;
    //处罚条款
     
    private String clause;
    //罚款金额(元)
     
    private Double fine;
    //查出方式
     
    private DiscoveryMethodEnum discoveryMethod;
    //查出人
     
    private String discoverer;
    //查处人工号
     
    private String discovererNumber;
    //查处人openid
     
    private String discovererOpenId;
    //查处人部门名称
     
    private String discovererDepartment;
    //查处人部门id
     
    private String discovererDepartmentId;
    // 是否删除
     
    private DelEnum del;

    // 查出人单位类别
    private DiscovererUnitsEnum discovererUnits;
    //创建人id
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;
    //创建人
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;
    //创建时间
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    //更新人id
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;
    //更新人
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;
    //更新时间
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}

