package cn.pf.orch.meiye.utils;

import cn.hutool.core.util.StrUtil;
import cn.pf.orch.feishu.properties.FeishuProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@Component
public class FsNotifyUrlUtils {

    private static final String PC_URL = "https://applink.feishu.cn/client/web_app/open?appId={1}&lk_target_url={2}";

    private static FeishuProperties feishuProperties;

    @Autowired
    public void setFeishuProperties(FeishuProperties feishuProperties) {
        FsNotifyUrlUtils.feishuProperties = feishuProperties;
    }

    /**
     * 飞书卡片pc端跳转,从飞书应用打开
     * @param pcUrl
     * @return
     */
    public static String getPCUrl(String pcUrl){
        String encodedURL = "";
        try {
            encodedURL = URLEncoder.encode(pcUrl, StandardCharsets.UTF_8.toString());
        } catch (Exception e) {
            throw new RuntimeException("飞书pcUrl编码失败!",e);
        }
        String appId = feishuProperties.getAppId();
        if(StrUtil.isBlank(appId)){
            throw new RuntimeException("飞书appId不能为空");
        }
        return PC_URL.replace("{1}", appId).replace("{2}", encodedURL);
    }
}
