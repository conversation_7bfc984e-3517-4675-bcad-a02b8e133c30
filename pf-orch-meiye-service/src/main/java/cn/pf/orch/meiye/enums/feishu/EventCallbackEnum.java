package cn.pf.orch.meiye.enums.feishu;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 事件回调枚举
 * @date 2024-12-25
 */
@Getter
@AllArgsConstructor
public enum EventCallbackEnum {

    APPROVAL_INSTANCE("approval_instance","审批实例状态变更"),
    APPROVAL_TASK("approval_task","审批任务状态变更 "),
    USER_UPDATE_V3("contact.user.updated_v3","员工信息变化"),
    // USER_CREATE_V3("contact.user.created_v3","员工入职"),   //入职时部门等信息不准确,无须处理
    // USER_DELETE_V3("contact.user.deleted_v3","员工离职"),   //离职时飞书都登不上,无须处理
    ;

    private final String eventType;

    private final String desc;

    private static final Map<String, EventCallbackEnum> VALUES = new HashMap<>();
    static {
        for (final EventCallbackEnum item : EventCallbackEnum.values()) {
            VALUES.put(item.getEventType(), item);
        }
    }

    public static EventCallbackEnum of(final String value) {
        return VALUES.get(value);
    }
}
