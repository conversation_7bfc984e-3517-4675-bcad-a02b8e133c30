package cn.pf.orch.meiye.properties;

import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
public class SsoAuthProperties {

    // @Value("${ssoauth.enable}")
    private boolean enable;
    //权限校验默认白名单路径
    private List<String> defaultUris = new ArrayList<>(Arrays.asList("/sso/**", "/health/**", "/white/**"));
    //鉴权白名单
    private List<String> excludePatterns = new ArrayList<>();

}
