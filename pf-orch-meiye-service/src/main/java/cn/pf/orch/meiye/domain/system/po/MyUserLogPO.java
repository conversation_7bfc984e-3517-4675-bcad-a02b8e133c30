package cn.pf.orch.meiye.domain.system.po;

import cn.pf.orch.meiye.enums.OperateTypeEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * MyUserLogPO对象
 *
 * <AUTHOR>
 * @desc 
 */
@Data
@Accessors(chain = true)
@TableName(value = "my_user_log", autoResultMap = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MyUserLogPO {

    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 公司id
     */
    @TableField("company_id")
    private Long companyId;

    /**
     * 操作类型（1查2增3改4删）
     */
    @TableField("operate_type")
    private OperateTypeEnum operateType;

    /**
     * 操作详情
     */
    @TableField("detail")
    private String detail;

    /**
     * 请求
     */
    @TableField("request")
    private String request;

    /**
     * 响应
     */
    @TableField("result")
    private String result;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人员openId
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;

    /**
     * 人员名称
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

}

