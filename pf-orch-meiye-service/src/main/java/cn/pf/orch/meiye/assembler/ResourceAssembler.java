package cn.pf.orch.meiye.assembler;

import cn.genn.core.model.assembler.QueryAssembler;
import cn.pf.orch.meiye.domain.system.dto.MyResourceDTO;
import cn.pf.orch.meiye.domain.system.dto.MyResourceTreeDTO;
import cn.pf.orch.meiye.domain.system.po.MyResourcePO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ResourceAssembler extends QueryAssembler<Object, MyResourcePO, MyResourceDTO> {

    default List<MyResourceTreeDTO> buildTree(List<MyResourcePO> resourceList) {
        // 获取顶层资源
        List<MyResourcePO> topLevelResources = resourceList.stream()
                .filter(resource -> resource.getPid() == null || resource.getPid() == 0)
                .sorted(Comparator.comparing(MyResourcePO::getResourceSort))
                .collect(Collectors.toList());

        // 递归生成树结构
        return topLevelResources.stream()
                .map(resource -> buildTree(resource, resourceList))
                .collect(Collectors.toList());
    }

    default MyResourceTreeDTO buildTree(MyResourcePO resource, List<MyResourcePO> resourceList) {
        MyResourceTreeDTO dto = new MyResourceTreeDTO(resource);

        // 获取子资源
        List<MyResourcePO> children = resourceList.stream()
                .filter(child -> resource.getId().equals(child.getPid()))
                .sorted(Comparator.comparing(MyResourcePO::getResourceSort))
                .collect(Collectors.toList());

        // 递归生成子资源树
        dto.setChildren(children.stream()
                .map(child -> buildTree(child, resourceList))
                .collect(Collectors.toList()));

        return dto;
    }
}
