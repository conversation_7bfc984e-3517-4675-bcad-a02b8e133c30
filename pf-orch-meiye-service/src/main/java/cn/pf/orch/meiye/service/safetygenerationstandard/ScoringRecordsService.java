package cn.pf.orch.meiye.service.safetygenerationstandard;

import cn.pf.orch.meiye.domain.safetygenerationstandard.command.AddRecordCommand;
import cn.pf.orch.meiye.domain.safetygenerationstandard.command.ScoredUpdateCommand;
import cn.pf.orch.meiye.domain.safetygenerationstandard.command.UpdateRecordCommand;
import cn.pf.orch.meiye.domain.safetygenerationstandard.dto.ScoringRecordsDTO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.dto.ScoringSystemCorrelationDTO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.po.ScoringRecordsPO;
import cn.pf.orch.meiye.domain.safetygenerationstandard.query.ScoringRecordQuery;
import cn.pf.orch.meiye.domain.safetygenerationstandard.query.ScoringSystemQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 安全生产煤矿评分记录表(ScoringRecords)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-22 10:29:37
 */
public interface ScoringRecordsService extends IService<ScoringRecordsPO> {


    /**
     * 安全生产煤矿评分记录 分页查询
     * @param query
     * @return
     */
    Page<ScoringRecordsDTO> page(ScoringRecordQuery query);


    /**
     * 主键查询
     * @param id
     * @return
     */
    ScoringRecordsDTO findById(Long id);


    /**
     * 条件查询单条
     * @param query
     * @return
     */
    ScoringRecordsPO conditionBy(ScoringSystemQuery query);


    /**
     * 新增记录
     * @param command
     */
    void add(AddRecordCommand command);


    /**
     * 修改记录
     * @param command
     */
    void update(UpdateRecordCommand command);


    /**
     * 更新评分
     */
    void scoredSystem(ScoredUpdateCommand command);

    /**
     * 删除评分
     * @param id
     */
    void delete(Long id);
}

