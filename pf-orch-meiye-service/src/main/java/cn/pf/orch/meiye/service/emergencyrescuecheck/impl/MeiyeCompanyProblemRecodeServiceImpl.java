package cn.pf.orch.meiye.service.emergencyrescuecheck.impl;

import cn.pf.orch.meiye.assembler.EmergencyRescueCheckAssembler;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.command.AddProblemCommand;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.command.UpdateProblemCommand;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.po.MeiyeCompanyProblemRecode;
import cn.pf.orch.meiye.enums.DelEnum;
import cn.pf.orch.meiye.mapper.MeiyeCompanyProblemRecodeMapper;
import cn.pf.orch.meiye.service.emergencyrescuecheck.MeiyeCompanyProblemRecodeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * 应急救援检查问题描述表(MeiyeCompanyProblemRecode)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-17 15:54:47
 */
@Service
public class MeiyeCompanyProblemRecodeServiceImpl extends ServiceImpl<MeiyeCompanyProblemRecodeMapper, MeiyeCompanyProblemRecode> implements MeiyeCompanyProblemRecodeService {

    @Resource
    private MeiyeCompanyProblemRecodeMapper problemRecodeMapper;

    @Resource
    private EmergencyRescueCheckAssembler emergencyRescueCheckAssembler;


    @Override
    public void addProblem(AddProblemCommand addProblem) {
        MeiyeCompanyProblemRecode po = emergencyRescueCheckAssembler.problemAddCommandToPO(addProblem);
        problemRecodeMapper.insert(po);
    }

    @Override
    public void updateById(UpdateProblemCommand updateProblem) {
        MeiyeCompanyProblemRecode po = emergencyRescueCheckAssembler.problemUpdateCommandToPO(updateProblem);
        problemRecodeMapper.updateById(po);
    }

    @Override
    public void delete(Long problemId) {
        MeiyeCompanyProblemRecode meiyeCompanyProblemRecode = problemRecodeMapper.selectById(problemId);
        Optional.ofNullable(meiyeCompanyProblemRecode)
                .map(info -> {
                    info.setDel(DelEnum.DELETE);
                    return problemRecodeMapper.updateById(info);
                }).orElseThrow(() -> new RuntimeException("当前信息没有关联的问题"));
    }
}

