package cn.pf.orch.meiye.service.system;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.collection.CollUtil;
import cn.pf.orch.feishu.FeishuAppClient;
import cn.pf.orch.meiye.assembler.UserAssembler;
import cn.pf.orch.meiye.domain.system.po.MyCompanyPO;
import cn.pf.orch.meiye.domain.system.po.MyUserPO;
import cn.pf.orch.meiye.domain.system.po.MyUserRoleRelPO;
import cn.pf.orch.meiye.enums.RoleTypeEnum;
import cn.pf.orch.meiye.enums.UserTypeEnum;
import cn.pf.orch.meiye.mapper.MyRoleMapper;
import cn.pf.orch.meiye.mapper.MyUserMapper;
import cn.pf.orch.meiye.mapper.MyUserRoleRelMapper;
import cn.pf.orch.meiye.service.SsoService;
import cn.pf.orch.meiye.utils.AsyncTaskExecutor;
import com.lark.oapi.service.contact.v3.model.Department;
import com.lark.oapi.service.contact.v3.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class SyncFSContactService {

    @Resource
    private FeishuAppClient feishuAppClient;
    @Resource
    private UserAssembler userAssembler;
    @Resource
    private MyUserMapper myUserMapper;
    @Resource
    private MyUserRoleRelMapper myUserRoleRelMapper;
    @Resource
    private MyRoleMapper myRoleMapper;
    @Resource
    private SsoService ssoService;

    /**
     * 同步飞书部门下用户信息
     * 同步处理角色信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncDepartmentAndUser(List<String> departmentIds, MyCompanyPO companyPO) {
        // 获取部门信息,保证部门存在
        List<Department> departmentBatch = feishuAppClient.getContactService().getDepartmentBatch(departmentIds);
        Map<Boolean, List<String>> groupedDepartments = departmentBatch.stream().map(Department::getOpenDepartmentId)
                .collect(Collectors.partitioningBy(departmentIds::contains));
        if (CollUtil.isNotEmpty(groupedDepartments.get(false))) {
            log.warn("有不存在的部门:{}", JsonUtils.toJson(groupedDepartments.get(false)));
        }
        // 1.递归获取部门
        List<String> departmentIdAllList = this.getDepartments(groupedDepartments.get(true));
        // 2.获取以上所有部门的用户信息
        List<CompletableFuture<List<User>>> futures = departmentIdAllList.stream()
                .map(id -> CompletableFuture.supplyAsync(() -> {
                    List<User> userListByDepartment = feishuAppClient.getContactService().getUserListByDepartment(id);
                    return CollUtil.isNotEmpty(userListByDepartment) ? userListByDepartment : new ArrayList<User>();
                }, AsyncTaskExecutor.resourceExecutor).exceptionally((ex -> {
                    log.error("refreshOperators 异常: ", ex);
                    return null;
                }))).collect(Collectors.toList());
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join(); // 等待所有任务完成
        List<User> userList = futures.stream()
                .flatMap(future -> {
                    try {
                        return future.get().stream();
                    } catch (Exception e) {
                        log.error("获取部门用户列表失败", e);
                        return Stream.empty();
                    }
                }).distinct()
                .collect(Collectors.toList());
        // 3.增量处理数据
        List<MyUserPO> myUserPOs = myUserMapper.selectByCompanyId(companyPO.getId(), UserTypeEnum.IN);
        List<MyUserPO> newUserPOs = userAssembler.DTO2PO(userList);
        for (MyUserPO newUserPO : newUserPOs) {
            newUserPO.setAuthType(companyPO.getAuthType());
            newUserPO.setCompanyId(companyPO.getId());
        }
        List<MyUserPO> addUserList = newUserPOs;  // 增量新增的用户
        if (CollUtil.isNotEmpty(myUserPOs)) {
            // 3.1删除缺少的用户
            List<MyUserPO> missingInNewUserPOs = myUserPOs.stream()
                    .filter(myUserPO -> newUserPOs.stream()
                            .noneMatch(newUserPO -> newUserPO.getId().equals(myUserPO.getId())))
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(missingInNewUserPOs)) {
                // 删除过时用户和角色;
                List<String> deletedUserIds = missingInNewUserPOs.stream().map(MyUserPO::getId).collect(Collectors.toList());
                myUserMapper.deleteByIds(deletedUserIds);
                //登出删除的用户
                ssoService.logoutByOpenId(deletedUserIds);
                myUserRoleRelMapper.deleteByUserIds(deletedUserIds);
            }
            // 新增的用户
            addUserList = newUserPOs.stream()
                    .filter(newUserPO -> myUserPOs.stream()
                            .noneMatch(myUserPO -> myUserPO.getId().equals(newUserPO.getId())))
                    .collect(Collectors.toList());
        }
        // 3.2增量用户处理
        if (CollUtil.isEmpty(addUserList)) {
            return;
        }
        addUserList = addUserList.stream().distinct().collect(Collectors.toList());
        List<String> userIds = addUserList.stream().map(MyUserPO::getId).distinct().collect(Collectors.toList());
        List<MyUserPO> myUserPOS = myUserMapper.selectBatchIds(userIds);

        Long roleId = myRoleMapper.getSystemRoleId(companyPO.getAuthType());
        if(CollUtil.isNotEmpty(myUserPOS)){
            List<String> userIdList = myUserPOS.stream().map(MyUserPO::getId).collect(Collectors.toList());
            Map<Boolean, List<MyUserPO>> poMap = addUserList.stream()
                    .collect(Collectors.partitioningBy(user -> userIdList.contains(user.getId())));
            // 新增
            if (CollUtil.isNotEmpty(poMap.get(false))) {
                myUserMapper.insertBatch(poMap.get(false));
                List<MyUserRoleRelPO> myUserRoleRelPOs = poMap.get(false).stream()
                        .map(po -> MyUserRoleRelPO.builder().userId(po.getId()).roleId(roleId).roleType(RoleTypeEnum.SYSTEM).build())
                        .collect(Collectors.toList());
                myUserRoleRelMapper.saveBatch(myUserRoleRelPOs);
            }
            // 更新
            // ToDo:如果用户可能属于多个部门,则这里存在问题;
            if (CollUtil.isNotEmpty(poMap.get(true))) {
                for (MyUserPO myUserPO : poMap.get(true)) {
                    myUserMapper.updateById(myUserPO);
                    myUserRoleRelMapper.deleteByUserIds(Collections.singletonList(myUserPO.getId()));
                    myUserRoleRelMapper.insert(MyUserRoleRelPO.builder().userId(myUserPO.getId()).roleId(roleId).roleType(RoleTypeEnum.SYSTEM).build());
                }
                myUserMapper.updateApprovalByIds(poMap.get(true).stream().map(MyUserPO::getId).collect(Collectors.toList()), null);
            }
        } else {
            //新增
            myUserMapper.insertBatch(addUserList);
            List<MyUserRoleRelPO> myUserRoleRelPOs = addUserList.stream()
                    .map(po -> MyUserRoleRelPO.builder().userId(po.getId()).roleId(roleId).roleType(RoleTypeEnum.SYSTEM).build())
                    .collect(Collectors.toList());
            myUserRoleRelMapper.saveBatch(myUserRoleRelPOs);
        }
    }


    private List<String> getDepartments(List<String> departmentIds) {
        Set<String> departmentIdAlls = new HashSet<>(departmentIds);
        for (String departmentId : departmentIds) {
            List<Department> departmentChildren = feishuAppClient.getContactService().getDepartmentChildren(departmentId);
            if (CollUtil.isNotEmpty(departmentChildren)) {
                List<String> list = departmentChildren.stream().map(Department::getOpenDepartmentId).distinct().collect(Collectors.toList());
                departmentIdAlls.addAll(list);
                departmentIdAlls.addAll(getDepartments(list));
            }
        }
        return new ArrayList<>(departmentIdAlls);
    }

}
