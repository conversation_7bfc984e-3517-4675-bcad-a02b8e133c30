package cn.pf.orch.meiye.domain.hazard.query;

import cn.genn.core.model.page.PageSortQuery;
import cn.pf.orch.meiye.enums.IdentifyHazardDetailEnum;
import cn.pf.orch.meiye.enums.IdentifyStatusEnum;
import cn.pf.orch.meiye.enums.IdentifyTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * HazardIdentify查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class HazardIdentifyQuery extends PageSortQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "排查计划名称")
    private String name;

    @ApiModelProperty(value = "排查类别")
    private IdentifyTypeEnum identifyType;

    @ApiModelProperty(value = "类别详情")
    private IdentifyHazardDetailEnum identifyDetail;

    @ApiModelProperty(value = "排查状态（未开始,进行中,已结束）")
    private IdentifyStatusEnum status;

    @ApiModelProperty(value = "隐患责任人")
    private String identifyOwnerName;

    @ApiModelProperty(value = "排查开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "排查结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    private List<Long> companyIds;


}

