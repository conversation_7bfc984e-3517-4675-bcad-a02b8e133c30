package cn.pf.orch.meiye.service;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.pf.orch.feishu.model.callback.EventCallBackDTO;
import cn.pf.orch.feishu.model.callback.EventCallbackCommand;
import cn.pf.orch.meiye.common.CacheConstants;
import cn.pf.orch.meiye.enums.feishu.EventCallbackEnum;
import cn.pf.orch.meiye.handler.CallbackHandler;
import cn.pf.orch.meiye.handler.CallbackHandlerFactory;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
@Transactional
public class FeishuEventService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private CallbackHandlerFactory callbackHandlerFactory;

    public <T> EventCallBackDTO callback(EventCallbackCommand<T> command) {
        log.info("callback command:{}", JsonUtils.toJson(command));
        // 防重逻辑
        if (ObjUtil.isNotNull(command.getHeader()) && ObjUtil.isNotNull(command.getHeader().getEventId())) {
            String callbackKey = CacheConstants.getFsCallbackEvent(command.getHeader().getEventId());
            String str = stringRedisTemplate.opsForValue().get(callbackKey);
            if (StrUtil.isNotBlank(str)) {
                log.warn("重复回调事件,eventId:{}", command.getHeader().getEventId());
                return EventCallBackDTO.builder().build();
            }
            stringRedisTemplate.opsForValue().set(callbackKey, LocalDateTime.now().toString(), 5, TimeUnit.SECONDS);
        }
        EventCallbackEnum eventCallbackEnum = null;
        if (ObjUtil.isEmpty(command.getHeader())) {
            eventCallbackEnum = this.getType(command.getEvent());
        } else {
            eventCallbackEnum = EventCallbackEnum.of(command.getHeader().getEventType());
        }
        CallbackHandler<T> callbackHandler = callbackHandlerFactory.getCallbackHandler(eventCallbackEnum);
        if (Objects.isNull(callbackHandler)) {
            log.error("未找到对应的回调处理器,{}", JsonUtils.toJson(command));
            return EventCallBackDTO.builder().build();
        }
        callbackHandler.handle(command);
        return EventCallBackDTO.builder().challenge(command.getChallenge()).build();
    }


    private EventCallbackEnum getType(Object event) {
        try {
            // 将 JSON 字符串解析为 JsonObject
            JsonObject rootObject = JsonParser.parseString(JsonUtils.toJson(event)).getAsJsonObject();
            // 提取 event 节点中的 type 字段
            JsonElement typeElement = rootObject.get("type");
            if (typeElement != null && !typeElement.isJsonNull()) {
                return EventCallbackEnum.of(typeElement.getAsString());
            }
        } catch (Exception e) {
            log.error("未找到对应的回调处理器,{}", JsonUtils.toJson(event));
        }
        return null; // 如果未找到 type 字段，返回 null
    }

}
