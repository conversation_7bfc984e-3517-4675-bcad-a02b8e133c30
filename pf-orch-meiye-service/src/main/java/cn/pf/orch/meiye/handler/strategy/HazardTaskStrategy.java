package cn.pf.orch.meiye.handler.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.pf.orch.feishu.FeishuAppClient;
import cn.pf.orch.meiye.domain.feishu.FeishuApprovalEventCommand;
import cn.pf.orch.meiye.domain.risk.po.MyApprovalLogPO;
import cn.pf.orch.meiye.enums.feishu.ApprovalEventTypeEnum;
import cn.pf.orch.meiye.mapper.MyApprovalLogMapper;
import com.lark.oapi.service.approval.v4.model.GetInstanceRespBody;
import com.lark.oapi.service.approval.v4.model.InstanceTimeline;
import com.lark.oapi.service.contact.v3.model.GetUserRespBody;
import com.lark.oapi.service.contact.v3.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Optional;

@Slf4j
@Component
public class HazardTaskStrategy extends ApprovalEventStrategy {

    @Resource
    private MyApprovalLogMapper myApprovalLogMapper;
    @Resource
    private FeishuAppClient feishuAppClient;

    @Override
    public String getApprovalCode() {
        return ApprovalEventTypeEnum.HAZARD_TASK.getCode();
    }

    @Override
    public boolean approved(FeishuApprovalEventCommand event) {
        String openId = event.getOpenId();
        String userName = "";
        if (StrUtil.isNotBlank(openId)) {
            GetUserRespBody userInfo = feishuAppClient.getContactService().getUserInfo(openId);
            userName = Optional.of(userInfo).map(GetUserRespBody::getUser).map(User::getName).orElse("");
        }
        myApprovalLogMapper.insert(MyApprovalLogPO.builder()
                .approvalId(event.getInstanceCode())
                .comment(this.getApprovalComment(event.getInstanceCode(), event.getDefKey()))
                .status("审批通过")
                .openId(openId)
                .userName(userName)
                .createTime(event.getOperateTime())
                .build());
        return true;
    }

    @Override
    public boolean rejected(FeishuApprovalEventCommand event) {
        String openId = event.getOpenId();
        String userName = "";
        if (StrUtil.isNotBlank(openId)) {
            GetUserRespBody userInfo = feishuAppClient.getContactService().getUserInfo(openId);
            userName = Optional.of(userInfo).map(GetUserRespBody::getUser).map(User::getName).orElse("");
        }

        myApprovalLogMapper.insert(MyApprovalLogPO.builder()
                .approvalId(event.getInstanceCode())
                .comment(this.getApprovalComment(event.getInstanceCode(), event.getDefKey()))
                .status("审批驳回")
                .openId(openId)
                .userName(userName)
                .createTime(event.getOperateTime())
                .build());
        return true;
    }

    @Override
    public boolean transferred(FeishuApprovalEventCommand event){
        String openId = event.getOpenId();
        String userName = "";
        if (StrUtil.isNotBlank(openId)) {
            GetUserRespBody userInfo = feishuAppClient.getContactService().getUserInfo(openId);
            userName = Optional.of(userInfo).map(GetUserRespBody::getUser).map(User::getName).orElse("");
        }
        myApprovalLogMapper.insert(MyApprovalLogPO.builder()
                .approvalId(event.getInstanceCode())
                .status("审批已转交")
                .openId(openId)
                .userName(userName)
                .createTime(event.getOperateTime())
                .build());
        return true;
    }

    @Override
    public boolean rollback(FeishuApprovalEventCommand event){
        String openId = event.getOpenId();
        String userName = "";
        if (StrUtil.isNotBlank(openId)) {
            GetUserRespBody userInfo = feishuAppClient.getContactService().getUserInfo(openId);
            userName = Optional.of(userInfo).map(GetUserRespBody::getUser).map(User::getName).orElse("");
        }
        myApprovalLogMapper.insert(MyApprovalLogPO.builder()
                .approvalId(event.getInstanceCode())
                .status("审批节点回退")
                .openId(openId)
                .userName(userName)
                .createTime(event.getOperateTime())
                .build());
        return true;
    }

    /**
     * 获取指定节点的审批意见
     *
     * @param instanceCode
     * @param defKey
     * @return
     */
    private String getApprovalComment(String instanceCode, String defKey) {
        if (StrUtil.isBlank(instanceCode) || StrUtil.isBlank(defKey)) {
            return "";
        }
        GetInstanceRespBody getInstanceRespBody = feishuAppClient.getApprovalService().instanceInfo(instanceCode);
        if (ObjUtil.isNotNull(getInstanceRespBody)) {
            InstanceTimeline[] timeline = getInstanceRespBody.getTimeline();
            if (CollUtil.isNotEmpty(Arrays.asList(timeline))) {
                for (InstanceTimeline instanceTimeline : timeline) {
                    if (defKey.equals(instanceTimeline.getNodeKey())) {
                        return instanceTimeline.getComment();
                    }
                }
            }
        }
        return "";
    }

}
