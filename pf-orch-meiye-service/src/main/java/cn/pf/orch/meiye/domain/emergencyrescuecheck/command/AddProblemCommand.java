package cn.pf.orch.meiye.domain.emergencyrescuecheck.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
public class AddProblemCommand {

    @ApiModelProperty(value = "煤矿Id")
    @NotNull
    private Long companyId;

    @ApiModelProperty(value = "模板Id")
    @NotNull
    private Long emergencyRescueCheckTempId;

    @ApiModelProperty(value = "问题描述")
    @NotEmpty
    @Size(max = 200, message = "名称长度不能超过200个字符")
    private String description;

    @ApiModelProperty(value = "问题附件，多个附件key用英文逗号分隔")
    private String attachment;

}
