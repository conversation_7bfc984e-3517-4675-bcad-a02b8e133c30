package cn.pf.orch.meiye.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum RiskStatusEnum {

    WAIT_SUBMIT(1, "待提交"),
    WAIT_REVIEW(2,"待审核"),
    REJECT(3,"已驳回"),
    NO_CLOSE(4,"待关闭"),
    CLOSE(5,"已关闭"),
    ;

    @EnumValue
    @JsonValue
    private final int type;

    private final String description;

    private static final Map<Integer, RiskStatusEnum> VALUES = new HashMap<>();
    static {
        for (final RiskStatusEnum item : RiskStatusEnum.values()) {
            VALUES.put(item.getType(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static RiskStatusEnum of(int code) {
        return VALUES.get(code);
    }
}
