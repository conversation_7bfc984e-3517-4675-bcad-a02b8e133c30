package cn.pf.orch.meiye.domain.system.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class QualificationUpdateCommand {

    @ApiModelProperty(value = "部门id")
    @NotNull(message = "idd不能为空")
    private Long id;

    @ApiModelProperty(value = "资质")
    @NotBlank(message = "资质不能为空")
    private String qualifications;
}
