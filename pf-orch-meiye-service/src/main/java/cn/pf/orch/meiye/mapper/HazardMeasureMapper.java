package cn.pf.orch.meiye.mapper;

import cn.pf.orch.meiye.domain.hazard.po.HazardMeasurePO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface HazardMeasureMapper extends BaseMapper<HazardMeasurePO> {
    
    default List<HazardMeasurePO> selectByHazardId(Long hazardId){
        LambdaQueryWrapper<HazardMeasurePO> wrapper = Wrappers.lambdaQuery(HazardMeasurePO.class)
                .eq(HazardMeasurePO::getHazardId, hazardId)
                .orderByAsc(HazardMeasurePO::getCreateTime);
        return selectList(wrapper);
    }
}
