package cn.pf.orch.meiye.service.emergencyrescuecheck;

import cn.pf.orch.meiye.domain.emergencyrescuecheck.dto.CheckTemplateDTO;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.dto.EmergencyRescueCheckInfoDTO;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.dto.InspectionDropDownDTO;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.po.MeiyeInspectionTemplate;
import cn.pf.orch.meiye.domain.emergencyrescuecheck.query.EmergencyRescueCheckInfoQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * 检查项目模板表(MeiyeInspectionTemplate)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-15 11:29:44
 */
public interface MeiyeInspectionTemplateService extends IService<MeiyeInspectionTemplate> {


    Map<String, List<CheckTemplateDTO>> checkTemplates();

    Page<EmergencyRescueCheckInfoDTO> queryEmergencyCheckInfo(EmergencyRescueCheckInfoQuery query);

    List<InspectionDropDownDTO> dropDown();

}

