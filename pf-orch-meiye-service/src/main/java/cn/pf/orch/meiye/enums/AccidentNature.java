package cn.pf.orch.meiye.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 事故性质
 */
@Getter
@AllArgsConstructor
public enum AccidentNature {

    /** 特大事故 */
    MAJOR_DISASTER("1","特大事故"),

    /** 重大事故 */
    SERIOUS("2","重大事故"),

    /** 一般事故 */
    GENERAL("3","一般事故"),

    /** 轻微事故 */
    MINOR("4","轻微事故");


    @EnumValue
    @JsonValue
    private String code;
    private String desc;
}
