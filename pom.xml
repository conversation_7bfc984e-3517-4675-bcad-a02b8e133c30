<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.pf.app</groupId>
    <artifactId>pf-orch-meiye</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <module>pf-orch-meiye-service</module>
    </modules>

    <parent>
        <groupId>cn.genn.boot</groupId>
        <artifactId>genn-spring-boot-parent</artifactId>
        <version>1.0.0-RELEASE</version>
    </parent>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>
    </dependencies>

    <distributionManagement>
        <repository>
            <id>nexus-zj-releases</id>
            <name>Nexus Releases Repository</name>
            <url>http://200.20.5.5:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus-zj-snapshots</id>
            <name>Nexus Snapshots Repository</name>
            <url>http://200.20.5.5:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>nexus-zj-releases</id>
            <name>Nexus Releases Repository</name>
            <url>http://200.20.5.5:8081/repository/maven-releases/</url>
            <releases>
                <updatePolicy>always</updatePolicy>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <updatePolicy>always</updatePolicy>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>nexus-zj-snapshots</id>
            <name>Nexus Snapshots Repository</name>
            <url>http://200.20.5.5:8081/repository/maven-snapshots/</url>
            <releases>
                <updatePolicy>always</updatePolicy>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <updatePolicy>always</updatePolicy>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

</project>